2025-06-26 08:32:54,269 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_experiment_modes_comparison_20250626_083254_20250626_083254.log
2025-06-26 08:32:54,269 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 08:32:54,270 - __main__ - INFO - ================================================================================
2025-06-26 08:32:54,270 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:32:54,270 - __main__ - INFO - 实验名称: test_experiment_modes
2025-06-26 08:32:54,270 - __main__ - INFO - 数据集: KAT
2025-06-26 08:32:54,270 - __main__ - INFO - 总实验数: 2
2025-06-26 08:32:54,270 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 08:32:54,270 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 08:32:54,270 - __main__ - INFO - ================================================================================
2025-06-26 08:32:54,271 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_083254
2025-06-26 08:32:54,272 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_083254\configs\original_config.yaml
2025-06-26 08:32:54,272 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 08:32:54,272 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 08:32:54,272 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_length=1024
2025-06-26 08:32:54,272 - __main__ - INFO - ======================================================================
2025-06-26 08:32:54,272 - __main__ - INFO - 处理实验组: dataset=KAT_length=1024
2025-06-26 08:32:54,272 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 08:32:54,272 - __main__ - INFO - ======================================================================
2025-06-26 08:32:54,272 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 08:32:54,275 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:32:54,275 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:32:54,283 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:32:54,290 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:32:54,290 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:32:54,290 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:32:54,291 - common.data_loader - INFO - 实验模式: imbalanced
2025-06-26 08:32:54,291 - common.data_loader - INFO - 样本配置: 故障样本每类最多10个, 健康样本最多50个
2025-06-26 08:32:54,291 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:32:54,291 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 50 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 10 个
2025-06-26 08:32:54,292 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:32:54,298 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:32:54,311 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:32:54,312 - common.data_loader - INFO -   训练样本: 120
2025-06-26 08:32:54,312 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:32:54,312 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:32:54,312 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:32:54,317 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:32:54,318 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:32:54,318 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:32:54,318 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:32:54,556 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:32:54,556 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:32:54,557 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:32:54,560 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:32:55,787 - __main__ - ERROR - 实验失败: 'patience'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1750, in main
    results = run_comparison_experiments(args.config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1559, in run_comparison_experiments
    return run_experiments_with_diffusion_reuse(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1589, in run_experiments_with_diffusion_reuse
    diffusion_model_path = train_diffusion_model_once(base_config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1819, in train_diffusion_model_once
    diffusion_trainer = DiffusionTrainer(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 83, in __init__
    patience=early_stop_config['patience'],
KeyError: 'patience'
