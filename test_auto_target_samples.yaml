# 测试生成策略自动调整功能
# 验证target_samples_per_class = -1 的自动匹配功能

# ================================================================================
# 1. 数据集配置
# ================================================================================
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载配置
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    fault_samples:
      max_fault_samples_per_class: [3]  # 故障样本数量为3

    healthy_samples:
      max_healthy_samples: -1           # 自动匹配故障样本数量
      healthy_label: 0

# ================================================================================
# 2. 数据增强配置
# ================================================================================
augmentation:
  method: "CDDPM"
  num_generated_per_class: [5]          # 最终生成5个样本
  save_generated: true
  generate_fault_only: true

  classifier_healthy_samples:
    use_real_when_no_generated: true
    real_healthy_count: -1

  # 生成策略配置 - 测试自动调整
  generation_strategy:
    target_samples_per_class: -1       # 应该自动调整为3（与故障样本数量一致）
    initial_multiplier: 3.0             # 生成 3 * 3 = 9 个样本用于筛选，最终选择5个
    min_multiplier: 2.0
    max_multiplier: 5.0
    adaptive_generation: true

  # CDDPM参数
  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02
    unconditional_prob: 0.1
    guidance_scale: 1.0

# ================================================================================
# 3. 模型配置
# ================================================================================
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# ================================================================================
# 4. 训练配置
# ================================================================================
training:
  diffusion:
    epochs: 20                          # 快速测试
    batch_size: 8
    learning_rate: 0.0001
    weight_decay: 0.0001

    best_model_criteria:
      metric: "val_loss"
      mode: "min"
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

    scheduler:
      type: "cosine"
      T_max: 20
      eta_min: 0.00001

    early_stopping:
      enabled: false                    # 快速测试关闭早停
      patience: 10
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

  classifier:
    epochs: 15                          # 快速测试
    batch_size: 16
    learning_rate: 0.0001
    weight_decay: 0.01

    best_model_criteria:
      metric: "val_loss"
      mode: "min"

    scheduler:
      type: "cosine"
      T_max: 15
      eta_min: 0.00001

    early_stopping:
      enabled: false                    # 快速测试关闭早停
      patience: 8
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

# ================================================================================
# 5. 数据筛选配置 - 启用筛选来测试生成策略
# ================================================================================
data_screening:
  enabled: true                         # 启用数据筛选来测试生成策略
  screening_level: "basic"

  target_control:
    enabled: true
    strict_target: false
    fallback_strategy: "relax_thresholds"

  confidence_filter:
    enabled: true
    threshold: 0.3
    min_threshold: 0.1
    max_threshold: 0.8
    per_class: false
    adaptive: true

  influence_filter:
    enabled: false

  outlier_detection:
    enabled: false

  diversity_selection:
    enabled: false

# ================================================================================
# 6. 评估配置
# ================================================================================
evaluation:
  metrics:
    classification: ["accuracy", "precision", "recall", "f1_score"]
    generation: ["gan_train", "gan_test"]
  
  gan_evaluation:
    classifier_epochs: 100              # 快速测试
    batch_size: 64
    
  visualization:
    save_confusion_matrix: true
    save_tsne: true
    tsne_perplexity: 30
    tsne_n_iter: 1000
    save_sample_plots: true
    num_samples_to_plot: 10

# ================================================================================
# 7. 系统配置
# ================================================================================
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  optimization:
    use_amp: false
    compile_model: false
    channels_last: false
    benchmark: true

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_process_checkpoints: true
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 3

# ================================================================================
# 8. 实验配置
# ================================================================================
experiment:
  name: "test_auto_target_samples"
  description: "测试生成策略中target_samples_per_class的自动调整功能"
  tags: ["auto_target", "generation_strategy", "data_screening"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# ================================================================================
# 9. 性能模式配置
# ================================================================================
performance_profiles:
  standard:
    training:
      diffusion:
        batch_size: 8
        epochs: 20
      classifier:
        batch_size: 16
        epochs: 15
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  fast:
    training:
      diffusion:
        batch_size: 4
        epochs: 5
      classifier:
        batch_size: 8
        epochs: 5
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true
