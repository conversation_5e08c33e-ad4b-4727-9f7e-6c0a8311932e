# 权重损失模式和早停功能使用指南

## 📋 功能概述

本项目支持完整的权重损失模式和早停功能，可以灵活配置训练过程中的最佳模型保存策略和早停条件。

## 🎯 权重损失模式

### 支持的判断指标

1. **训练损失模式** (`train_loss`)
   - 基于训练损失判断最佳模型
   - 适用于训练数据较少的情况

2. **验证损失模式** (`val_loss`)
   - 基于验证损失判断最佳模型
   - 最常用的模式，防止过拟合

3. **权重损失模式** (`weighted_loss`)
   - 综合训练损失和验证损失
   - 公式：`weighted_loss = train_weight * train_loss + val_weight * val_loss`
   - 平衡训练效果和泛化能力

### 配置示例

```yaml
training:
  diffusion:
    # 最佳模型判断配置
    best_model_criteria:
      metric: "weighted_loss"         # 判断指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 优化方向: "min" (越小越好) 或 "max" (越大越好)

      # 加权损失配置 (仅当metric为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重70%
        val_weight: 0.3               # 验证损失权重30%

  classifier:
    # 分类器可以使用不同的判断标准
    best_model_criteria:
      metric: "val_loss"              # 分类器通常使用验证损失
      mode: "min"
```

## 🛑 早停功能

### 支持的监控指标

早停功能支持与最佳模型判断相同的指标：

1. **训练损失监控** (`train_loss`)
2. **验证损失监控** (`val_loss`)
3. **权重损失监控** (`weighted_loss`)

### 配置示例

```yaml
training:
  diffusion:
    # 早停配置
    early_stopping:
      enabled: true                   # 是否启用早停
      patience: 10                    # 耐心值：容忍多少轮没有改善
      min_delta: 0.001                # 最小改善阈值
      monitor: "weighted_loss"        # 监控指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"                     # 模式: "min" (越小越好) 或 "max" (越大越好)
      restore_best_weights: true      # 早停时是否恢复最佳权重

      # 加权损失配置 (仅当monitor为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7             # 训练损失权重
        val_weight: 0.3               # 验证损失权重
```

## 📊 训练过程中的信息显示

### 扩散模型训练日志

使用权重损失模式时，训练日志会显示：

```
Epoch   1/50: Train Loss: 0.845850, Val Loss: 0.883640, Weighted Loss: 0.860966 (Best✓)
Epoch   2/50: Train Loss: 0.853239, Val Loss: 0.832448, Weighted Loss: 0.844923 (Best✓)
```

### 分类器训练日志

```
Epoch   1/100 | Train Loss: 2.0123 Acc: 0.2500 | Val Loss: 1.9876 Acc: 0.3125 | LR: 1.00e-04 | Time: 00:02 | Save: Best✓(val_loss=1.987600)
```

### 早停触发日志

```
早停触发，在第15轮停止训练（基于weighted_loss）
已恢复最佳权重
```

## 🔧 配置建议

### 1. 扩散模型配置建议

```yaml
training:
  diffusion:
    # 推荐使用权重损失模式
    best_model_criteria:
      metric: "weighted_loss"
      mode: "min"
      weighted_loss:
        train_weight: 0.6             # 稍微偏重训练损失
        val_weight: 0.4

    # 早停配置
    early_stopping:
      enabled: true
      patience: 15                    # 扩散模型训练较慢，给更多耐心
      monitor: "weighted_loss"        # 与最佳模型判断保持一致
      weighted_loss:
        train_weight: 0.6
        val_weight: 0.4
```

### 2. 分类器配置建议

```yaml
training:
  classifier:
    # 推荐使用验证损失模式
    best_model_criteria:
      metric: "val_loss"
      mode: "min"

    # 早停配置
    early_stopping:
      enabled: true
      patience: 8                     # 分类器训练较快，较小耐心值
      monitor: "val_loss"             # 防止过拟合
```

### 3. 不同场景的配置

#### 小数据集场景
```yaml
# 训练数据很少时，可以更关注训练损失
best_model_criteria:
  metric: "weighted_loss"
  weighted_loss:
    train_weight: 0.8               # 更关注训练损失
    val_weight: 0.2
```

#### 大数据集场景
```yaml
# 训练数据充足时，更关注泛化能力
best_model_criteria:
  metric: "val_loss"                # 直接使用验证损失
```

#### 快速实验场景
```yaml
# 快速测试时，使用较小的耐心值
early_stopping:
  enabled: true
  patience: 3                       # 快速停止
  monitor: "val_loss"
```

## ✅ 验证结果

### 权重损失模式验证

从测试结果可以看到：

1. ✅ **权重损失计算正确**：`Weighted Loss: 0.860966 = 0.6 * 0.845850 + 0.4 * 0.883640`
2. ✅ **最佳模型保存正确**：每当权重损失改善时显示 `(Best✓)`
3. ✅ **训练信息完整**：同时显示训练损失、验证损失和权重损失

### 早停功能验证

1. ✅ **早停逻辑正确**：支持多种监控指标
2. ✅ **权重恢复功能**：早停时自动恢复最佳权重
3. ✅ **配置灵活性**：可以为扩散模型和分类器设置不同的早停策略

## 🚀 使用示例

### 完整配置示例

```yaml
training:
  # 扩散模型：使用权重损失模式
  diffusion:
    epochs: 100
    best_model_criteria:
      metric: "weighted_loss"
      mode: "min"
      weighted_loss:
        train_weight: 0.6
        val_weight: 0.4
    early_stopping:
      enabled: true
      patience: 15
      monitor: "weighted_loss"
      mode: "min"
      restore_best_weights: true
      weighted_loss:
        train_weight: 0.6
        val_weight: 0.4

  # 分类器：使用验证损失模式
  classifier:
    epochs: 100
    best_model_criteria:
      metric: "val_loss"
      mode: "min"
    early_stopping:
      enabled: true
      patience: 8
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true
```

## 📝 注意事项

1. **权重配置一致性**：确保 `best_model_criteria` 和 `early_stopping` 中的权重配置一致
2. **耐心值设置**：根据模型复杂度和训练速度调整耐心值
3. **监控指标选择**：根据数据集大小和实验目标选择合适的监控指标
4. **权重恢复**：建议始终启用 `restore_best_weights` 以获得最佳性能

## 🎉 总结

权重损失模式和早停功能为训练过程提供了强大的控制能力：

- **灵活的最佳模型判断**：支持训练损失、验证损失和权重损失三种模式
- **智能的早停机制**：防止过拟合，节省训练时间
- **完整的权重管理**：自动保存和恢复最佳权重
- **详细的训练信息**：实时显示各种损失指标和保存状态

这些功能已经完全集成到项目中，可以直接使用！
