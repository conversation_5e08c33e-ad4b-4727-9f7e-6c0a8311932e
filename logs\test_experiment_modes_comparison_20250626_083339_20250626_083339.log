2025-06-26 08:33:39,409 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_experiment_modes_comparison_20250626_083339_20250626_083339.log
2025-06-26 08:33:39,410 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-26 08:33:39,410 - __main__ - INFO - ================================================================================
2025-06-26 08:33:39,410 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:33:39,410 - __main__ - INFO - 实验名称: test_experiment_modes
2025-06-26 08:33:39,410 - __main__ - INFO - 数据集: KAT
2025-06-26 08:33:39,410 - __main__ - INFO - 总实验数: 2
2025-06-26 08:33:39,410 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-26 08:33:39,410 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-26 08:33:39,411 - __main__ - INFO - ================================================================================
2025-06-26 08:33:39,411 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_083339
2025-06-26 08:33:39,411 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_083339\configs\original_config.yaml
2025-06-26 08:33:39,411 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-26 08:33:39,411 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-26 08:33:39,411 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_length=1024
2025-06-26 08:33:39,413 - __main__ - INFO - ======================================================================
2025-06-26 08:33:39,413 - __main__ - INFO - 处理实验组: dataset=KAT_length=1024
2025-06-26 08:33:39,413 - __main__ - INFO - 该组包含 2 个实验
2025-06-26 08:33:39,413 - __main__ - INFO - ======================================================================
2025-06-26 08:33:39,413 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-26 08:33:39,414 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:33:39,414 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:33:39,439 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:33:39,461 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:33:39,462 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:33:39,462 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:33:39,462 - common.data_loader - INFO - 实验模式: imbalanced
2025-06-26 08:33:39,462 - common.data_loader - INFO - 样本配置: 故障样本每类最多10个, 健康样本最多50个
2025-06-26 08:33:39,462 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:33:39,462 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 50 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 10 个
2025-06-26 08:33:39,463 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:39,475 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:33:39,481 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:33:39,481 - common.data_loader - INFO -   训练样本: 120
2025-06-26 08:33:39,482 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:33:39,482 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:33:39,482 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:33:39,483 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:33:39,483 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:33:39,483 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:33:39,483 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:33:39,705 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:33:39,706 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:33:39,706 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:33:39,706 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:33:40,782 - __main__ - INFO - 扩散模型参数数量: 52,721,281
2025-06-26 08:33:40,782 - __main__ - INFO - 开始训练扩散模型...
2025-06-26 08:33:40,783 - __main__ - INFO - 最佳模型判断标准: val_loss (min)
2025-06-26 08:33:43,361 - __main__ - INFO - Epoch   1/1 | Train Loss: 0.823056 | Val Loss: 0.808375 | LR: 1.00e-05 | Time: 00:02 | Save: Best✓(val_loss=0.808375)
2025-06-26 08:33:43,362 - __main__ - INFO - 扩散模型训练完成，用时: 00:02
2025-06-26 08:33:43,362 - __main__ - INFO - 最佳验证损失: 0.808375
2025-06-26 08:33:43,362 - __main__ - INFO - ✅ 扩散模型训练完成，保存至: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:33:43,363 - __main__ - INFO - ============================================================
2025-06-26 08:33:43,363 - __main__ - INFO - 开始实验 1/2 (组内 1/2)
2025-06-26 08:33:43,363 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 30}
2025-06-26 08:33:43,363 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:33:43,363 - __main__ - INFO - ============================================================
2025-06-26 08:33:43,368 - __main__ - INFO - 缓存配置已保存: cache\20250626_083339\experiment_001_config.yaml
2025-06-26 08:33:43,370 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:33:43,370 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:33:43,372 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:33:43,375 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:33:43,375 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:33:43,375 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:33:43,376 - common.data_loader - INFO - 实验模式: imbalanced
2025-06-26 08:33:43,376 - common.data_loader - INFO - 样本配置: 故障样本每类最多10个, 健康样本最多50个
2025-06-26 08:33:43,376 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:33:43,376 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 50 个
2025-06-26 08:33:43,376 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,376 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,376 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,376 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,377 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,377 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 10 个
2025-06-26 08:33:43,377 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 10 个
2025-06-26 08:33:43,382 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:33:43,384 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:33:43,384 - common.data_loader - INFO -   训练样本: 120
2025-06-26 08:33:43,385 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:33:43,385 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:33:43,385 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:33:43,386 - __main__ - INFO - 训练基线分类器...
2025-06-26 08:33:43,414 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:33:43,415 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:33:43,731 - __main__ - INFO - Epoch   1/5 | Train Loss: 2.0377 Acc: 0.3690 | Val Loss: 2.0363 Acc: 0.3889 | LR: 9.14e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:33:43,893 - __main__ - INFO - Epoch   2/5 | Train Loss: 1.9383 Acc: 0.4286 | Val Loss: 1.9720 Acc: 0.3889 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:33:44,040 - __main__ - INFO - Epoch   3/5 | Train Loss: 1.8426 Acc: 0.4286 | Val Loss: 1.9443 Acc: 0.3889 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:33:44,192 - __main__ - INFO - Epoch   4/5 | Train Loss: 1.8043 Acc: 0.4286 | Val Loss: 1.9283 Acc: 0.3889 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:33:44,349 - __main__ - INFO - Epoch   5/5 | Train Loss: 1.7515 Acc: 0.4286 | Val Loss: 1.9122 Acc: 0.3889 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:33:44,350 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:33:44,350 - __main__ - INFO - 最佳验证准确率: 0.3889
2025-06-26 08:33:44,351 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:33:44,351 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:33:44,351 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:33:44,351 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:33:44,351 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:33:44,545 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:33:44,545 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:33:44,545 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:33:44,545 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:33:44,797 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:33:44,799 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 30 个...
2025-06-26 08:33:44,800 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:33:44,800 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:33:44,800 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:35:10,560 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:36:36,386 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:38:01,871 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:39:27,564 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:40:53,015 - __main__ - INFO - 生成类别 6 的样本...
