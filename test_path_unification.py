#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径统一测试脚本

验证检查点保存和加载路径是否统一
"""

import os
import yaml
import sys
from pathlib import Path

def test_config_paths():
    """测试配置文件中的路径设置"""
    print("=" * 60)
    print("测试配置文件路径设置")
    print("=" * 60)
    
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        save_config = config['system']['save']
        
        print("✅ 配置文件加载成功")
        print(f"checkpoints_dir: {save_config.get('checkpoints_dir')}")
        print(f"generated_samples_dir: {save_config.get('generated_samples_dir')}")
        
        # 检查生成样本路径格式
        gen_samples_dir = save_config.get('generated_samples_dir', '')
        if '{dataset_name}' in gen_samples_dir:
            print("✅ 生成样本路径支持数据集名称格式化")
            example_path = gen_samples_dir.format(dataset_name='KAT')
            print(f"  示例路径: {example_path}")
        else:
            print("❌ 生成样本路径不支持数据集名称格式化")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构创建"""
    print("\n" + "=" * 60)
    print("测试目录结构")
    print("=" * 60)
    
    # 预期的目录结构
    expected_dirs = [
        'checkpoints',
        'checkpoints/diffusion',
        'checkpoints/diffusion/best',
        'checkpoints/diffusion/process',
        'checkpoints/classifier',
        'checkpoints/classifier/best',
        'checkpoints/classifier/process',
        'checkpoints/augmentation',
        'generated_samples',
        'results',
        'logs'
    ]
    
    # 检查基础目录
    for dir_path in expected_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}")
        else:
            print(f"⚠️  {dir_path} (不存在，运行时会创建)")
    
    # 检查增强方法目录
    aug_methods = ['cddpm', 'cgan', 'wgan', 'wgan-gp', 'ddpm', 'dcgan', 'cvae']
    
    print("\n增强方法目录:")
    for method in aug_methods:
        method_dir = f'checkpoints/augmentation/{method}'
        if os.path.exists(method_dir):
            print(f"✅ {method_dir}")
        else:
            print(f"⚠️  {method_dir} (不存在，运行时会创建)")
    
    return True

def test_path_logic():
    """测试路径逻辑"""
    print("\n" + "=" * 60)
    print("测试路径逻辑")
    print("=" * 60)
    
    # 模拟配置
    config = {
        'system': {
            'save': {
                'checkpoints_dir': 'checkpoints',
                'generated_samples_dir': 'generated_samples/{dataset_name}'
            }
        },
        'augmentation': {
            'method': 'CDDPM'
        },
        'dataset': {
            'name': 'KAT'
        }
    }
    
    # 测试增强方法路径构建
    method_name = config['augmentation']['method']
    augmentation_path = os.path.join(
        config['system']['save']['checkpoints_dir'],
        'augmentation', method_name.lower(), 'best_model.pth'
    )
    
    print(f"增强方法模型路径: {augmentation_path}")
    
    # 测试扩散模型路径构建
    diffusion_path = os.path.join(
        config['system']['save']['checkpoints_dir'],
        'diffusion', 'best', 'best_model.pth'
    )
    
    print(f"扩散模型路径: {diffusion_path}")
    
    # 测试生成样本路径构建
    dataset_name = config['dataset']['name']
    gen_samples_dir = config['system']['save']['generated_samples_dir'].format(dataset_name=dataset_name)
    gen_samples_path = os.path.join(gen_samples_dir, method_name, 'generated_data.npy')
    
    print(f"生成样本路径: {gen_samples_path}")
    
    # 验证路径格式
    expected_patterns = [
        'checkpoints/augmentation/cddpm/best_model.pth',
        'checkpoints/diffusion/best/best_model.pth',
        'generated_samples/KAT/CDDPM/generated_data.npy'
    ]
    
    actual_paths = [
        augmentation_path.replace('\\', '/'),
        diffusion_path.replace('\\', '/'),
        gen_samples_path.replace('\\', '/')
    ]
    
    print("\n路径格式验证:")
    for expected, actual in zip(expected_patterns, actual_paths):
        if expected == actual:
            print(f"✅ {actual}")
        else:
            print(f"❌ 期望: {expected}")
            print(f"   实际: {actual}")
            return False
    
    return True

def test_code_consistency():
    """测试代码一致性"""
    print("\n" + "=" * 60)
    print("测试代码一致性")
    print("=" * 60)
    
    # 检查main.py中的路径逻辑
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # 检查是否使用了统一的路径逻辑
        checks = [
            ('augmentation', 'checkpoints/augmentation'),
            ('method_name.lower()', '方法名小写转换'),
            ('generated_samples_dir', '生成样本目录'),
            ('shutil.copy2', '模型复制逻辑')
        ]
        
        for keyword, description in checks:
            if keyword in main_content:
                print(f"✅ {description}: 找到 '{keyword}'")
            else:
                print(f"❌ {description}: 未找到 '{keyword}'")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 代码一致性检查失败: {e}")
        return False

def test_example_scenarios():
    """测试示例场景"""
    print("\n" + "=" * 60)
    print("测试示例场景")
    print("=" * 60)
    
    scenarios = [
        {
            'name': 'CDDPM + KAT数据集',
            'config': {
                'augmentation': {'method': 'CDDPM'},
                'dataset': {'name': 'KAT'}
            },
            'expected_paths': {
                'model': 'checkpoints/augmentation/cddpm/best_model.pth',
                'samples': 'generated_samples/KAT/CDDPM/generated_data.npy'
            }
        },
        {
            'name': 'CGAN + SK数据集',
            'config': {
                'augmentation': {'method': 'CGAN'},
                'dataset': {'name': 'SK'}
            },
            'expected_paths': {
                'model': 'checkpoints/augmentation/cgan/best_model.pth',
                'samples': 'generated_samples/SK/CGAN/generated_data.npy'
            }
        },
        {
            'name': 'WGAN-GP + JST数据集',
            'config': {
                'augmentation': {'method': 'WGAN-GP'},
                'dataset': {'name': 'JST'}
            },
            'expected_paths': {
                'model': 'checkpoints/augmentation/wgan-gp/best_model.pth',
                'samples': 'generated_samples/JST/WGAN-GP/generated_data.npy'
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        
        method = scenario['config']['augmentation']['method']
        dataset = scenario['config']['dataset']['name']
        
        # 构建实际路径
        actual_model_path = f"checkpoints/augmentation/{method.lower()}/best_model.pth"
        actual_samples_path = f"generated_samples/{dataset}/{method}/generated_data.npy"
        
        # 验证路径
        expected_model = scenario['expected_paths']['model']
        expected_samples = scenario['expected_paths']['samples']
        
        if actual_model_path == expected_model:
            print(f"  ✅ 模型路径: {actual_model_path}")
        else:
            print(f"  ❌ 模型路径不匹配")
            print(f"     期望: {expected_model}")
            print(f"     实际: {actual_model_path}")
            return False
        
        if actual_samples_path == expected_samples:
            print(f"  ✅ 样本路径: {actual_samples_path}")
        else:
            print(f"  ❌ 样本路径不匹配")
            print(f"     期望: {expected_samples}")
            print(f"     实际: {actual_samples_path}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("路径统一测试")
    print("=" * 60)
    
    tests = [
        ("配置文件路径", test_config_paths),
        ("目录结构", test_directory_structure),
        ("路径逻辑", test_path_logic),
        ("代码一致性", test_code_consistency),
        ("示例场景", test_example_scenarios)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出错: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！路径统一修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
