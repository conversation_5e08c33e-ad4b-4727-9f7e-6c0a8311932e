2025-06-25 11:38:01,940 - common.utils - INFO - 日志系统初始化完成，日志文件: logs/cddpm_fault_diagnosis_20250625_113801_20250625_113801.log
2025-06-25 11:38:01,940 - __main__ - INFO - ================================================================================
2025-06-25 11:38:01,940 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-25 11:38:01,940 - __main__ - INFO - 数据集: KAT
2025-06-25 11:38:01,940 - __main__ - INFO - ================================================================================
2025-06-25 11:38:01,940 - __main__ - INFO - 🚀 实验开始
2025-06-25 11:38:01,940 - __main__ - INFO - ================================================================================
2025-06-25 11:38:01,940 - __main__ - INFO - 当前实验配置
2025-06-25 11:38:01,940 - __main__ - INFO - ================================================================================
2025-06-25 11:38:01,940 - __main__ - INFO - 数据集: KAT
2025-06-25 11:38:01,940 - __main__ - INFO - 故障样本每类: [9]
2025-06-25 11:38:01,940 - __main__ - INFO - 健康样本总数: 1
2025-06-25 11:38:01,940 - __main__ - INFO - 信号长度: 1024
2025-06-25 11:38:01,940 - __main__ - INFO - 归一化方法: minmax
2025-06-25 11:38:01,940 - __main__ - INFO - 增强方法: CDDPM
2025-06-25 11:38:01,940 - __main__ - INFO - 每类生成样本数: [10]
2025-06-25 11:38:01,940 - __main__ - INFO - 只生成故障样本: True
2025-06-25 11:38:01,940 - __main__ - INFO - 扩散模型训练轮数: 20000
2025-06-25 11:38:01,940 - __main__ - INFO - 分类器训练轮数: 500
2025-06-25 11:38:01,940 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-25 11:38:01,940 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-25 11:38:01,940 - __main__ - INFO - 设备: cuda
2025-06-25 11:38:01,940 - __main__ - INFO - 性能模式: auto
2025-06-25 11:38:01,940 - __main__ - INFO - 随机种子: 42
2025-06-25 11:38:01,940 - __main__ - INFO - ================================================================================
2025-06-25 11:38:01,941 - __main__ - INFO - 使用设备: cuda
2025-06-25 11:38:01,942 - __main__ - INFO - 加载数据...
2025-06-25 11:38:01,942 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 11:38:01,942 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 11:38:01,946 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 11:38:01,957 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 11:38:01,957 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 11:38:01,957 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 11:38:01,957 - common.data_loader - INFO - 样本配置: 故障样本每类最多9个, 健康样本最多1个
2025-06-25 11:38:01,957 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 1 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 9 个
2025-06-25 11:38:01,958 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 11:38:01,963 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 11:38:01,966 - common.data_loader - INFO - 数据加载完成:
2025-06-25 11:38:01,966 - common.data_loader - INFO -   训练样本: 64
2025-06-25 11:38:01,966 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 11:38:01,966 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 11:38:01,966 - common.data_loader - INFO -   类别数: 8
2025-06-25 11:38:01,967 - __main__ - INFO - ==================================================
2025-06-25 11:38:01,967 - __main__ - INFO - 开始训练数据增强模型
2025-06-25 11:38:01,967 - __main__ - INFO - ==================================================
2025-06-25 11:38:02,393 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-25 11:38:02,393 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 11:38:02,393 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 11:38:02,393 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 11:38:02,393 - models.cddpm - INFO -   类别数量: 8
2025-06-25 11:38:02,678 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 11:38:02,678 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 11:38:02,678 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 11:38:02,678 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 11:38:03,303 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-25 11:38:03,303 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-25 11:38:03,303 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共20000轮
2025-06-25 11:38:04,777 - models.augmentation_factory - INFO - Epoch   1/20000: Train Loss: 0.841567, Val Loss: 0.830637 (Best✓)
2025-06-25 11:38:05,735 - models.augmentation_factory - INFO - Epoch   2/20000: Train Loss: 0.821918, Val Loss: 0.819751 (Best✓)
2025-06-25 11:38:06,686 - models.augmentation_factory - INFO - Epoch   3/20000: Train Loss: 0.813930, Val Loss: 0.807260 (Best✓)
2025-06-25 11:38:07,633 - models.augmentation_factory - INFO - Epoch   4/20000: Train Loss: 0.813868, Val Loss: 0.798138 (Best✓)
2025-06-25 11:38:10,502 - models.augmentation_factory - INFO - Epoch   7/20000: Train Loss: 0.799744, Val Loss: 0.794207 (Best✓)
2025-06-25 11:38:11,450 - models.augmentation_factory - INFO - Epoch   8/20000: Train Loss: 0.801195, Val Loss: 0.788526 (Best✓)
2025-06-25 11:38:12,417 - models.augmentation_factory - INFO - Epoch   9/20000: Train Loss: 0.794231, Val Loss: 0.780800 (Best✓)
2025-06-25 11:38:13,369 - models.augmentation_factory - INFO - Epoch  10/20000: Train Loss: 0.780397, Val Loss: 0.766695 (Best✓)
2025-06-25 11:38:14,323 - models.augmentation_factory - INFO - Epoch  11/20000: Train Loss: 0.771096, Val Loss: 0.736160 (Best✓)
2025-06-25 11:38:15,272 - models.augmentation_factory - INFO - Epoch  12/20000: Train Loss: 0.729561, Val Loss: 0.692404 (Best✓)
2025-06-25 11:38:16,229 - models.augmentation_factory - INFO - Epoch  13/20000: Train Loss: 0.681686, Val Loss: 0.624225 (Best✓)
2025-06-25 11:38:17,202 - models.augmentation_factory - INFO - Epoch  14/20000: Train Loss: 0.632008, Val Loss: 0.589192 (Best✓)
2025-06-25 11:38:19,131 - models.augmentation_factory - INFO - Epoch  16/20000: Train Loss: 0.592539, Val Loss: 0.563481 (Best✓)
2025-06-25 11:38:20,080 - models.augmentation_factory - INFO - Epoch  17/20000: Train Loss: 0.573712, Val Loss: 0.503182 (Best✓)
2025-06-25 11:38:21,690 - models.augmentation_factory - INFO - Epoch  18/20000: Train Loss: 0.521595, Val Loss: 0.493614 (Best✓)
2025-06-25 11:38:22,670 - models.augmentation_factory - INFO - Epoch  19/20000: Train Loss: 0.477357, Val Loss: 0.429208 (Best✓)
2025-06-25 11:38:23,635 - models.augmentation_factory - INFO - Epoch  20/20000: Train Loss: 0.475569, Val Loss: 0.414618 (Best✓)
2025-06-25 11:38:24,590 - models.augmentation_factory - INFO - Epoch  21/20000: Train Loss: 0.446071, Val Loss: 0.394686 (Best✓)
2025-06-25 11:38:25,543 - models.augmentation_factory - INFO - Epoch  22/20000: Train Loss: 0.420226, Val Loss: 0.360358 (Best✓)
2025-06-25 11:38:27,470 - models.augmentation_factory - INFO - Epoch  24/20000: Train Loss: 0.371383, Val Loss: 0.330785 (Best✓)
2025-06-25 11:38:28,422 - models.augmentation_factory - INFO - Epoch  25/20000: Train Loss: 0.324772, Val Loss: 0.321868 (Best✓)
2025-06-25 11:38:29,363 - models.augmentation_factory - INFO - Epoch  26/20000: Train Loss: 0.352176, Val Loss: 0.255257 (Best✓)
2025-06-25 11:38:30,295 - models.augmentation_factory - INFO - Epoch  27/20000: Train Loss: 0.254558, Val Loss: 0.250814 (Best✓)
2025-06-25 11:38:31,246 - models.augmentation_factory - INFO - Epoch  28/20000: Train Loss: 0.307195, Val Loss: 0.238504 (Best✓)
2025-06-25 11:38:33,182 - models.augmentation_factory - INFO - Epoch  30/20000: Train Loss: 0.220029, Val Loss: 0.204278 (Best✓)
2025-06-25 11:38:34,135 - models.augmentation_factory - INFO - Epoch  31/20000: Train Loss: 0.203675, Val Loss: 0.155709 (Best✓)
2025-06-25 11:38:40,664 - models.augmentation_factory - INFO - Epoch  38/20000: Train Loss: 0.185672, Val Loss: 0.129486 (Best✓)
2025-06-25 11:38:48,274 - models.augmentation_factory - INFO - Epoch  46/20000: Train Loss: 0.191364, Val Loss: 0.111818 (Best✓)
2025-06-25 11:38:52,111 - models.augmentation_factory - INFO - Epoch  50/20000: Train Loss: 0.115840, Val Loss: 0.209001
2025-06-25 11:39:00,587 - models.augmentation_factory - INFO - Epoch  58/20000: Train Loss: 0.153507, Val Loss: 0.110301 (Best✓)
2025-06-25 11:39:11,914 - models.augmentation_factory - INFO - Epoch  70/20000: Train Loss: 0.202510, Val Loss: 0.108478 (Best✓)
2025-06-25 11:39:34,015 - models.augmentation_factory - INFO - Epoch  92/20000: Train Loss: 0.178028, Val Loss: 0.095676 (Best✓)
2025-06-25 11:39:42,007 - models.augmentation_factory - INFO - Epoch 100/20000: Train Loss: 0.155646, Val Loss: 0.187111
2025-06-25 11:39:42,981 - models.augmentation_factory - INFO - Epoch 101/20000: Train Loss: 0.197894, Val Loss: 0.088362 (Best✓)
2025-06-25 11:39:43,965 - models.augmentation_factory - INFO - Epoch 102/20000: Train Loss: 0.136465, Val Loss: 0.081969 (Best✓)
2025-06-25 11:40:33,963 - models.augmentation_factory - INFO - Epoch 150/20000: Train Loss: 0.113880, Val Loss: 0.158831
2025-06-25 11:40:46,289 - models.augmentation_factory - INFO - Epoch 162/20000: Train Loss: 0.147850, Val Loss: 0.078990 (Best✓)
2025-06-25 11:41:25,467 - models.augmentation_factory - INFO - Epoch 200/20000: Train Loss: 0.132601, Val Loss: 0.151940
2025-06-25 11:41:29,469 - models.augmentation_factory - INFO - Epoch 204/20000: Train Loss: 0.146132, Val Loss: 0.078055 (Best✓)
