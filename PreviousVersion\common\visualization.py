"""
可视化模块
包含各种数据可视化和结果展示功能
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import pandas as pd
from typing import List, Optional, Dict, Tuple
import os
import logging

logger = logging.getLogger(__name__)

# 设置中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    import platform
    import warnings

    # 临时抑制字体警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")

        try:
            if platform.system() == 'Linux':
                # Linux系统，直接使用英文标签避免字体问题
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                logger.info("Linux环境：使用英文标签以避免字体警告")

            else:
                # Windows/Mac系统，尝试中文字体
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                logger.info("Windows/Mac环境：启用中文字体支持")

        except Exception as e:
            logger.warning(f"字体设置失败: {e}")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

# 初始化字体设置
setup_chinese_font()

# 定义标签映射（Linux环境使用英文标签）
def get_label_text(chinese_text: str, english_text: str = None) -> str:
    """根据环境返回合适的标签文本"""
    import platform
    if platform.system() == 'Linux' and english_text:
        return english_text
    return chinese_text


class Visualizer:
    """可视化工具类"""
    
    def __init__(self, save_dir: str):
        """
        初始化可视化器
        
        Args:
            save_dir: 保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
    
    def plot_confusion_matrix(self, cm: np.ndarray, class_names: List[str], 
                            title: str = "混淆矩阵", save_name: str = "confusion_matrix.png"):
        """
        绘制混淆矩阵
        
        Args:
            cm: 混淆矩阵
            class_names: 类别名称列表
            title: 图表标题
            save_name: 保存文件名
        """
        plt.figure(figsize=(10, 8))
        
        # 计算百分比
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
        
        # 创建热力图
        sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names,
                   cbar_kws={'label': '百分比 (%)'})
        
        plt.title(title, fontsize=16)
        plt.xlabel(get_label_text('预测标签', 'Predicted Label'), fontsize=12)
        plt.ylabel(get_label_text('真实标签', 'True Label'), fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"混淆矩阵已保存: {save_path}")
    
    def plot_tsne(self, features: np.ndarray, labels: np.ndarray, 
                  class_names: List[str], title: str = "t-SNE可视化",
                  save_name: str = "tsne.png", perplexity: int = 30):
        """
        绘制t-SNE降维可视化
        
        Args:
            features: 特征数据
            labels: 标签数据
            class_names: 类别名称列表
            title: 图表标题
            save_name: 保存文件名
            perplexity: t-SNE困惑度参数
        """
        logger.info("开始t-SNE降维...")
        
        # 执行t-SNE降维
        tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42, 
                   n_iter=1000, verbose=1)
        features_2d = tsne.fit_transform(features)
        
        # 绘制散点图
        plt.figure(figsize=(12, 10))
        
        # 为每个类别分配颜色
        colors = plt.cm.tab10(np.linspace(0, 1, len(class_names)))
        
        for i, class_name in enumerate(class_names):
            mask = labels == i
            if np.any(mask):
                plt.scatter(features_2d[mask, 0], features_2d[mask, 1], 
                          c=[colors[i]], label=class_name, alpha=0.7, s=20)
        
        plt.title(title, fontsize=16)
        plt.xlabel('t-SNE 1', fontsize=12)
        plt.ylabel('t-SNE 2', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"t-SNE可视化已保存: {save_path}")
    
    def plot_training_curves(self, train_losses: List[float], val_losses: List[float],
                           train_accs: Optional[List[float]] = None, 
                           val_accs: Optional[List[float]] = None,
                           title: str = "训练曲线", save_name: str = "training_curves.png"):
        """
        绘制训练曲线
        
        Args:
            train_losses: 训练损失列表
            val_losses: 验证损失列表
            train_accs: 训练准确率列表
            val_accs: 验证准确率列表
            title: 图表标题
            save_name: 保存文件名
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # 绘制损失曲线
        epochs = range(1, len(train_losses) + 1)
        axes[0].plot(epochs, train_losses, 'b-', label=get_label_text('训练损失', 'Train Loss'))
        axes[0].plot(epochs, val_losses, 'r-', label=get_label_text('验证损失', 'Val Loss'))
        axes[0].set_title(get_label_text('损失曲线', 'Loss Curve'))
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 绘制准确率曲线
        if train_accs is not None and val_accs is not None:
            axes[1].plot(epochs, train_accs, 'b-', label=get_label_text('训练准确率', 'Train Acc'))
            axes[1].plot(epochs, val_accs, 'r-', label=get_label_text('验证准确率', 'Val Acc'))
            axes[1].set_title(get_label_text('准确率曲线', 'Accuracy Curve'))
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Accuracy')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
        else:
            axes[1].text(0.5, 0.5, get_label_text('无准确率数据', 'No Accuracy Data'),
                        ha='center', va='center', transform=axes[1].transAxes, fontsize=14)
            axes[1].set_title(get_label_text('准确率曲线', 'Accuracy Curve'))
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"训练曲线已保存: {save_path}")
    
    def plot_signal_samples(self, signals: np.ndarray, labels: np.ndarray,
                          class_names: List[str], num_samples: int = 10,
                          title: str = "信号样本", save_name: str = "signal_samples.png"):
        """
        绘制信号样本
        
        Args:
            signals: 信号数据
            labels: 标签数据
            class_names: 类别名称列表
            num_samples: 每个类别显示的样本数
            title: 图表标题
            save_name: 保存文件名
        """
        num_classes = len(class_names)
        fig, axes = plt.subplots(num_classes, num_samples, 
                               figsize=(num_samples * 3, num_classes * 2))
        
        if num_classes == 1:
            axes = axes.reshape(1, -1)
        if num_samples == 1:
            axes = axes.reshape(-1, 1)
        
        for class_idx, class_name in enumerate(class_names):
            # 获取该类别的样本
            class_mask = labels == class_idx
            class_signals = signals[class_mask]
            
            if len(class_signals) == 0:
                continue
            
            # 随机选择样本
            sample_indices = np.random.choice(len(class_signals), 
                                            min(num_samples, len(class_signals)), 
                                            replace=False)
            
            for sample_idx in range(num_samples):
                ax = axes[class_idx, sample_idx]
                
                if sample_idx < len(sample_indices):
                    signal = class_signals[sample_indices[sample_idx]]
                    ax.plot(signal)
                    ax.set_title(f'{class_name} - {get_label_text("样本", "Sample")}{sample_idx+1}', fontsize=10)
                else:
                    ax.text(0.5, 0.5, get_label_text('无数据', 'No Data'), ha='center', va='center',
                           transform=ax.transAxes)
                    ax.set_title(f'{class_name} - {get_label_text("样本", "Sample")}{sample_idx+1}', fontsize=10)

                ax.set_xlabel(get_label_text('时间点', 'Time'))
                ax.set_ylabel(get_label_text('振幅', 'Amplitude'))
                ax.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"信号样本图已保存: {save_path}")
    
    def plot_generation_comparison(self, real_signals: np.ndarray, 
                                 generated_signals: np.ndarray,
                                 class_names: List[str], num_samples: int = 5,
                                 save_name: str = "generation_comparison.png"):
        """
        绘制真实信号与生成信号的对比
        
        Args:
            real_signals: 真实信号
            generated_signals: 生成信号
            class_names: 类别名称列表
            num_samples: 每个类别显示的样本数
            save_name: 保存文件名
        """
        num_classes = len(class_names)
        fig, axes = plt.subplots(num_classes, num_samples * 2, 
                               figsize=(num_samples * 6, num_classes * 2))
        
        if num_classes == 1:
            axes = axes.reshape(1, -1)
        
        for class_idx, class_name in enumerate(class_names):
            for sample_idx in range(num_samples):
                # 真实信号
                real_ax = axes[class_idx, sample_idx * 2]
                if sample_idx < len(real_signals):
                    real_ax.plot(real_signals[sample_idx])
                    real_ax.set_title(f'{class_name} - 真实信号{sample_idx+1}', fontsize=10)
                else:
                    real_ax.text(0.5, 0.5, '无数据', ha='center', va='center', 
                               transform=real_ax.transAxes)
                real_ax.grid(True, alpha=0.3)
                
                # 生成信号
                gen_ax = axes[class_idx, sample_idx * 2 + 1]
                if sample_idx < len(generated_signals):
                    gen_ax.plot(generated_signals[sample_idx])
                    gen_ax.set_title(f'{class_name} - 生成信号{sample_idx+1}', fontsize=10)
                else:
                    gen_ax.text(0.5, 0.5, '无数据', ha='center', va='center', 
                              transform=gen_ax.transAxes)
                gen_ax.grid(True, alpha=0.3)
        
        plt.suptitle('真实信号 vs 生成信号对比', fontsize=16)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"生成对比图已保存: {save_path}")
    
    def plot_metrics_comparison(self, metrics_dict: Dict[str, Dict[str, float]],
                              title: str = "指标对比", save_name: str = "metrics_comparison.png"):
        """
        绘制指标对比图
        
        Args:
            metrics_dict: 指标字典，格式为 {method_name: {metric_name: value}}
            title: 图表标题
            save_name: 保存文件名
        """
        # 提取指标名称和方法名称
        methods = list(metrics_dict.keys())
        metric_names = list(next(iter(metrics_dict.values())).keys())
        
        # 创建数据矩阵
        data = []
        for method in methods:
            row = [metrics_dict[method].get(metric, 0) for metric in metric_names]
            data.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(data, index=methods, columns=metric_names)
        
        # 绘制热力图
        plt.figure(figsize=(12, 8))
        sns.heatmap(df, annot=True, fmt='.4f', cmap='YlOrRd', 
                   cbar_kws={'label': '指标值'})
        
        plt.title(title, fontsize=16)
        plt.xlabel('指标', fontsize=12)
        plt.ylabel('方法', fontsize=12)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"指标对比图已保存: {save_path}")
