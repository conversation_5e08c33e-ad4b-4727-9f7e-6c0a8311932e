2025-06-26 08:10:12,653 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250626_081012_20250626_081012.log
2025-06-26 08:10:12,654 - common.experiment_manager - INFO - 检测到训练数据参数变化，需要重新训练扩散模型
2025-06-26 08:10:12,654 - __main__ - INFO - ================================================================================
2025-06-26 08:10:12,654 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:10:12,654 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-26 08:10:12,654 - __main__ - INFO - 数据集: KAT
2025-06-26 08:10:12,654 - __main__ - INFO - 总实验数: 9
2025-06-26 08:10:12,654 - __main__ - INFO - 对比参数: ['dataset.data_loading.fault_samples.max_fault_samples_per_class', 'augmentation.num_generated_per_class']
2025-06-26 08:10:12,654 - __main__ - INFO - 🔥 扩散模型重用: 否
2025-06-26 08:10:12,654 - __main__ - INFO - ================================================================================
2025-06-26 08:10:12,654 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_081012
2025-06-26 08:10:12,655 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_081012\configs\original_config.yaml
2025-06-26 08:10:12,655 - __main__ - INFO - 🔧 使用传统独立实验模式
2025-06-26 08:10:12,656 - __main__ - INFO - ============================================================
2025-06-26 08:10:12,656 - __main__ - INFO - 开始实验 1/9
2025-06-26 08:10:12,656 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 10}
2025-06-26 08:10:12,656 - __main__ - INFO - ============================================================
2025-06-26 08:10:12,664 - __main__ - INFO - 缓存配置已保存: cache\20250626_081012\experiment_001_config.yaml
2025-06-26 08:10:12,664 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:10:12,665 - __main__ - INFO - ================================================================================
2025-06-26 08:10:12,665 - __main__ - INFO - 当前实验配置
2025-06-26 08:10:12,665 - __main__ - INFO - ================================================================================
2025-06-26 08:10:12,665 - __main__ - INFO - 数据集: KAT
2025-06-26 08:10:12,665 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:10:12,665 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:10:12,665 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:10:12,666 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:10:12,666 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:10:12,666 - __main__ - INFO - 每类生成样本数: 10
2025-06-26 08:10:12,666 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:10:12,666 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:10:12,666 - __main__ - INFO - 分类器训练轮数: 200
2025-06-26 08:10:12,666 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:10:12,666 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:10:12,666 - __main__ - INFO - 设备: cuda
2025-06-26 08:10:12,666 - __main__ - INFO - 性能模式: auto
2025-06-26 08:10:12,666 - __main__ - INFO - 随机种子: 42
2025-06-26 08:10:12,667 - __main__ - INFO - ================================================================================
2025-06-26 08:10:12,670 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:10:12,670 - __main__ - INFO - 加载数据...
2025-06-26 08:10:12,671 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:10:12,671 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:10:12,673 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:10:12,678 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:10:12,678 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:10:12,679 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:10:12,679 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 08:10:12,679 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,680 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,681 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 08:10:12,681 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:10:12,685 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:10:12,698 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:10:12,698 - common.data_loader - INFO -   训练样本: 24
2025-06-26 08:10:12,699 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:10:12,699 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:10:12,699 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:10:12,704 - __main__ - INFO - ==================================================
2025-06-26 08:10:12,704 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:10:12,705 - __main__ - INFO - ==================================================
2025-06-26 08:10:12,720 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:10:12,720 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:10:12,720 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:10:12,720 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:10:12,721 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:10:12,945 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:10:12,945 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:10:12,945 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:10:12,947 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:10:14,357 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:10:14,357 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:10:14,357 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:10:15,491 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.835237, Val Loss: 0.853848, Weighted Loss: 0.840820 (Best✓)
2025-06-26 08:10:15,491 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:10:15,845 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:10:15,846 - __main__ - INFO - ==================================================
2025-06-26 08:10:15,846 - __main__ - INFO - 生成增强样本
2025-06-26 08:10:15,846 - __main__ - INFO - ==================================================
2025-06-26 08:10:16,305 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:10:16,305 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:10:16,306 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:10:16,306 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:10:16,306 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:10:16,306 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:10:16,500 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:10:16,500 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:10:16,500 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:10:16,500 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:10:16,906 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:10:16,907 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 10 个...
2025-06-26 08:10:16,907 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:10:16,908 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:10:16,908 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:10:45,279 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:11:12,198 - __main__ - INFO - 生成类别 3 的样本...
