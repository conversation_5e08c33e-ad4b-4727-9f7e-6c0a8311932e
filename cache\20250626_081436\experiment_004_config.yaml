# 自动生成的实验配置文件
# 实验 4/4
# 时间戳: 20250626_081436
# 参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 8}

_config_path: test_batch_config.yaml
augmentation:
  cddpm:
    beta_end: 0.02
    beta_schedule: linear
    beta_start: 0.0001
    guidance_scale: 1.0
    timesteps: 1000
    unconditional_prob: 0.1
  generate_fault_only: true
  method: CDDPM
  num_generated_per_class: 8
  save_generated: true
dataset:
  data_loading:
    data_type: sequential
    fault_samples:
      max_fault_samples_per_class: 3
    healthy_samples:
      healthy_label: 0
      max_healthy_samples: 3
    normalization_method: minmax
    normalize: true
    original_length: 1024
    sample_selection: sequential
    signal_length: 1024
    train_val_split: 0.7
  datasets:
    KAT:
      class_names:
      - K001
      - KA01
      - KA05
      - KA09
      - KI01
      - KI03
      - KI05
      - KI08
      num_classes: 8
  name: KAT
evaluation:
  metrics:
    classification: true
    generation: false
experiment:
  current_experiment:
    index: 4
    parameters:
      augmentation.num_generated_per_class: 8
      dataset.data_loading.fault_samples.max_fault_samples_per_class: 3
    timestamp: '20250626_081436'
    total: 4
  description: 批量实验功能测试
  name: test_batch_experiment
  results:
    create_timestamp_folder: true
    save_comparison_csv: true
    save_individual: true
    save_plots_csv: true
  tags:
  - CDDPM
  - batch_test
  - fault_diagnosis
models:
  mrcnn:
    base_channels: 64
    dropout: 0.1
    input_channels: 1
    num_blocks: 4
    num_classes: 8
    use_attention: true
  unet:
    attention_resolutions:
    - 16
    - 8
    channel_mult:
    - 1
    - 2
    - 4
    dropout: 0.1
    in_channels: 1
    model_channels: 64
    num_res_blocks: 2
    out_channels: 1
    use_scale_shift_norm: true
performance_profiles:
  standard:
    system:
      num_workers: 0
      optimization: &id001
        benchmark: true
        channels_last: false
        compile_model: false
        use_amp: false
      pin_memory: false
    training:
      classifier:
        batch_size: 16
      diffusion:
        batch_size: 8
system:
  device: auto
  num_workers: 0
  optimization: *id001
  performance_mode: auto
  pin_memory: false
  save:
    checkpoints_dir: checkpoints
    generated_samples_dir: generated_samples/{dataset_name}
    logs_dir: logs
    max_checkpoints_to_keep: 1
    results_dir: results
    save_best_only: true
    save_every_n_epochs: 1000
  seed: 42
training:
  classifier:
    batch_size: 16
    early_stopping:
      enabled: false
      min_delta: 0.001
      mode: min
      monitor: train_loss
      patience: 20
      restore_best_weights: true
    epochs: 10
    learning_rate: 0.0001
    scheduler:
      T_max: 10
      eta_min: 1.0e-05
      type: cosine
    weight_decay: 0.01
  diffusion:
    batch_size: 8
    early_stopping:
      enabled: false
      min_delta: 0.001
      mode: min
      monitor: val_loss
      patience: 50
      restore_best_weights: true
    epochs: 1
    learning_rate: 0.0001
    scheduler:
      T_max: 1
      eta_min: 1.0e-05
      type: cosine
    weight_decay: 0.01
