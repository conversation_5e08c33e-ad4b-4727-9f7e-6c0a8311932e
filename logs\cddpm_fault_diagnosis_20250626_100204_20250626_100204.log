2025-06-26 10:02:04,397 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_100204_20250626_100204.log
2025-06-26 10:02:04,397 - __main__ - INFO - ================================================================================
2025-06-26 10:02:04,397 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 10:02:04,397 - __main__ - INFO - 数据集: KAT
2025-06-26 10:02:04,397 - __main__ - INFO - ================================================================================
2025-06-26 10:02:04,398 - __main__ - INFO - 🚀 实验开始
2025-06-26 10:02:04,398 - __main__ - INFO - ================================================================================
2025-06-26 10:02:04,398 - __main__ - INFO - 当前实验配置
2025-06-26 10:02:04,398 - __main__ - INFO - ================================================================================
2025-06-26 10:02:04,398 - __main__ - INFO - 数据集: KAT
2025-06-26 10:02:04,398 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 10:02:04,398 - __main__ - INFO - 健康样本总数: -1
2025-06-26 10:02:04,398 - __main__ - INFO - 信号长度: 1024
2025-06-26 10:02:04,398 - __main__ - INFO - 归一化方法: minmax
2025-06-26 10:02:04,398 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 10:02:04,398 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 10:02:04,399 - __main__ - INFO - 只生成故障样本: False
2025-06-26 10:02:04,399 - __main__ - INFO - 扩散模型训练轮数: 2
2025-06-26 10:02:04,399 - __main__ - INFO - 分类器训练轮数: 2
2025-06-26 10:02:04,399 - __main__ - INFO - 扩散模型学习率: 1e-05
2025-06-26 10:02:04,399 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 10:02:04,399 - __main__ - INFO - 设备: auto
2025-06-26 10:02:04,399 - __main__ - INFO - 性能模式: fast
2025-06-26 10:02:04,399 - __main__ - INFO - 随机种子: 42
2025-06-26 10:02:04,399 - __main__ - INFO - ================================================================================
2025-06-26 10:02:04,399 - __main__ - INFO - ============================================================
2025-06-26 10:02:04,400 - __main__ - INFO - 健康样本配置验证
2025-06-26 10:02:04,400 - __main__ - INFO - ============================================================
2025-06-26 10:02:04,400 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 10:02:04,400 - __main__ - INFO - 只生成故障样本: False
2025-06-26 10:02:04,400 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 10:02:04,400 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 10:02:04,400 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 10:02:04,400 - __main__ - INFO - ============================================================
2025-06-26 10:02:04,400 - __main__ - INFO - 使用设备: cuda
2025-06-26 10:02:04,401 - __main__ - INFO - 加载数据...
2025-06-26 10:02:04,401 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 10:02:04,401 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 10:02:04,407 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 10:02:04,414 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 10:02:04,414 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 10:02:04,415 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 10:02:04,415 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 10:02:04,415 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 10:02:04,415 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 10:02:04,415 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,415 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,415 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,416 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,416 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,416 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,416 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 10:02:04,416 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 10:02:04,421 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 10:02:04,426 - common.data_loader - INFO - 数据加载完成:
2025-06-26 10:02:04,426 - common.data_loader - INFO -   训练样本: 24
2025-06-26 10:02:04,426 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 10:02:04,426 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 10:02:04,427 - common.data_loader - INFO -   类别数: 8
2025-06-26 10:02:04,428 - __main__ - INFO - ==================================================
2025-06-26 10:02:04,428 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 10:02:04,428 - __main__ - INFO - ==================================================
2025-06-26 10:02:04,429 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 10:02:04,429 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 10:02:04,429 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 10:02:04,429 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 10:02:04,429 - models.cddpm - INFO -   类别数量: 8
2025-06-26 10:02:04,623 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 10:02:04,624 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 10:02:04,624 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 10:02:04,624 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 10:02:04,896 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 10:02:04,896 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 10:02:04,896 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共2轮
2025-06-26 10:02:08,848 - models.augmentation_factory - INFO - Epoch   1/2: Train Loss: 0.835232, Val Loss: 0.817680, Weighted Loss: 0.829966 (Best✓)
2025-06-26 10:02:09,101 - models.augmentation_factory - INFO - Epoch   2/2: Train Loss: 0.827587, Val Loss: 0.813951, Weighted Loss: 0.823496 (Best✓)
2025-06-26 10:02:09,102 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 10:02:09,515 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 10:02:09,516 - __main__ - INFO - ==================================================
2025-06-26 10:02:09,516 - __main__ - INFO - 生成增强样本
2025-06-26 10:02:09,516 - __main__ - INFO - ==================================================
2025-06-26 10:02:10,045 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 10:02:10,045 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 10:02:10,046 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 10:02:10,046 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 10:02:10,046 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 10:02:10,046 - models.cddpm - INFO -   类别数量: 8
2025-06-26 10:02:10,202 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 10:02:10,202 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 10:02:10,202 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 10:02:10,203 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 10:02:10,677 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 10:02:10,678 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [3]
2025-06-26 10:02:10,678 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 3 个...
2025-06-26 10:02:10,678 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成3个
2025-06-26 10:02:10,678 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 10:02:27,556 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 10:02:44,209 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 10:03:00,911 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 10:03:17,328 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 10:03:34,137 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 10:03:51,052 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 10:04:08,134 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 10:04:24,714 - __main__ - INFO - 样本生成完成，总共生成 24 个样本
2025-06-26 10:04:24,716 - __main__ - INFO - 生成样本已保存:
2025-06-26 10:04:24,716 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 10:04:24,716 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 10:04:28,633 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 10:04:28,634 - __main__ - INFO - ==================================================
2025-06-26 10:04:28,634 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 10:04:28,634 - __main__ - INFO - ==================================================
2025-06-26 10:04:28,677 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 10:04:28,677 - __main__ - INFO - 开始训练分类器...
2025-06-26 10:04:28,987 - __main__ - INFO - Epoch   1/2 | Train Loss: 2.0755 Acc: 0.1250 | Val Loss: 2.0800 Acc: 0.0000 | LR: 1.00e-04 | Time: 00:00
2025-06-26 10:04:29,112 - __main__ - INFO - Epoch   2/2 | Train Loss: 2.0740 Acc: 0.2500 | Val Loss: 2.0812 Acc: 0.0000 | LR: 9.99e-05 | Time: 00:00
2025-06-26 10:04:29,113 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 10:04:29,113 - __main__ - INFO - 最佳验证准确率: 0.0000
2025-06-26 10:04:29,621 - __main__ - INFO - 基线分类器性能:
2025-06-26 10:04:29,621 - __main__ - INFO -   准确率: 0.1249
2025-06-26 10:04:29,621 - __main__ - INFO -   精确率: 0.0156
2025-06-26 10:04:29,621 - __main__ - INFO -   召回率: 0.1249
2025-06-26 10:04:29,622 - __main__ - INFO -   F1分数: 0.0277
2025-06-26 10:04:29,622 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 10:04:29,622 - __main__ - INFO -   最终训练损失: 2.0740
2025-06-26 10:04:29,622 - __main__ - INFO -   最终验证损失: 2.0812
2025-06-26 10:04:29,622 - __main__ - INFO -   最终训练准确率: 0.2500
2025-06-26 10:04:29,622 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 10:04:29,622 - __main__ - INFO -   实际训练轮数: 2
2025-06-26 10:04:29,622 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 10:04:29,623 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 10:04:29,623 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 10:04:29,623 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 10:04:29,623 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 10:04:29,623 - __main__ - INFO - ==================================================
2025-06-26 10:04:29,623 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 10:04:29,623 - __main__ - INFO - ==================================================
2025-06-26 10:04:29,623 - __main__ - INFO - 数据来源确认:
2025-06-26 10:04:29,623 - __main__ - INFO -   基线训练数据来源: train_subset (16 样本)
2025-06-26 10:04:29,623 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 10:04:29,623 - __main__ - INFO -   验证数据来源: val_subset (8 样本)
2025-06-26 10:04:29,624 - __main__ - INFO - 数据维度检查:
2025-06-26 10:04:29,624 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 10:04:29,624 - __main__ - INFO -   生成数据形状: (24, 1024)
2025-06-26 10:04:29,624 - __main__ - INFO - 原始训练数据: 16 样本
2025-06-26 10:04:29,624 - __main__ - INFO - 生成数据: 24 样本
2025-06-26 10:04:29,624 - __main__ - INFO - 增强后训练数据: 40 样本
2025-06-26 10:04:29,624 - __main__ - INFO - 数据分布分析:
2025-06-26 10:04:29,624 - __main__ - INFO -   原始数据类别分布: {0: 2, 1: 3, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 1}
2025-06-26 10:04:29,625 - __main__ - INFO -   生成数据类别分布: {0: 3, 1: 3, 2: 3, 3: 3, 4: 3, 5: 3, 6: 3, 7: 3}
2025-06-26 10:04:29,625 - __main__ - INFO -   增强数据类别分布: {0: 5, 1: 6, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5, 7: 4}
2025-06-26 10:04:29,625 - __main__ - INFO - 健康样本处理逻辑:
2025-06-26 10:04:29,625 - __main__ - INFO -   只生成故障样本: False
2025-06-26 10:04:29,625 - __main__ - INFO -   使用真实健康样本: True
2025-06-26 10:04:29,625 - __main__ - INFO -   真实健康样本数量配置: -1
2025-06-26 10:04:29,625 - __main__ - INFO -   检测到生成的健康样本，使用生成的健康样本
2025-06-26 10:04:29,625 - __main__ - INFO - 数据质量检查:
2025-06-26 10:04:29,626 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 10:04:29,626 - __main__ - INFO -   生成数据形状: (24, 1024)
2025-06-26 10:04:29,626 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 10:04:29,626 - __main__ - INFO -   生成数据范围: [-968.9593, 894.0368]
2025-06-26 10:04:29,652 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 10:04:29,652 - __main__ - INFO - 开始训练分类器...
2025-06-26 10:04:29,760 - __main__ - INFO - Epoch   1/2 | Train Loss: 2.0787 Acc: 0.1429 | Val Loss: 2.1602 Acc: 0.0833 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-26 10:04:29,825 - __main__ - INFO - Epoch   2/2 | Train Loss: 2.0696 Acc: 0.1786 | Val Loss: 2.1579 Acc: 0.0833 | LR: 9.99e-05 | Time: 00:00
2025-06-26 10:04:29,826 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 10:04:29,826 - __main__ - INFO - 最佳验证准确率: 0.0833
2025-06-26 10:04:30,233 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 10:04:30,233 - __main__ - INFO - ==================================================
2025-06-26 10:04:30,233 - __main__ - INFO - 评估分类器性能
2025-06-26 10:04:30,233 - __main__ - INFO - ==================================================
2025-06-26 10:04:30,692 - __main__ - INFO - 分类性能指标:
2025-06-26 10:04:30,692 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 10:04:30,692 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 10:04:30,692 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 10:04:30,692 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 10:04:31,066 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 10:04:31,066 - __main__ - INFO - ==================================================
2025-06-26 10:04:31,066 - __main__ - INFO - 计算GAN-train和GAN-test指标
2025-06-26 10:04:31,066 - __main__ - INFO - ==================================================
2025-06-26 10:04:31,072 - common.metrics - INFO - 计算GAN-train指标...
2025-06-26 10:04:31,957 - common.metrics - INFO - 分类器训练 Epoch 20/100, Loss: 0.7581
2025-06-26 10:04:32,824 - common.metrics - INFO - 分类器训练 Epoch 40/100, Loss: 0.0468
2025-06-26 10:04:33,682 - common.metrics - INFO - 分类器训练 Epoch 60/100, Loss: 0.0027
2025-06-26 10:04:34,547 - common.metrics - INFO - 分类器训练 Epoch 80/100, Loss: 0.0004
2025-06-26 10:04:35,431 - common.metrics - INFO - 分类器训练 Epoch 100/100, Loss: 0.0003
2025-06-26 10:04:35,899 - common.metrics - INFO - GAN-train准确率: 0.1249
2025-06-26 10:04:35,900 - common.metrics - INFO - 计算GAN-test指标...
2025-06-26 10:04:36,677 - common.metrics - INFO - 分类器训练 Epoch 20/100, Loss: 0.5957
2025-06-26 10:04:37,422 - common.metrics - INFO - 分类器训练 Epoch 40/100, Loss: 0.0053
2025-06-26 10:04:38,231 - common.metrics - INFO - 分类器训练 Epoch 60/100, Loss: 0.0005
2025-06-26 10:04:38,985 - common.metrics - INFO - 分类器训练 Epoch 80/100, Loss: 0.0002
2025-06-26 10:04:39,782 - common.metrics - INFO - 分类器训练 Epoch 100/100, Loss: 0.0001
2025-06-26 10:04:39,795 - common.metrics - INFO - GAN-test准确率: 0.1250
2025-06-26 10:04:39,796 - __main__ - INFO - GAN评估指标:
2025-06-26 10:04:39,796 - __main__ - INFO -   GAN-train: 0.1249
2025-06-26 10:04:39,796 - __main__ - INFO -   GAN-test: 0.1250
2025-06-26 10:04:39,796 - __main__ - INFO - ==================================================
2025-06-26 10:04:39,796 - __main__ - INFO - 性能对比总结
2025-06-26 10:04:39,796 - __main__ - INFO - ==================================================
2025-06-26 10:04:39,796 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 10:04:39,796 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 10:04:39,796 - __main__ - INFO -   精确率: 0.0156 → 0.0156 (Δ+0.0000)
2025-06-26 10:04:39,797 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 10:04:39,797 - __main__ - INFO -   F1分数: 0.0277 → 0.0277 (Δ+0.0000)
2025-06-26 10:04:39,797 - __main__ - INFO - 🏁 实验结束
2025-06-26 10:04:39,797 - __main__ - INFO - ================================================================================
2025-06-26 10:04:39,797 - __main__ - INFO - 当前实验配置
2025-06-26 10:04:39,797 - __main__ - INFO - ================================================================================
2025-06-26 10:04:39,797 - __main__ - INFO - 数据集: KAT
2025-06-26 10:04:39,797 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 10:04:39,797 - __main__ - INFO - 健康样本总数: -1
2025-06-26 10:04:39,797 - __main__ - INFO - 信号长度: 1024
2025-06-26 10:04:39,797 - __main__ - INFO - 归一化方法: minmax
2025-06-26 10:04:39,798 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 10:04:39,798 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 10:04:39,798 - __main__ - INFO - 只生成故障样本: False
2025-06-26 10:04:39,798 - __main__ - INFO - 扩散模型训练轮数: 2
2025-06-26 10:04:39,798 - __main__ - INFO - 分类器训练轮数: 2
2025-06-26 10:04:39,798 - __main__ - INFO - 扩散模型学习率: 1e-05
2025-06-26 10:04:39,798 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 10:04:39,798 - __main__ - INFO - 设备: auto
2025-06-26 10:04:39,798 - __main__ - INFO - 性能模式: fast
2025-06-26 10:04:39,798 - __main__ - INFO - 随机种子: 42
2025-06-26 10:04:39,798 - __main__ - INFO - ================================================================================
2025-06-26 10:04:39,831 - __main__ - INFO - 实验完成，结果已保存: results\cddpm_fault_diagnosis_20250626_100204_results.json
2025-06-26 10:04:39,832 - __main__ - INFO - 实验完成
2025-06-26 10:04:39,832 - __main__ - INFO - 程序结束
