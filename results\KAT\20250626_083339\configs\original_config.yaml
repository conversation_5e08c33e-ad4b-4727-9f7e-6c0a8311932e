# ========================================
# 实验模式测试配置文件
# 测试不同的健康样本处理策略
# ========================================

# 数据集配置
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载参数
  data_loading:
    # 公共参数
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    # ========== 实验模式配置 ==========
    experiment_mode: "imbalanced"    # 测试不平衡实验模式
    
    # 不平衡实验配置
    imbalanced_config:
      healthy_samples_fixed: 50      # 健康样本固定50个
      fault_samples_per_class: [10, 20]  # 故障样本10个和20个（对比实验）

    # ========== 健康样本处理策略 ==========
    healthy_sample_strategy:
      # 扩散模型训练时是否包含健康样本
      include_in_diffusion_training: true   # 健康样本参与扩散模型训练
      
      # 样本生成策略
      generation_strategy: "real_only"     # 只使用真实健康样本（不生成）
      
      # 分类器训练时是否包含健康样本
      include_in_classification: true      # 分类器训练包含健康样本（多分类）
      
      healthy_label: 0                    # 健康样本的标签值

# 数据增强配置
augmentation:
  method: "CDDPM"
  save_generated: true

  # 向后兼容：保留原有配置
  num_generated_per_class: [30, 40]    # 对比实验：生成30个和40个样本

  # ========== 生成样本数量配置 ==========
  generation_config:
    imbalanced_mode:
      strategy: "balance_to_healthy"  # 生成到与健康样本相等

  # CDDPM参数
  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02
    unconditional_prob: 0.1
    guidance_scale: 1.0

# 模型配置
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# 训练配置（快速测试）
training:
  diffusion:
    epochs: 1                   # 快速测试
    batch_size: 8
    learning_rate: 0.0001
    weight_decay: 0.01
    
    scheduler:
      type: "cosine"
      T_max: 1
      eta_min: 0.00001

    early_stopping:
      enabled: false
      patience: 50
      min_delta: 0.001
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

  classifier:
    epochs: 5                   # 快速测试
    batch_size: 16
    learning_rate: 0.0001
    weight_decay: 0.01

    scheduler:
      type: "cosine"
      T_max: 5
      eta_min: 0.00001

    early_stopping:
      enabled: false
      patience: 20
      min_delta: 0.001
      monitor: "train_loss"
      mode: "min"
      restore_best_weights: true

# 评估配置
evaluation:
  metrics:
    classification: true
    generation: false           # 跳过生成质量评估（加快测试）

# 系统配置
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 1

# 实验配置
experiment:
  name: "test_experiment_modes"
  description: "测试不同实验模式和健康样本处理策略"
  tags: ["test", "experiment_modes", "healthy_samples"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# 性能模式配置
performance_profiles:
  standard:
    training:
      diffusion:
        batch_size: 8
      classifier:
        batch_size: 16
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true
