2025-06-26 09:19:24,617 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_weighted_loss_early_stopping_20250626_091924_20250626_091924.log
2025-06-26 09:19:24,617 - __main__ - INFO - ================================================================================
2025-06-26 09:19:24,617 - __main__ - INFO - 开始单一实验: test_weighted_loss_early_stopping
2025-06-26 09:19:24,617 - __main__ - INFO - 数据集: KAT
2025-06-26 09:19:24,618 - __main__ - INFO - ================================================================================
2025-06-26 09:19:24,618 - __main__ - INFO - 🚀 实验开始
2025-06-26 09:19:24,618 - __main__ - INFO - ================================================================================
2025-06-26 09:19:24,618 - __main__ - INFO - 当前实验配置
2025-06-26 09:19:24,618 - __main__ - INFO - ================================================================================
2025-06-26 09:19:24,618 - __main__ - INFO - 数据集: KAT
2025-06-26 09:19:24,618 - __main__ - INFO - 故障样本每类: [2]
2025-06-26 09:19:24,618 - __main__ - INFO - 健康样本总数: -1
2025-06-26 09:19:24,619 - __main__ - INFO - 信号长度: 1024
2025-06-26 09:19:24,619 - __main__ - INFO - 归一化方法: minmax
2025-06-26 09:19:24,619 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 09:19:24,619 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 09:19:24,619 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:19:24,619 - __main__ - INFO - 扩散模型训练轮数: 50
2025-06-26 09:19:24,619 - __main__ - INFO - 分类器训练轮数: 30
2025-06-26 09:19:24,619 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 09:19:24,619 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 09:19:24,619 - __main__ - INFO - 设备: auto
2025-06-26 09:19:24,620 - __main__ - INFO - 性能模式: auto
2025-06-26 09:19:24,620 - __main__ - INFO - 随机种子: 42
2025-06-26 09:19:24,620 - __main__ - INFO - ================================================================================
2025-06-26 09:19:24,620 - __main__ - INFO - ============================================================
2025-06-26 09:19:24,620 - __main__ - INFO - 健康样本配置验证
2025-06-26 09:19:24,620 - __main__ - INFO - ============================================================
2025-06-26 09:19:24,620 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 09:19:24,620 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:19:24,620 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 09:19:24,620 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 09:19:24,620 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 09:19:24,620 - __main__ - INFO - ============================================================
2025-06-26 09:19:24,622 - __main__ - INFO - 使用设备: cuda
2025-06-26 09:19:24,622 - __main__ - INFO - 加载数据...
2025-06-26 09:19:24,623 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 09:19:24,623 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 09:19:24,628 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:19:24,634 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:19:24,634 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:19:24,634 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:19:24,634 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 2
2025-06-26 09:19:24,635 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,635 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,636 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 09:19:24,636 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 09:19:24,640 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 09:19:24,649 - common.data_loader - INFO - 数据加载完成:
2025-06-26 09:19:24,650 - common.data_loader - INFO -   训练样本: 16
2025-06-26 09:19:24,650 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 09:19:24,650 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 09:19:24,650 - common.data_loader - INFO -   类别数: 8
2025-06-26 09:19:24,654 - __main__ - INFO - ==================================================
2025-06-26 09:19:24,654 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 09:19:24,654 - __main__ - INFO - ==================================================
2025-06-26 09:19:24,666 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 09:19:24,666 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:19:24,666 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:19:24,666 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:19:24,667 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:19:24,856 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:19:24,856 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:19:24,856 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:19:24,857 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:19:25,145 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 09:19:25,145 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 09:19:25,145 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共50轮
2025-06-26 09:19:32,895 - models.augmentation_factory - INFO - Epoch   1/50: Train Loss: 0.845850, Val Loss: 0.883640, Weighted Loss: 0.860966 (Best✓)
2025-06-26 09:19:33,146 - models.augmentation_factory - INFO - Epoch   2/50: Train Loss: 0.853239, Val Loss: 0.832448, Weighted Loss: 0.844923 (Best✓)
2025-06-26 09:19:33,397 - models.augmentation_factory - INFO - Epoch   3/50: Train Loss: 0.820804, Val Loss: 0.832810, Weighted Loss: 0.825607 (Best✓)
2025-06-26 09:19:33,658 - models.augmentation_factory - INFO - Epoch   4/50: Train Loss: 0.823213, Val Loss: 0.809691, Weighted Loss: 0.817804 (Best✓)
2025-06-26 09:19:33,898 - models.augmentation_factory - INFO - Epoch   5/50: Train Loss: 0.817322, Val Loss: 0.799448, Weighted Loss: 0.810172 (Best✓)
2025-06-26 09:19:34,634 - models.augmentation_factory - INFO - Epoch   8/50: Train Loss: 0.805841, Val Loss: 0.808577, Weighted Loss: 0.806936 (Best✓)
2025-06-26 09:19:35,393 - models.augmentation_factory - INFO - Epoch  11/50: Train Loss: 0.814447, Val Loss: 0.791484, Weighted Loss: 0.805261 (Best✓)
2025-06-26 09:19:35,654 - models.augmentation_factory - INFO - Epoch  12/50: Train Loss: 0.807710, Val Loss: 0.801273, Weighted Loss: 0.805135 (Best✓)
2025-06-26 09:19:35,910 - models.augmentation_factory - INFO - Epoch  13/50: Train Loss: 0.804244, Val Loss: 0.800627, Weighted Loss: 0.802797 (Best✓)
2025-06-26 09:19:36,178 - models.augmentation_factory - INFO - Epoch  14/50: Train Loss: 0.794413, Val Loss: 0.791729, Weighted Loss: 0.793339 (Best✓)
2025-06-26 09:19:36,436 - models.augmentation_factory - INFO - Epoch  15/50: Train Loss: 0.792928, Val Loss: 0.786071, Weighted Loss: 0.790185 (Best✓)
2025-06-26 09:19:36,947 - models.augmentation_factory - INFO - Epoch  17/50: Train Loss: 0.787467, Val Loss: 0.787941, Weighted Loss: 0.787657 (Best✓)
2025-06-26 09:19:37,196 - models.augmentation_factory - INFO - Epoch  18/50: Train Loss: 0.782070, Val Loss: 0.776424, Weighted Loss: 0.779812 (Best✓)
2025-06-26 09:19:37,438 - models.augmentation_factory - INFO - Epoch  19/50: Train Loss: 0.767209, Val Loss: 0.756521, Weighted Loss: 0.762934 (Best✓)
2025-06-26 09:19:37,681 - models.augmentation_factory - INFO - Epoch  20/50: Train Loss: 0.766781, Val Loss: 0.721071, Weighted Loss: 0.748497 (Best✓)
2025-06-26 09:19:37,941 - models.augmentation_factory - INFO - Epoch  21/50: Train Loss: 0.724402, Val Loss: 0.694918, Weighted Loss: 0.712609 (Best✓)
2025-06-26 09:19:38,225 - models.augmentation_factory - INFO - Epoch  22/50: Train Loss: 0.698161, Val Loss: 0.675256, Weighted Loss: 0.688999 (Best✓)
2025-06-26 09:19:38,472 - models.augmentation_factory - INFO - Epoch  23/50: Train Loss: 0.690691, Val Loss: 0.669287, Weighted Loss: 0.682129 (Best✓)
2025-06-26 09:19:38,966 - models.augmentation_factory - INFO - Epoch  25/50: Train Loss: 0.651244, Val Loss: 0.635831, Weighted Loss: 0.645079 (Best✓)
2025-06-26 09:19:39,216 - models.augmentation_factory - INFO - Epoch  26/50: Train Loss: 0.623246, Val Loss: 0.601614, Weighted Loss: 0.614593 (Best✓)
2025-06-26 09:19:39,705 - models.augmentation_factory - INFO - Epoch  28/50: Train Loss: 0.588149, Val Loss: 0.608889, Weighted Loss: 0.596445 (Best✓)
2025-06-26 09:19:40,193 - models.augmentation_factory - INFO - Epoch  30/50: Train Loss: 0.586701, Val Loss: 0.551725, Weighted Loss: 0.572711 (Best✓)
2025-06-26 09:19:40,439 - models.augmentation_factory - INFO - Epoch  31/50: Train Loss: 0.560381, Val Loss: 0.568698, Weighted Loss: 0.563708 (Best✓)
2025-06-26 09:19:40,676 - models.augmentation_factory - INFO - Epoch  32/50: Train Loss: 0.567569, Val Loss: 0.531893, Weighted Loss: 0.553298 (Best✓)
2025-06-26 09:19:41,157 - models.augmentation_factory - INFO - Epoch  34/50: Train Loss: 0.518346, Val Loss: 0.481451, Weighted Loss: 0.503588 (Best✓)
2025-06-26 09:19:41,648 - models.augmentation_factory - INFO - Epoch  36/50: Train Loss: 0.493041, Val Loss: 0.519177, Weighted Loss: 0.503495 (Best✓)
2025-06-26 09:19:41,889 - models.augmentation_factory - INFO - Epoch  37/50: Train Loss: 0.545450, Val Loss: 0.429796, Weighted Loss: 0.499188 (Best✓)
2025-06-26 09:19:42,135 - models.augmentation_factory - INFO - Epoch  38/50: Train Loss: 0.553824, Val Loss: 0.399188, Weighted Loss: 0.491970 (Best✓)
2025-06-26 09:19:42,392 - models.augmentation_factory - INFO - Epoch  39/50: Train Loss: 0.443989, Val Loss: 0.395876, Weighted Loss: 0.424744 (Best✓)
2025-06-26 09:19:42,634 - models.augmentation_factory - INFO - Epoch  40/50: Train Loss: 0.400204, Val Loss: 0.381477, Weighted Loss: 0.392713 (Best✓)
2025-06-26 09:19:43,111 - models.augmentation_factory - INFO - Epoch  42/50: Train Loss: 0.352711, Val Loss: 0.432090, Weighted Loss: 0.384463 (Best✓)
2025-06-26 09:19:43,594 - models.augmentation_factory - INFO - Epoch  44/50: Train Loss: 0.362110, Val Loss: 0.290592, Weighted Loss: 0.333503 (Best✓)
2025-06-26 09:19:43,842 - models.augmentation_factory - INFO - Epoch  45/50: Train Loss: 0.344672, Val Loss: 0.311141, Weighted Loss: 0.331260 (Best✓)
2025-06-26 09:19:44,089 - models.augmentation_factory - INFO - Epoch  46/50: Train Loss: 0.287831, Val Loss: 0.371106, Weighted Loss: 0.321141 (Best✓)
2025-06-26 09:19:44,330 - models.augmentation_factory - INFO - Epoch  47/50: Train Loss: 0.344746, Val Loss: 0.266664, Weighted Loss: 0.313513 (Best✓)
2025-06-26 09:19:45,057 - models.augmentation_factory - INFO - Epoch  50/50: Train Loss: 0.258097, Val Loss: 0.260857, Weighted Loss: 0.259201 (Best✓)
2025-06-26 09:19:45,057 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 09:19:45,665 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 09:19:45,666 - __main__ - INFO - ==================================================
2025-06-26 09:19:45,666 - __main__ - INFO - 生成增强样本
2025-06-26 09:19:45,667 - __main__ - INFO - ==================================================
2025-06-26 09:19:46,999 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:19:46,999 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:19:47,001 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:19:47,001 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:19:47,001 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:19:47,002 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:19:47,181 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:19:47,181 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:19:47,181 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:19:47,182 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:19:47,762 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 09:19:47,763 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [3]
2025-06-26 09:19:47,763 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 3 个...
2025-06-26 09:19:47,763 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 09:19:47,763 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 09:19:47,763 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 09:20:05,659 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 09:20:23,789 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 09:20:41,772 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 09:20:59,072 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 09:21:16,882 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 09:21:35,714 - __main__ - INFO - 生成类别 7 的样本...
