{"basic_statistics": {"experiment_index": {"count": 4.0, "mean": 2.5, "std": 1.2909944487358056, "min": 1.0, "25%": 1.75, "50%": 2.5, "75%": 3.25, "max": 4.0}, "num_generated_per_class": {"count": 4.0, "mean": 6.5, "std": 1.7320508075688772, "min": 5.0, "25%": 5.0, "50%": 6.5, "75%": 8.0, "max": 8.0}, "max_fault_samples_per_class": {"count": 4.0, "mean": 2.5, "std": 0.5773502691896257, "min": 2.0, "25%": 2.0, "50%": 2.5, "75%": 3.0, "max": 3.0}, "max_healthy_samples": {"count": 4.0, "mean": 2.5, "std": 0.5773502691896257, "min": 2.0, "25%": 2.0, "50%": 2.5, "75%": 3.0, "max": 3.0}, "baseline_accuracy": {"count": 4.0, "mean": 0.12487512487512488, "std": 0.0, "min": 0.12487512487512488, "25%": 0.12487512487512488, "50%": 0.12487512487512488, "75%": 0.12487512487512488, "max": 0.12487512487512488}, "baseline_precision": {"count": 4.0, "mean": 0.015769008012719358, "std": 0.00020231646713326294, "min": 0.015593796812578033, "25%": 0.015593796812578033, "50%": 0.015769008012719358, "75%": 0.015944219212860683, "max": 0.015944219212860683}, "baseline_recall": {"count": 4.0, "mean": 0.12487512487512488, "std": 0.0, "min": 0.12487512487512488, "25%": 0.12487512487512488, "50%": 0.12487512487512488, "75%": 0.12487512487512488, "max": 0.12487512487512488}, "baseline_f1_score": {"count": 4.0, "mean": 0.028001632251960458, "std": 0.0003189852001840392, "min": 0.027725382965169815, "25%": 0.027725382965169815, "50%": 0.028001632251960458, "75%": 0.0282778815387511, "max": 0.0282778815387511}, "augmented_accuracy": {"count": 4.0, "mean": 0.12487512487512488, "std": 0.0, "min": 0.12487512487512488, "25%": 0.12487512487512488, "50%": 0.12487512487512488, "75%": 0.12487512487512488, "max": 0.12487512487512488}, "augmented_precision": {"count": 4.0, "mean": 0.015593796812578033, "std": 0.0, "min": 0.015593796812578033, "25%": 0.015593796812578033, "50%": 0.015593796812578033, "75%": 0.015593796812578033, "max": 0.015593796812578033}, "augmented_recall": {"count": 4.0, "mean": 0.12487512487512488, "std": 0.0, "min": 0.12487512487512488, "25%": 0.12487512487512488, "50%": 0.12487512487512488, "75%": 0.12487512487512488, "max": 0.12487512487512488}, "augmented_f1_score": {"count": 4.0, "mean": 0.027725382965169815, "std": 0.0, "min": 0.027725382965169815, "25%": 0.027725382965169815, "50%": 0.027725382965169815, "75%": 0.027725382965169815, "max": 0.027725382965169815}, "accuracy_improvement": {"count": 4.0, "mean": 0.0, "std": 0.0, "min": 0.0, "25%": 0.0, "50%": 0.0, "75%": 0.0, "max": 0.0}, "precision_improvement": {"count": 4.0, "mean": -0.00017521120014132516, "std": 0.00020231646713326294, "min": -0.0003504224002826503, "25%": -0.0003504224002826503, "50%": -0.00017521120014132516, "75%": 0.0, "max": 0.0}, "recall_improvement": {"count": 4.0, "mean": 0.0, "std": 0.0, "min": 0.0, "25%": 0.0, "50%": 0.0, "75%": 0.0, "max": 0.0}, "f1_improvement": {"count": 4.0, "mean": -0.00027624928679064256, "std": 0.0003189852001840392, "min": -0.0005524985735812851, "25%": -0.0005524985735812851, "50%": -0.00027624928679064256, "75%": 0.0, "max": 0.0}, "augmentation_training_time": {"count": 4.0, "mean": 0.0, "std": 0.0, "min": 0.0, "25%": 0.0, "50%": 0.0, "75%": 0.0, "max": 0.0}, "classifier_training_time": {"count": 4.0, "mean": 0.7815881967544556, "std": 0.17881623078551137, "min": 0.5803992748260498, "25%": 0.6561618447303772, "50%": 0.8060488700866699, "75%": 0.9314752221107483, "max": 0.9338557720184326}}, "best_result": {"experiment_index": 1, "timestamp": "20250626_081508", "dataset": "KAT", "augmentation_method": "CDDPM", "num_generated_per_class": 5, "max_fault_samples_per_class": 2, "max_healthy_samples": 2, "use_healthy": true, "baseline_accuracy": 0.12487512487512488, "baseline_precision": 0.015944219212860683, "baseline_recall": 0.12487512487512488, "baseline_f1_score": 0.0282778815387511, "augmented_accuracy": 0.12487512487512488, "augmented_precision": 0.015593796812578033, "augmented_recall": 0.12487512487512488, "augmented_f1_score": 0.027725382965169815, "accuracy_improvement": 0.0, "precision_improvement": -0.0003504224002826503, "recall_improvement": 0.0, "f1_improvement": -0.0005524985735812851, "augmentation_training_time": 0.0, "classifier_training_time": 0.6814160346984863}, "method_statistics": {"experiment_index": {"CDDPM": 2.5}, "num_generated_per_class": {"CDDPM": 6.5}, "max_fault_samples_per_class": {"CDDPM": 2.5}, "max_healthy_samples": {"CDDPM": 2.5}, "baseline_accuracy": {"CDDPM": 0.12487512487512488}, "baseline_precision": {"CDDPM": 0.015769008012719358}, "baseline_recall": {"CDDPM": 0.12487512487512488}, "baseline_f1_score": {"CDDPM": 0.028001632251960458}, "augmented_accuracy": {"CDDPM": 0.12487512487512488}, "augmented_precision": {"CDDPM": 0.015593796812578033}, "augmented_recall": {"CDDPM": 0.12487512487512488}, "augmented_f1_score": {"CDDPM": 0.027725382965169815}, "accuracy_improvement": {"CDDPM": 0.0}, "precision_improvement": {"CDDPM": -0.00017521120014132516}, "recall_improvement": {"CDDPM": 0.0}, "f1_improvement": {"CDDPM": -0.00027624928679064256}, "augmentation_training_time": {"CDDPM": 0.0}, "classifier_training_time": {"CDDPM": 0.7815881967544556}}}