# 一维振动信号数据增强项目配置文件
# 基于条件去噪扩散概率模型(CDDPM)和多尺度残差CNN的故障诊断

# 数据集配置
dataset:
  name: "KAT"  # 单一数据集 或 ["KAT", "SK"] 多数据集对比实验
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]
    SK:
      num_classes: 3
      class_names: ["0", "1", "2"]  #正常 预警 故障
    JST:
      num_classes: 3
      class_names: ["0", "1", "2"]  # 正常 弱 强

  # 数据加载参数
  data_loading:
    # 公共参数
    data_type: "sequential"      # 数据类型: "random" 或 "sequential" 到底选择哪个类型数据，从随机还是顺序文件夹读取数据
    sample_selection: "sequential"  # 样本选择方式: "random" 或 "sequential"
    original_length: 1024        # 原始信号长度
    signal_length: 1024          # 截取后的信号长度（论文中使用1024）
    train_val_split: 0.7         # 训练集在训练数据中的比例（剩余为验证集）
    normalize: true              # 是否归一化
    normalization_method: "minmax"  # 归一化方法: minmax, zscore, robust

    # 样本数量配置（分开配置）
    fault_samples:
      max_fault_samples_per_class: [3, 4]          # 故障样本：单一值 或 [10, 20, 30] 多值对比实验

    healthy_samples:
      max_healthy_samples: 1          # 健康样本开关：>0启用并动态调整，=0禁用
      healthy_label: 0                # 健康样本的标签值（通常为0）

# 数据增强配置
augmentation:
  method: "CDDPM"    # 单一方法 或 ["CDDPM", "CGAN", "WGAN", "WGAN-GP", "DDPM", "DCGAN", "CVAE"] 多方法对比实验
  num_generated_per_class: [2, 3]    # 单一值 或 [10, 20, 30] 多值对比实验
  save_generated: true           # 是否保存生成的样本
  generate_fault_only: true     # 是否只生成故障样本（健康样本直接从原始数据加载）

  # 生成策略配置
  generation_strategy:
    target_samples_per_class: 10  # 每个类别的目标样本数（组合实验时会自动调整）
    initial_multiplier: 5.0       # 初始生成倍数（生成target * multiplier个样本用于筛选）
    min_multiplier: 2.0           # 最小生成倍数
    max_multiplier: 10.0          # 最大生成倍数
    adaptive_generation: true     # 是否启用自适应生成（根据筛选效果调整）

  # CDDPM参数
  cddpm:
    timesteps: 1000             # 扩散步数
    beta_schedule: "linear"     # 噪声调度: linear, cosine
    beta_start: 0.0001         # 起始beta值
    beta_end: 0.02             # 结束beta值

    # Classifier-Free Guidance 参数（论文中的条件/无条件混合训练）
    unconditional_prob: 0.1    # 无条件训练概率（10%的样本进行无条件训练）
    guidance_scale: 1.0        # 引导强度（1.0=标准条件生成，>1.0=增强条件控制）

  # CGAN参数
  cgan:
    latent_dim: 100            # 潜在空间维度
    generator_lr: 0.0002       # 生成器学习率
    discriminator_lr: 0.0002   # 判别器学习率
    beta1: 0.5                 # Adam优化器beta1参数
    epochs: 1                  # 快速测试用1轮

  # WGAN参数
  wgan:
    latent_dim: 100            # 潜在空间维度
    generator_lr: 0.00005      # 生成器学习率
    critic_lr: 0.00005         # 判别器学习率
    n_critic: 5                # 判别器训练次数
    clip_value: 0.01           # 权重裁剪值
    epochs: 1                  # 快速测试用1轮

  # WGAN-GP参数
  wgan_gp:
    latent_dim: 100            # 潜在空间维度
    generator_lr: 0.0001       # 生成器学习率
    critic_lr: 0.0001          # 判别器学习率
    n_critic: 5                # 判别器训练次数
    lambda_gp: 10              # 梯度惩罚系数
    epochs: 1                  # 快速测试用1轮

  # DDPM参数（原始DDPM，无条件）
  ddpm:
    timesteps: 1000            # 扩散步数
    beta_schedule: "linear"    # 噪声调度
    beta_start: 0.0001         # 起始beta值
    beta_end: 0.02             # 结束beta值
    learning_rate: 0.0001      # 学习率
    epochs: 1                  # 快速测试用1轮

  # DCGAN参数
  dcgan:
    latent_dim: 100            # 潜在空间维度
    generator_lr: 0.0002       # 生成器学习率
    discriminator_lr: 0.0002   # 判别器学习率
    beta1: 0.5                 # Adam优化器beta1参数
    epochs: 1                  # 快速测试用1轮

  # CVAE参数（条件变分自编码器）
  cvae:
    latent_dim: 100            # 潜在空间维度
    hidden_dim: 512            # 隐藏层维度
    learning_rate: 0.0001      # 学习率
    beta: 1.0                  # KL散度权重
    epochs: 1                  # 快速测试用1轮

  # 传统数据增强方法参数
  traditional:
    # ADASYN参数
    adasyn:
      sampling_strategy: "auto" # 采样策略
      n_neighbors: 5            # 近邻数量
      random_state: 42          # 随机种子

    # SMOTEENN参数
    smoteenn:
      sampling_strategy: "auto" # 采样策略
      random_state: 42          # 随机种子

    # K-means-SMOTE参数
    kmeans_smote:
      sampling_strategy: "auto" # 采样策略
      k_neighbors: 5            # 近邻数量
      cluster_balance_threshold: 0.005  # 聚类平衡阈值
      random_state: 42          # 随机种子

# 生成数据筛选配置
data_screening:
  enabled: false                 # 是否启用数据筛选
  screening_level: "basic"       # 筛选档位: basic, advanced, comprehensive

  # 目标数量控制
  target_control:
    enabled: true               # 是否启用目标数量控制
    strict_target: false         # 是否严格控制目标数量（true: 精确控制, false: 允许少量偏差）
    fallback_strategy: "relax_thresholds"  # 不足时的回退策略: relax_thresholds, random_fill, regenerate

  # 置信度过滤
  confidence_filter:
    enabled: true               # 是否启用置信度过滤
    threshold: 0.3              # 置信度阈值（初始值，会自适应调整）
    min_threshold: 0.1          # 最小阈值（自适应调整的下限）
    max_threshold: 0.8          # 最大阈值（自适应调整的上限）
    per_class: false            # 是否按类别独立设置阈值
    adaptive: true              # 是否启用自适应阈值调整

  # Influence评分过滤
  influence_filter:
    enabled: true               # 是否启用Influence过滤
    method: "tracin"            # 方法: tracin, influence_function
    ratio: 0.3                  # 剔除负分前30%（初始值，会自适应调整）
    min_ratio: 0.1              # 最小剔除比例
    max_ratio: 0.5              # 最大剔除比例
    per_class: true             # 是否按类别独立计算
    batch_size: 32              # 计算时的批次大小
    adaptive: true              # 是否启用自适应比例调整

  # 离群检测
  outlier_detection:
    enabled: true              # 是否启用离群检测
    method: "lof"               # 方法: lof, one_class_svm, mahalanobis
    k_neighbors: 20             # LOF的k值
    contamination: 0.1          # 异常比例（初始值，会自适应调整）
    min_contamination: 0.05     # 最小异常比例
    max_contamination: 0.2      # 最大异常比例
    adaptive: true              # 是否启用自适应调整

  # 多样性子集选择（最终数量控制）
  diversity_selection:
    enabled: true               # 是否启用多样性选择
    method: "kmeans"            # 方法: kmeans, dpp, random
    use_target_count: true      # 是否使用目标数量而非比例
    target_ratio: 0.8           # 目标保留比例（当use_target_count=false时使用）
    n_clusters: null            # 聚类数量（null表示使用目标数量）

  # Soft-Delete权重缩放（替代硬删除）
  soft_delete:
    enabled: false              # 是否启用软删除
    min_weight: 0.1             # 最小权重
    max_weight: 1.0             # 最大权重

# 模型配置
models:
  # 扩散模型配置 (匹配t2的简单结构)
  diffusion:
    model_type: "UNet1D"        # 去噪网络类型
    hidden_dim: 128             # 使用128作为基础通道数
    num_layers: 5               # 网络层数
    num_heads: 8                # 注意力头数
    dropout: 0.1                # Dropout率
    
  # 分类器配置
  classifier:
    model_type: "MRCNN"         # 分类器类型（使用您的MRCNN）
    kernel_sizes: [3, 5, 7]     # 恢复原始3个尺度
    base_channels: 64           # 恢复原始通道数
    dropout: 0.2                # 使用0.2

  # MRCNN特定配置
  MRCNN:
    kernel_sizes: [3, 5, 7]     # 恢复原始3个尺度
    base_channels: 64           # 恢复原始通道数
    num_layers: 3               # 网络层数
    pool_size: 2                # 池化大小
    use_batch_norm: true        # 使用批标准化
    activation: "relu"          # 激活函数

# 训练配置
training:
  # 扩散模型训练
  diffusion:
    epochs: 20               # 快速测试用1个epoch--------------------------------------------
    batch_size: 64              # 使用32
    learning_rate: 0.0001       # 使用1e-4
    weight_decay: 0.0001
    scheduler:
      type: "cosine"            # 学习率调度器类型: none, cosine, step, exponential
      T_max: 20000              # cosine调度器的最大epoch (与训练轮数一致)
      eta_min: 0.00001          # cosine调度器的最小学习率
      step_size: 20             # step调度器的步长
      gamma: 0.5                # step调度器的衰减因子
    early_stopping:
      enabled: false            # 是否启用早停
      patience: 1000            # 耐心值（多少个epoch没有改善就停止）
      min_delta: 0.0001         # 最小改善阈值
      monitor: "weighted_loss"     # 监控指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"               # 优化方向: "min" (越小越好) 或 "max" (越大越好)
      restore_best_weights: true # 是否恢复最佳权重

      # 加权损失配置 (仅当monitor为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7       # 训练损失权重
        val_weight: 0.3         # 验证损失权重

    # 最佳模型判断配置
    best_model_criteria:
      metric: "weighted_loss"      # 判断指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"               # 优化方向: "min" (越小越好) 或 "max" (越大越好)

      # 加权损失配置 (仅当metric为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7       # 训练损失权重
        val_weight: 0.3         # 验证损失权重
        # 最终损失 = train_weight * train_loss + val_weight * val_loss
    
  # 分类器训练
  classifier:
    epochs: 200                  # 快速测试用1个epoch-----------------------------------------------
    batch_size: 128              # 使用32
    learning_rate: 0.0001       # 使用1e-4 (重要!)
    weight_decay: 0.0001
    scheduler:
      type: "step"              # 使用step调度器
      step_size: 100             # 每10轮衰减
      gamma: 0.5                # 衰减因子0.5
    early_stopping:
      enabled: false            # 是否启用早停
      patience: 100             # 耐心值（多少个epoch没有改善就停止）
      min_delta: 0.0001         # 最小改善阈值
      monitor: "weighted_loss"     # 监控指标: "train_loss", "val_loss", "weighted_loss"
      mode: "min"               # 优化方向: "min" (越小越好) 或 "max" (越大越好)
      restore_best_weights: true # 是否恢复最佳权重

      # 加权损失配置 (仅当monitor为"weighted_loss"时生效)
      weighted_loss:
        train_weight: 0.7       # 训练损失权重
        val_weight: 0.3         # 验证损失权重

# 评估配置
evaluation:
  metrics:
    classification: ["accuracy", "precision", "recall", "f1_score"]
    generation: ["gan_train", "gan_test"]
  
  # GAN-train和GAN-test评估
  gan_evaluation:
    classifier_epochs: 500      # 用于GAN评估的分类器训练轮数
    batch_size: 64
    
  # 可视化配置
  visualization:
    save_confusion_matrix: true
    save_tsne: true
    tsne_perplexity: 30
    tsne_n_iter: 1000
    save_sample_plots: true
    num_samples_to_plot: 10

# 系统配置
system:
  device: "cuda"                # 设备选择: auto, cpu, cuda
  performance_mode: "auto"      # 性能模式: auto, standard, high_performance, ultra
  num_workers: 8               # 数据加载器工作进程数（auto模式会自动调整）
  pin_memory: false             # 是否固定内存（auto模式会自动调整）
  seed: 42                      # 随机种子

  # 性能优化配置
  optimization:
    use_amp: false              # 自动混合精度（auto模式会根据GPU自动启用）
    compile_model: false        # PyTorch 2.0编译优化（auto模式会自动启用）
    channels_last: false        # channels_last内存格式（auto模式会自动启用）
    benchmark: false            # cuDNN benchmark（auto模式会自动启用）
  
  # 保存配置
  save:
    checkpoints_dir: "checkpoints"
    results_dir: "results"
    logs_dir: "logs"
    generated_samples_dir: "dataset/{dataset_name}/gen_samples"
    
    # 检查点保存策略
    save_process_checkpoints: true   # 是否保存过程权重
    save_every_n_epochs: 2000          # 过程权重保存间隔（轮数）
    save_best_only: true            # 是否只保存最佳模型
    max_checkpoints_to_keep: 1       # 最多保留多少个过程检查点

# 实验配置
experiment:
  name: "cddpm_fault_diagnosis"  # 实验名称
  description: "一维振动信号故障诊断数据增强实验"
  tags: ["CDDPM", "fault_diagnosis", "vibration_signal"]

  # 智能重用扩散模型（自动启用）
  # 程序会自动检测配置中的多参数并智能重用扩散模型
  # 对比实验通过列表参数自动触发，例如：
  # augmentation:
  #   num_generated_per_class: [10, 20, 30]  # 自动检测并重用扩散模型
  # dataset:
  #   data_loading:
  #     fault_samples:
  #       max_fault_samples_per_class: [50, 100]  # 不同原始样本数量

  # 结果保存配置
  results:
    save_individual: true       # 保存单次实验结果
    save_comparison_csv: true   # 保存对比实验汇总CSV
    save_plots_csv: true        # 保存绘图数据的CSV文件
    create_timestamp_folder: true  # 创建时间戳文件夹

# 性能模式配置 - 只调整系统性能参数，保持网络结构一致
performance_profiles:
  # 标准模式 - 适用于大多数GPU (GTX 1060, RTX 2060, 4060等)
  standard:
    training:
      diffusion:
        batch_size: 32          # 小批次，适合显存较小的GPU
      classifier:
        batch_size: 32          # 小批次，适合显存较小的GPU
    system:
      num_workers: 16            # 较少的数据加载进程
      pin_memory: true          # 启用内存固定
      optimization:
        use_amp: false          # 不使用混合精度
        compile_model: false    # 不使用模型编译
        channels_last: false    # 不使用channels_last
        benchmark: true         # 启用cuDNN benchmark

  # 高性能模式 - 适用于高端GPU (RTX 3070, 3080, 4070, 4080等)
  high_performance:
    training:
      diffusion:
        batch_size: 64          # 中等批次
      classifier:
        batch_size: 64          # 中等批次
    system:
      num_workers: 16            # 更多数据加载进程
      pin_memory: true          # 启用内存固定
      optimization:
        use_amp: true           # 启用混合精度训练
        compile_model: true     # 启用PyTorch 2.0编译优化
        channels_last: true     # 启用channels_last内存格式
        benchmark: true         # 启用cuDNN benchmark

  # 超高性能模式 - 适用于顶级GPU (RTX 4090, A100等)
  ultra:
    training:
      diffusion:
        batch_size: 128          # 大批次，充分利用显存
      classifier:
        batch_size: 128         # 大批次，充分利用显存
    system:
      num_workers: 8           # 最多数据加载进程
      pin_memory: true          # 启用内存固定
      optimization:
        use_amp: true           # 启用混合精度训练
        compile_model: true     # 启用PyTorch 2.0编译优化
        channels_last: true     # 启用channels_last内存格式
        benchmark: true         # 启用cuDNN benchmark
