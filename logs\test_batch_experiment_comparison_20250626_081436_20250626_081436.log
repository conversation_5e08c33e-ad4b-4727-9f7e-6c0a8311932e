2025-06-26 08:14:36,496 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_batch_experiment_comparison_20250626_081436_20250626_081436.log
2025-06-26 08:14:36,497 - common.experiment_manager - INFO - 检测到训练数据参数变化，需要重新训练扩散模型
2025-06-26 08:14:36,497 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,497 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:14:36,497 - __main__ - INFO - 实验名称: test_batch_experiment
2025-06-26 08:14:36,497 - __main__ - INFO - 数据集: KAT
2025-06-26 08:14:36,497 - __main__ - INFO - 总实验数: 4
2025-06-26 08:14:36,497 - __main__ - INFO - 对比参数: ['dataset.data_loading.fault_samples.max_fault_samples_per_class', 'augmentation.num_generated_per_class']
2025-06-26 08:14:36,497 - __main__ - INFO - 🔥 扩散模型重用: 否
2025-06-26 08:14:36,497 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,499 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_081436
2025-06-26 08:14:36,499 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_081436\configs\original_config.yaml
2025-06-26 08:14:36,500 - __main__ - INFO - 🔧 使用传统独立实验模式
2025-06-26 08:14:36,500 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,500 - __main__ - INFO - 开始实验 1/4
2025-06-26 08:14:36,500 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 5}
2025-06-26 08:14:36,500 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,504 - __main__ - INFO - 缓存配置已保存: cache\20250626_081436\experiment_001_config.yaml
2025-06-26 08:14:36,504 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:14:36,504 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,504 - __main__ - INFO - 当前实验配置
2025-06-26 08:14:36,505 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,505 - __main__ - INFO - 数据集: KAT
2025-06-26 08:14:36,505 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:14:36,505 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:14:36,505 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:14:36,505 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:14:36,505 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:14:36,505 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:14:36,506 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:14:36,506 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:14:36,506 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:14:36,506 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:14:36,506 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:14:36,506 - __main__ - INFO - 设备: auto
2025-06-26 08:14:36,506 - __main__ - INFO - 性能模式: auto
2025-06-26 08:14:36,506 - __main__ - INFO - 随机种子: 42
2025-06-26 08:14:36,506 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,508 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:14:36,510 - __main__ - INFO - 加载数据...
2025-06-26 08:14:36,510 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:14:36,510 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:14:36,519 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,526 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,526 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,526 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,526 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:14:36,527 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:14:36,528 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,532 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:14:36,537 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:14:36,537 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:14:36,537 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:14:36,537 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:14:36,537 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:14:36,539 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,539 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:14:36,539 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,541 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:14:36,541 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:14:36,541 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:14:36,541 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:14:36,541 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:14:36,543 - __main__ - ERROR - 实验 1 失败: 'diffusion'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1636, in run_experiments_independently
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1083, in run_experiment
    augmentation_results = generator.train_method(train_loader_split, val_loader)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 703, in train_method
    self.initialize_method()
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 694, in initialize_method
    self.augmentation_method = UnifiedAugmentationInterface(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 416, in __init__
    self.method = AugmentationFactory.create_method(method_name, config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 335, in create_method
    return CDDPMWrapper(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 36, in __init__
    self.model = CDDPM(config).to(device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\cddpm.py", line 118, in __init__
    self.denoise_model = UNet1D(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\unet.py", line 200, in __init__
    model_config = config['models']['diffusion']
KeyError: 'diffusion'
2025-06-26 08:14:36,545 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,545 - __main__ - INFO - 开始实验 2/4
2025-06-26 08:14:36,545 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 8}
2025-06-26 08:14:36,546 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,550 - __main__ - INFO - 缓存配置已保存: cache\20250626_081436\experiment_002_config.yaml
2025-06-26 08:14:36,550 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:14:36,550 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,551 - __main__ - INFO - 当前实验配置
2025-06-26 08:14:36,551 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,551 - __main__ - INFO - 数据集: KAT
2025-06-26 08:14:36,551 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:14:36,551 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:14:36,551 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:14:36,552 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:14:36,552 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:14:36,552 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:14:36,552 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:14:36,552 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:14:36,552 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:14:36,552 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:14:36,552 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:14:36,553 - __main__ - INFO - 设备: auto
2025-06-26 08:14:36,553 - __main__ - INFO - 性能模式: auto
2025-06-26 08:14:36,553 - __main__ - INFO - 随机种子: 42
2025-06-26 08:14:36,553 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,554 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:14:36,555 - __main__ - INFO - 加载数据...
2025-06-26 08:14:36,555 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:14:36,555 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:14:36,558 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,561 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,561 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,561 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,561 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:14:36,562 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:14:36,562 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,562 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,562 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,562 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,562 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,563 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,563 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:14:36,563 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:14:36,568 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:14:36,569 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:14:36,570 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:14:36,570 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:14:36,570 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:14:36,570 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:14:36,571 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,571 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:14:36,571 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,571 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:14:36,572 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:14:36,572 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:14:36,572 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:14:36,572 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:14:36,572 - __main__ - ERROR - 实验 2 失败: 'diffusion'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1636, in run_experiments_independently
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1083, in run_experiment
    augmentation_results = generator.train_method(train_loader_split, val_loader)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 703, in train_method
    self.initialize_method()
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 694, in initialize_method
    self.augmentation_method = UnifiedAugmentationInterface(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 416, in __init__
    self.method = AugmentationFactory.create_method(method_name, config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 335, in create_method
    return CDDPMWrapper(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 36, in __init__
    self.model = CDDPM(config).to(device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\cddpm.py", line 118, in __init__
    self.denoise_model = UNet1D(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\unet.py", line 200, in __init__
    model_config = config['models']['diffusion']
KeyError: 'diffusion'
2025-06-26 08:14:36,573 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,573 - __main__ - INFO - 开始实验 3/4
2025-06-26 08:14:36,574 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 5}
2025-06-26 08:14:36,574 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,580 - __main__ - INFO - 缓存配置已保存: cache\20250626_081436\experiment_003_config.yaml
2025-06-26 08:14:36,580 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:14:36,580 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,581 - __main__ - INFO - 当前实验配置
2025-06-26 08:14:36,581 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,581 - __main__ - INFO - 数据集: KAT
2025-06-26 08:14:36,581 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:14:36,581 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:14:36,581 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:14:36,581 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:14:36,582 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:14:36,582 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:14:36,582 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:14:36,582 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:14:36,582 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:14:36,582 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:14:36,582 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:14:36,583 - __main__ - INFO - 设备: auto
2025-06-26 08:14:36,583 - __main__ - INFO - 性能模式: auto
2025-06-26 08:14:36,583 - __main__ - INFO - 随机种子: 42
2025-06-26 08:14:36,583 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,583 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:14:36,584 - __main__ - INFO - 加载数据...
2025-06-26 08:14:36,584 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:14:36,585 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:14:36,587 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,591 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,591 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,591 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,591 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 08:14:36,591 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,592 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,593 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 08:14:36,593 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,598 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:14:36,599 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:14:36,599 - common.data_loader - INFO -   训练样本: 24
2025-06-26 08:14:36,600 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:14:36,600 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:14:36,600 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:14:36,601 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,601 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:14:36,601 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,601 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:14:36,602 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:14:36,602 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:14:36,602 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:14:36,602 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:14:36,603 - __main__ - ERROR - 实验 3 失败: 'diffusion'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1636, in run_experiments_independently
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1083, in run_experiment
    augmentation_results = generator.train_method(train_loader_split, val_loader)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 703, in train_method
    self.initialize_method()
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 694, in initialize_method
    self.augmentation_method = UnifiedAugmentationInterface(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 416, in __init__
    self.method = AugmentationFactory.create_method(method_name, config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 335, in create_method
    return CDDPMWrapper(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 36, in __init__
    self.model = CDDPM(config).to(device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\cddpm.py", line 118, in __init__
    self.denoise_model = UNet1D(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\unet.py", line 200, in __init__
    model_config = config['models']['diffusion']
KeyError: 'diffusion'
2025-06-26 08:14:36,603 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,603 - __main__ - INFO - 开始实验 4/4
2025-06-26 08:14:36,604 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 8}
2025-06-26 08:14:36,604 - __main__ - INFO - ============================================================
2025-06-26 08:14:36,608 - __main__ - INFO - 缓存配置已保存: cache\20250626_081436\experiment_004_config.yaml
2025-06-26 08:14:36,608 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:14:36,608 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,608 - __main__ - INFO - 当前实验配置
2025-06-26 08:14:36,608 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,608 - __main__ - INFO - 数据集: KAT
2025-06-26 08:14:36,608 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:14:36,608 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:14:36,608 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:14:36,609 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:14:36,609 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:14:36,609 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:14:36,609 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:14:36,609 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:14:36,609 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:14:36,609 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:14:36,609 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:14:36,610 - __main__ - INFO - 设备: auto
2025-06-26 08:14:36,610 - __main__ - INFO - 性能模式: auto
2025-06-26 08:14:36,610 - __main__ - INFO - 随机种子: 42
2025-06-26 08:14:36,610 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,610 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:14:36,611 - __main__ - INFO - 加载数据...
2025-06-26 08:14:36,611 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:14:36,612 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:14:36,615 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,618 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:14:36,618 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,618 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:14:36,618 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 08:14:36,618 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,619 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,620 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 08:14:36,620 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:14:36,624 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:14:36,625 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:14:36,625 - common.data_loader - INFO -   训练样本: 24
2025-06-26 08:14:36,625 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:14:36,626 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:14:36,626 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:14:36,626 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,627 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:14:36,627 - __main__ - INFO - ==================================================
2025-06-26 08:14:36,627 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:14:36,628 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:14:36,628 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:14:36,628 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:14:36,628 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:14:36,628 - __main__ - ERROR - 实验 4 失败: 'diffusion'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1636, in run_experiments_independently
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1083, in run_experiment
    augmentation_results = generator.train_method(train_loader_split, val_loader)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 703, in train_method
    self.initialize_method()
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 694, in initialize_method
    self.augmentation_method = UnifiedAugmentationInterface(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 416, in __init__
    self.method = AugmentationFactory.create_method(method_name, config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 335, in create_method
    return CDDPMWrapper(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 36, in __init__
    self.model = CDDPM(config).to(device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\cddpm.py", line 118, in __init__
    self.denoise_model = UNet1D(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\unet.py", line 200, in __init__
    model_config = config['models']['diffusion']
KeyError: 'diffusion'
2025-06-26 08:14:36,629 - common.results_manager - WARNING - 没有结果数据可保存
2025-06-26 08:14:36,629 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,629 - __main__ - INFO - 对比实验全部完成，总用时: 00:00
2025-06-26 08:14:36,629 - __main__ - INFO - 结果保存在: results\KAT\20250626_081436
2025-06-26 08:14:36,630 - __main__ - INFO - 缓存配置保存在: cache\20250626_081436
2025-06-26 08:14:36,630 - __main__ - INFO - ================================================================================
2025-06-26 08:14:36,630 - __main__ - INFO - 实验完成
2025-06-26 08:14:36,630 - __main__ - INFO - 程序结束
