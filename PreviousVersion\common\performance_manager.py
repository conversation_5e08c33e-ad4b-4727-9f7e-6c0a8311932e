"""
性能模式管理器
根据硬件配置自动选择或手动指定性能模式
"""

import torch
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class PerformanceManager:
    """性能模式管理器"""
    
    def __init__(self):
        self.gpu_memory_gb = 0
        self.gpu_name = ""
        self.cuda_available = torch.cuda.is_available()
        
        if self.cuda_available:
            self.gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            self.gpu_name = torch.cuda.get_device_name(0)
    
    def detect_optimal_mode(self) -> str:
        """自动检测最优性能模式 - 基于显存大小和GPU型号"""
        if not self.cuda_available:
            return "standard"

        gpu_name_lower = self.gpu_name.lower()

        # 超高性能模式 - 顶级GPU (20GB+显存)
        if any(gpu in gpu_name_lower for gpu in ["4090", "a100", "h100", "v100"]):
            if self.gpu_memory_gb >= 20:
                return "ultra"

        # 高性能模式 - 高端GPU (10GB+显存)
        if any(gpu in gpu_name_lower for gpu in ["4080", "4070", "3090", "3080", "a6000"]):
            if self.gpu_memory_gb >= 10:
                return "high_performance"

        # 根据显存大小决定
        if self.gpu_memory_gb >= 16:
            return "ultra"
        elif self.gpu_memory_gb >= 10:
            return "high_performance"
        else:
            return "standard"
    
    def apply_performance_mode(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用性能模式配置"""
        performance_mode = config['system']['performance_mode']
        
        # 自动模式
        if performance_mode == "auto":
            performance_mode = self.detect_optimal_mode()
            logger.info(f"自动检测性能模式: {performance_mode}")
            logger.info(f"GPU: {self.gpu_name} ({self.gpu_memory_gb:.1f}GB)")
        
        # 检查是否有对应的性能配置
        if performance_mode not in config.get('performance_profiles', {}):
            logger.warning(f"性能模式 '{performance_mode}' 不存在，使用标准模式")
            performance_mode = "standard"
        
        # 应用性能配置
        profile = config['performance_profiles'][performance_mode]
        config = self._merge_config(config, profile)
        
        # 设置优化参数
        self._setup_optimizations(config, performance_mode)
        
        logger.info(f"应用性能模式: {performance_mode}")
        self._log_performance_settings(config, performance_mode)
        
        return config
    
    def _merge_config(self, base_config: Dict[str, Any], profile: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        def deep_merge(base: Dict, override: Dict) -> Dict:
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        return deep_merge(base_config, profile)
    
    def _setup_optimizations(self, config: Dict[str, Any], mode: str):
        """设置优化参数"""
        opt_config = config['system']['optimization']
        
        # 根据模式设置优化
        if mode in ["high_performance", "ultra"]:
            # 启用高性能优化
            if hasattr(torch.backends.cudnn, 'benchmark'):
                torch.backends.cudnn.benchmark = opt_config.get('benchmark', True)
            
            if hasattr(torch.backends.cuda, 'matmul') and opt_config.get('use_amp', False):
                torch.backends.cuda.matmul.allow_tf32 = True
        
        # Windows系统特殊处理
        import platform
        if platform.system() == "Windows":
            if config['system']['num_workers'] > 0:
                logger.warning("Windows系统，将num_workers设为0避免多进程问题")
                config['system']['num_workers'] = 0
            config['system']['pin_memory'] = False
    
    def _log_performance_settings(self, config: Dict[str, Any], mode: str):
        """记录性能设置 - 只显示调整的参数"""
        logger.info("=" * 60)
        logger.info(f"性能模式: {mode.upper()}")
        logger.info("=" * 60)

        # 批次大小 (主要调整参数)
        diff_batch = config['training']['diffusion']['batch_size']
        cls_batch = config['training']['classifier']['batch_size']
        logger.info(f"批次大小: 扩散模型={diff_batch}, 分类器={cls_batch}")

        # 系统设置 (主要调整参数)
        sys_config = config['system']
        logger.info(f"数据加载: workers={sys_config['num_workers']}, pin_memory={sys_config['pin_memory']}")

        # 优化设置 (主要调整参数)
        opt_config = config['system']['optimization']
        optimizations = []
        if opt_config.get('use_amp', False):
            optimizations.append("混合精度")
        if opt_config.get('compile_model', False):
            optimizations.append("模型编译")
        if opt_config.get('channels_last', False):
            optimizations.append("channels_last")
        if opt_config.get('benchmark', False):
            optimizations.append("cuDNN benchmark")

        if optimizations:
            logger.info(f"优化特性: {', '.join(optimizations)}")
        else:
            logger.info(f"优化特性: 仅cuDNN benchmark")

        logger.info("=" * 60)


def apply_performance_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """应用性能配置的便捷函数"""
    manager = PerformanceManager()
    return manager.apply_performance_mode(config)


def get_recommended_mode() -> str:
    """获取推荐的性能模式"""
    manager = PerformanceManager()
    return manager.detect_optimal_mode()


def log_gpu_info():
    """记录GPU信息"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        logger.info(f"检测到GPU: {gpu_name} ({memory_gb:.1f}GB)")
        
        recommended = get_recommended_mode()
        logger.info(f"推荐性能模式: {recommended}")
    else:
        logger.info("未检测到CUDA GPU，使用CPU模式")
