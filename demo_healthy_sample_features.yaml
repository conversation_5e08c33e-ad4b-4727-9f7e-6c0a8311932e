# 新健康样本处理功能完整演示配置
# 展示所有新功能：配置验证、多种健康样本处理模式、批量实验

# 数据集配置
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载参数
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    # 故障样本配置 - 支持批量实验
    fault_samples:
      max_fault_samples_per_class: [2, 3]  # 多值对比实验

    # 健康样本处理配置 - 新功能
    healthy_samples:
      max_healthy_samples: -1         # 健康样本数量控制：
                                      #   -1: 与故障样本数量保持一致（动态匹配）
                                      #   0: 扩散训练中不使用健康样本
                                      #   >0: 使用指定数量的健康样本
      healthy_label: 0                # 健康样本的标签值（通常为0）

# 数据增强配置
augmentation:
  method: "CDDPM"
  num_generated_per_class: [5, 8]    # 多值对比实验
  save_generated: true
  
  # 生成样本控制 - 新功能
  generate_fault_only: false         # 生成样本控制：
                                      #   true: 只生成故障样本，不生成健康样本
                                      #   false: 生成故障样本+健康样本（数量与num_generated_per_class一致）
  
  # 分类器训练的健康样本配置 - 新功能
  classifier_healthy_samples:
    use_real_when_no_generated: true    # 当没有生成健康样本时，是否使用真实健康样本
    real_healthy_count: -1              # 使用真实健康样本的数量：
                                        #   -1: 与故障样本数量保持一致
                                        #   0: 不使用健康样本（不推荐）
                                        #   >0: 使用指定数量

  # CDDPM参数 - 包含Classifier-Free Guidance
  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02

    # Classifier-Free Guidance 参数（论文中的条件/无条件混合训练）
    unconditional_prob: 0.1    # 无条件训练概率（10%的样本进行无条件训练）
    guidance_scale: 1.0        # 引导强度（1.0=标准条件生成，>1.0=增强条件控制）

# 模型配置
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  unet:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# 训练配置
training:
  diffusion:
    epochs: 1                   # 快速演示
    batch_size: 8
    learning_rate: 0.0001
    weight_decay: 0.01
    scheduler:
      type: "cosine"
      T_max: 1
      eta_min: 0.00001
    early_stopping:
      enabled: false
      patience: 50
      min_delta: 0.001
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

  classifier:
    epochs: 10                  # 快速演示
    batch_size: 16
    learning_rate: 0.0001
    weight_decay: 0.01
    scheduler:
      type: "cosine"
      T_max: 10
      eta_min: 0.00001
    early_stopping:
      enabled: false
      patience: 20
      min_delta: 0.001
      monitor: "train_loss"
      mode: "min"
      restore_best_weights: true

# 评估配置
evaluation:
  metrics:
    classification: true
    generation: false           # 快速演示

# 系统配置
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 1

# 实验配置
experiment:
  name: "demo_healthy_sample_features"
  description: "演示新的健康样本处理功能：配置验证、多种处理模式、批量实验"
  tags: ["CDDPM", "healthy_samples", "batch_experiments", "config_validation"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# 性能模式配置
performance_profiles:
  standard:
    training:
      diffusion:
        batch_size: 8
      classifier:
        batch_size: 16
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true
