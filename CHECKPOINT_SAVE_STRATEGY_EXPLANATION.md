# 检查点保存策略说明

## 📋 问题分析

用户发现了原始配置中的逻辑问题：

### **原始配置（有问题）**：
```yaml
save_process_checkpoints: false    # 是否保存过程权重
save_every_n_epochs: 5000         # 过程权重保存间隔（轮数）
save_best_only: true              # 是否只保存最佳模型
max_checkpoints_to_keep: 3        # 最多保留多少个过程检查点
```

### **逻辑冲突**：
1. `save_process_checkpoints: false` - 不保存过程权重
2. `save_best_only: true` - 只保存最佳模型
3. **冲突点**：如果只保存最佳模型，那么"是否保存过程权重"这个选项就没有意义了

## ✅ 优化后的配置

### **新配置（逻辑清晰）**：
```yaml
save_best_only: true              # 是否只保存最佳模型（true=只保存最佳，false=保存过程+最佳）
save_every_n_epochs: 1000         # 过程检查点保存间隔（仅当save_best_only=false时生效）
max_checkpoints_to_keep: 3        # 最多保留多少个过程检查点（仅当save_best_only=false时生效）
```

## 🔧 配置逻辑说明

### **模式1：只保存最佳模型（推荐）**
```yaml
save_best_only: true              # 启用最佳模型保存模式
# save_every_n_epochs 和 max_checkpoints_to_keep 在此模式下不生效
```

**行为**：
- ✅ 只保存训练过程中验证性能最佳的模型
- ✅ 节省磁盘空间
- ✅ 避免检查点文件过多
- ✅ 适合大多数实验场景

**保存的文件**：
```
checkpoints/
├── diffusion/
│   └── best_model.pth            # 最佳扩散模型
└── classifier/
    └── best_model.pth            # 最佳分类器模型
```

### **模式2：保存过程检查点+最佳模型**
```yaml
save_best_only: false             # 禁用最佳模型保存模式
save_every_n_epochs: 100          # 每100轮保存一次过程检查点
max_checkpoints_to_keep: 5        # 最多保留5个过程检查点
```

**行为**：
- ✅ 保存训练过程中的定期检查点
- ✅ 保存最佳模型
- ✅ 自动清理旧的过程检查点
- ✅ 适合长时间训练和实验分析

**保存的文件**：
```
checkpoints/
├── diffusion/
│   ├── best_model.pth            # 最佳扩散模型
│   ├── epoch_100.pth             # 第100轮检查点
│   ├── epoch_200.pth             # 第200轮检查点
│   ├── epoch_300.pth             # 第300轮检查点
│   ├── epoch_400.pth             # 第400轮检查点
│   └── epoch_500.pth             # 第500轮检查点（最多保留5个）
└── classifier/
    ├── best_model.pth            # 最佳分类器模型
    ├── epoch_10.pth              # 第10轮检查点
    ├── epoch_20.pth              # 第20轮检查点
    ├── epoch_30.pth              # 第30轮检查点
    ├── epoch_40.pth              # 第40轮检查点
    └── epoch_50.pth              # 第50轮检查点（最多保留5个）
```

## 🎯 使用建议

### **推荐配置（大多数情况）**：
```yaml
save_best_only: true              # 只保存最佳模型
```

**适用场景**：
- ✅ 常规实验和对比分析
- ✅ 磁盘空间有限
- ✅ 只关心最终性能
- ✅ 批量实验

### **高级配置（特殊需求）**：
```yaml
save_best_only: false             # 保存过程检查点
save_every_n_epochs: 50           # 每50轮保存一次
max_checkpoints_to_keep: 10       # 保留10个检查点
```

**适用场景**：
- ✅ 需要分析训练过程
- ✅ 长时间训练（防止意外中断）
- ✅ 研究训练动态
- ✅ 磁盘空间充足

## 📊 磁盘空间对比

### **只保存最佳模型**：
```
扩散模型：~50MB × 1 = 50MB
分类器：~10MB × 1 = 10MB
总计：~60MB per experiment
```

### **保存过程检查点（每50轮，保留10个）**：
```
扩散模型：~50MB × 11 = 550MB  (10个过程 + 1个最佳)
分类器：~10MB × 11 = 110MB    (10个过程 + 1个最佳)
总计：~660MB per experiment
```

## 🔄 配置切换

### **从过程保存切换到只保存最佳**：
```yaml
# 修改前
save_best_only: false
save_every_n_epochs: 100
max_checkpoints_to_keep: 5

# 修改后
save_best_only: true
# save_every_n_epochs 和 max_checkpoints_to_keep 自动忽略
```

### **从只保存最佳切换到过程保存**：
```yaml
# 修改前
save_best_only: true

# 修改后
save_best_only: false
save_every_n_epochs: 100          # 需要设置保存间隔
max_checkpoints_to_keep: 5        # 需要设置保留数量
```

## 🎉 总结

通过这次优化：

1. **✅ 消除了逻辑冲突**：移除了冗余的 `save_process_checkpoints` 参数
2. **✅ 简化了配置**：只需要一个 `save_best_only` 参数控制保存模式
3. **✅ 提高了清晰度**：每个参数的作用和生效条件都很明确
4. **✅ 保持了灵活性**：支持两种保存模式满足不同需求
5. **✅ 优化了默认值**：默认只保存最佳模型，节省空间

**新的保存策略更加合理、清晰和易用！** 🚀
