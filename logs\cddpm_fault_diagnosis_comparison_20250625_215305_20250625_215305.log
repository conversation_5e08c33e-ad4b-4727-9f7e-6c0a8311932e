2025-06-25 21:53:05,356 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250625_215305_20250625_215305.log
2025-06-25 21:53:05,357 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-25 21:53:05,357 - __main__ - INFO - ================================================================================
2025-06-25 21:53:05,358 - __main__ - INFO - 检测到对比实验配置
2025-06-25 21:53:05,358 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-25 21:53:05,359 - __main__ - INFO - 数据集: KAT
2025-06-25 21:53:05,359 - __main__ - INFO - 总实验数: 2
2025-06-25 21:53:05,360 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-25 21:53:05,360 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-25 21:53:05,360 - __main__ - INFO - ================================================================================
2025-06-25 21:53:05,366 - __main__ - INFO - 缓存配置文件保存在: cache\20250625_215305
2025-06-25 21:53:05,394 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250625_215305\configs\original_config.yaml
2025-06-25 21:53:05,395 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-25 21:53:05,395 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-25 21:53:05,396 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=4_healthy=-1_length=1024
2025-06-25 21:53:05,396 - __main__ - INFO - ======================================================================
2025-06-25 21:53:05,396 - __main__ - INFO - 处理实验组: dataset=KAT_fault=4_healthy=-1_length=1024
2025-06-25 21:53:05,397 - __main__ - INFO - 该组包含 2 个实验
2025-06-25 21:53:05,397 - __main__ - INFO - ======================================================================
2025-06-25 21:53:05,398 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-25 21:53:05,398 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 21:53:05,399 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 21:53:05,440 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:53:05,480 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:53:05,481 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:53:05,481 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:53:05,481 - common.data_loader - INFO - 健康样本数量自动设置为与故障样本相同: 4
2025-06-25 21:53:05,482 - common.data_loader - INFO - 样本配置: 故障样本每类最多4个, 健康样本最多4个
2025-06-25 21:53:05,482 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-25 21:53:05,483 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,483 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,484 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,484 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,485 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,485 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,486 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 4 个
2025-06-25 21:53:05,486 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:53:05,490 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 21:53:05,495 - common.data_loader - INFO - 数据加载完成:
2025-06-25 21:53:05,496 - common.data_loader - INFO -   训练样本: 32
2025-06-25 21:53:05,496 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 21:53:05,497 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 21:53:05,498 - common.data_loader - INFO -   类别数: 8
2025-06-25 21:53:05,499 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 21:53:05,499 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 21:53:05,500 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 21:53:05,500 - models.cddpm - INFO -   类别数量: 8
2025-06-25 21:53:05,734 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 21:53:05,735 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 21:53:05,735 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 21:53:05,736 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 21:53:05,876 - __main__ - ERROR - 实验失败: 'scheduler'
Traceback (most recent call last):
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1767, in main
    results = run_comparison_experiments(args.config)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1576, in run_comparison_experiments
    return run_experiments_with_diffusion_reuse(
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1606, in run_experiments_with_diffusion_reuse
    diffusion_model_path = train_diffusion_model_once(base_config)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1836, in train_diffusion_model_once
    diffusion_trainer = DiffusionTrainer(config, device)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 78, in __init__
    self._setup_scheduler()
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 100, in _setup_scheduler
    scheduler_config = self.config['training']['diffusion']['scheduler']
KeyError: 'scheduler'
