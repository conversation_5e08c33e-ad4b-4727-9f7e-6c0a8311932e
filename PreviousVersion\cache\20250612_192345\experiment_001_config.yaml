# 自动生成的实验配置文件
# 实验 1/2
# 时间戳: 20250612_192345
# 参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 5}

_config_path: config.yaml
augmentation:
  cddpm:
    beta_end: 0.02
    beta_schedule: linear
    beta_start: 0.0001
    timesteps: 1000
  cgan:
    beta1: 0.5
    discriminator_lr: 0.0002
    epochs: 1
    generator_lr: 0.0002
    latent_dim: 100
  cvae:
    beta: 1.0
    epochs: 1
    hidden_dim: 512
    latent_dim: 100
    learning_rate: 0.0001
  dcgan:
    beta1: 0.5
    discriminator_lr: 0.0002
    epochs: 1
    generator_lr: 0.0002
    latent_dim: 100
  ddpm:
    beta_end: 0.02
    beta_schedule: linear
    beta_start: 0.0001
    epochs: 1
    learning_rate: 0.0001
    timesteps: 1000
  generate_fault_only: true
  method: CGAN
  num_generated_per_class: 2
  save_generated: true
  traditional:
    adasyn:
      n_neighbors: 5
      random_state: 42
      sampling_strategy: auto
    kmeans_smote:
      cluster_balance_threshold: 0.005
      k_neighbors: 5
      random_state: 42
      sampling_strategy: auto
    smoteenn:
      random_state: 42
      sampling_strategy: auto
  wgan:
    clip_value: 0.01
    critic_lr: 5.0e-05
    epochs: 1
    generator_lr: 5.0e-05
    latent_dim: 100
    n_critic: 5
  wgan_gp:
    critic_lr: 0.0001
    epochs: 1
    generator_lr: 0.0001
    lambda_gp: 10
    latent_dim: 100
    n_critic: 5
dataset:
  data_loading:
    fault_samples:
      max_fault_samples_per_class: 5
    healthy_samples:
      healthy_label: 0
      max_healthy_samples: 10
    normalization_method: minmax
    normalize: true
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.8
  datasets:
    JST:
      class_names:
      - '0'
      - '1'
      - '2'
      num_classes: 3
    KAT:
      class_names:
      - K001
      - KA01
      - KA05
      - KA09
      - KI01
      - KI03
      - KI05
      - KI08
      num_classes: 8
    SK:
      class_names:
      - '0'
      - '1'
      - '2'
      num_classes: 3
  name: KAT
evaluation:
  gan_evaluation:
    batch_size: 64
    classifier_epochs: 10
  metrics:
    classification:
    - accuracy
    - precision
    - recall
    - f1_score
    generation:
    - gan_train
    - gan_test
  visualization:
    num_samples_to_plot: 10
    save_confusion_matrix: true
    save_sample_plots: true
    save_tsne: true
    tsne_n_iter: 1000
    tsne_perplexity: 30
experiment:
  comparison:
    auto_detect: true
    enabled: false
    methods:
    - CGAN
    - CWGAN
    - VAE
  current_experiment:
    index: 1
    parameters:
      dataset.data_loading.fault_samples.max_fault_samples_per_class: 5
    timestamp: '20250612_192345'
    total: 2
  description: 一维振动信号故障诊断数据增强实验
  name: cddpm_fault_diagnosis
  results:
    create_timestamp_folder: true
    save_comparison_csv: true
    save_individual: true
    save_plots_csv: true
  tags:
  - CDDPM
  - fault_diagnosis
  - vibration_signal
models:
  MRCNN:
    activation: relu
    base_channels: 64
    kernel_sizes:
    - 3
    - 5
    - 7
    num_layers: 3
    pool_size: 2
    use_batch_norm: true
  classifier:
    base_channels: 64
    dropout: 0.2
    kernel_sizes:
    - 3
    - 5
    - 7
    model_type: MRCNN
  diffusion:
    dropout: 0.1
    hidden_dim: 128
    model_type: UNet1D
    num_heads: 8
    num_layers: 5
performance_profiles:
  high_performance:
    system:
      num_workers: 16
      optimization:
        benchmark: true
        channels_last: true
        compile_model: true
        use_amp: true
      pin_memory: true
    training:
      classifier:
        batch_size: 64
      diffusion:
        batch_size: 64
  standard:
    system:
      num_workers: 16
      optimization:
        benchmark: true
        channels_last: false
        compile_model: false
        use_amp: false
      pin_memory: true
    training:
      classifier:
        batch_size: 32
      diffusion:
        batch_size: 32
  ultra:
    system:
      num_workers: 16
      optimization:
        benchmark: true
        channels_last: true
        compile_model: true
        use_amp: true
      pin_memory: true
    training:
      classifier:
        batch_size: 128
      diffusion:
        batch_size: 128
system:
  device: cuda
  num_workers: 0
  optimization:
    benchmark: true
    channels_last: false
    compile_model: false
    use_amp: false
  performance_mode: auto
  pin_memory: false
  save:
    checkpoints_dir: checkpoints
    generated_samples_dir: dataset/{dataset_name}/gen_samples
    logs_dir: logs
    max_checkpoints_to_keep: 1
    results_dir: results
    save_best_only: true
    save_every_n_epochs: 10
    save_process_checkpoints: true
  seed: 42
training:
  classifier:
    batch_size: 32
    early_stopping:
      enabled: false
      min_delta: 0.0001
      monitor: train_loss
      patience: 10
      restore_best_weights: true
    epochs: 1
    learning_rate: 0.0001
    scheduler:
      gamma: 0.5
      step_size: 10
      type: step
    weight_decay: 0.0001
  diffusion:
    batch_size: 32
    early_stopping:
      enabled: false
      min_delta: 0.0001
      monitor: train_loss
      patience: 10
      restore_best_weights: true
    epochs: 1
    learning_rate: 0.0001
    scheduler:
      T_max: 50
      eta_min: 1.0e-05
      gamma: 0.5
      step_size: 20
      type: cosine
    weight_decay: 0.0001
