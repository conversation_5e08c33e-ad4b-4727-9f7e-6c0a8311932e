2025-06-26 09:30:31,554 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_auto_target_samples_20250626_093031_20250626_093031.log
2025-06-26 09:30:31,554 - __main__ - INFO - ================================================================================
2025-06-26 09:30:31,554 - __main__ - INFO - 开始单一实验: test_auto_target_samples
2025-06-26 09:30:31,554 - __main__ - INFO - 数据集: KAT
2025-06-26 09:30:31,554 - __main__ - INFO - ================================================================================
2025-06-26 09:30:31,554 - __main__ - INFO - 🚀 实验开始
2025-06-26 09:30:31,555 - __main__ - INFO - ================================================================================
2025-06-26 09:30:31,555 - __main__ - INFO - 当前实验配置
2025-06-26 09:30:31,555 - __main__ - INFO - ================================================================================
2025-06-26 09:30:31,555 - __main__ - INFO - 数据集: KAT
2025-06-26 09:30:31,555 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 09:30:31,555 - __main__ - INFO - 健康样本总数: -1
2025-06-26 09:30:31,555 - __main__ - INFO - 信号长度: 1024
2025-06-26 09:30:31,555 - __main__ - INFO - 归一化方法: minmax
2025-06-26 09:30:31,555 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 09:30:31,556 - __main__ - INFO - 每类生成样本数: [5]
2025-06-26 09:30:31,556 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:30:31,556 - __main__ - INFO - 扩散模型训练轮数: 20
2025-06-26 09:30:31,556 - __main__ - INFO - 分类器训练轮数: 15
2025-06-26 09:30:31,556 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 09:30:31,556 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 09:30:31,556 - __main__ - INFO - 设备: auto
2025-06-26 09:30:31,556 - __main__ - INFO - 性能模式: auto
2025-06-26 09:30:31,556 - __main__ - INFO - 随机种子: 42
2025-06-26 09:30:31,556 - __main__ - INFO - ================================================================================
2025-06-26 09:30:31,556 - __main__ - INFO - ============================================================
2025-06-26 09:30:31,557 - __main__ - INFO - 健康样本配置验证
2025-06-26 09:30:31,557 - __main__ - INFO - ============================================================
2025-06-26 09:30:31,557 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 09:30:31,557 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:30:31,557 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 09:30:31,557 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 09:30:31,557 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 09:30:31,557 - __main__ - INFO - ============================================================
2025-06-26 09:30:31,558 - __main__ - INFO - 使用设备: cuda
2025-06-26 09:30:31,558 - __main__ - INFO - 加载数据...
2025-06-26 09:30:31,559 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 09:30:31,559 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 09:30:31,565 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:30:31,574 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:30:31,574 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:30:31,574 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:30:31,575 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 09:30:31,575 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 09:30:31,575 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 09:30:31,575 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,575 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 09:30:31,576 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:30:31,585 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 09:30:31,602 - common.data_loader - INFO - 数据加载完成:
2025-06-26 09:30:31,603 - common.data_loader - INFO -   训练样本: 24
2025-06-26 09:30:31,603 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 09:30:31,603 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 09:30:31,603 - common.data_loader - INFO -   类别数: 8
2025-06-26 09:30:31,604 - __main__ - INFO - ==================================================
2025-06-26 09:30:31,604 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 09:30:31,604 - __main__ - INFO - ==================================================
2025-06-26 09:30:31,605 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 09:30:31,605 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:30:31,606 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:30:31,606 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:30:31,606 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:30:31,848 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:30:31,849 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:30:31,849 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:30:31,849 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:30:32,032 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 09:30:32,032 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 09:30:32,032 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共20轮
2025-06-26 09:30:38,566 - models.augmentation_factory - INFO - Epoch   1/20: Train Loss: 0.845048, Val Loss: 0.850046 (Best✓)
2025-06-26 09:30:38,879 - models.augmentation_factory - INFO - Epoch   2/20: Train Loss: 0.827508, Val Loss: 0.805379 (Best✓)
2025-06-26 09:30:39,511 - models.augmentation_factory - INFO - Epoch   4/20: Train Loss: 0.817485, Val Loss: 0.802084 (Best✓)
2025-06-26 09:30:40,138 - models.augmentation_factory - INFO - Epoch   6/20: Train Loss: 0.800449, Val Loss: 0.800834 (Best✓)
2025-06-26 09:30:40,457 - models.augmentation_factory - INFO - Epoch   7/20: Train Loss: 0.802020, Val Loss: 0.798160 (Best✓)
2025-06-26 09:30:41,065 - models.augmentation_factory - INFO - Epoch   9/20: Train Loss: 0.812387, Val Loss: 0.794513 (Best✓)
2025-06-26 09:30:41,370 - models.augmentation_factory - INFO - Epoch  10/20: Train Loss: 0.802259, Val Loss: 0.792535 (Best✓)
2025-06-26 09:30:41,997 - models.augmentation_factory - INFO - Epoch  12/20: Train Loss: 0.798976, Val Loss: 0.792450 (Best✓)
2025-06-26 09:30:42,610 - models.augmentation_factory - INFO - Epoch  14/20: Train Loss: 0.797871, Val Loss: 0.786231 (Best✓)
2025-06-26 09:30:43,546 - models.augmentation_factory - INFO - Epoch  17/20: Train Loss: 0.793419, Val Loss: 0.777352 (Best✓)
2025-06-26 09:30:44,162 - models.augmentation_factory - INFO - Epoch  19/20: Train Loss: 0.789910, Val Loss: 0.773807 (Best✓)
2025-06-26 09:30:44,467 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 09:30:44,998 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 09:30:44,998 - __main__ - INFO - ==================================================
2025-06-26 09:30:44,998 - __main__ - INFO - 生成增强样本
2025-06-26 09:30:44,998 - __main__ - INFO - ==================================================
2025-06-26 09:30:45,508 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:30:45,509 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:30:45,509 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:30:45,509 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:30:45,509 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:30:45,509 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:30:45,678 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:30:45,679 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:30:45,679 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:30:45,679 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:30:46,160 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 09:30:46,161 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [5]
2025-06-26 09:30:46,161 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 09:30:46,161 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 09:30:46,162 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 09:30:46,162 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 09:31:10,659 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 09:31:35,135 - __main__ - INFO - 生成类别 3 的样本...
