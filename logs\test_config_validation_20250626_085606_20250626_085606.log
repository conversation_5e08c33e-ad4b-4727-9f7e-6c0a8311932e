2025-06-26 08:56:06,811 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_config_validation_20250626_085606_20250626_085606.log
2025-06-26 08:56:06,811 - __main__ - INFO - ================================================================================
2025-06-26 08:56:06,812 - __main__ - INFO - 开始单一实验: test_config_validation
2025-06-26 08:56:06,812 - __main__ - INFO - 数据集: KAT
2025-06-26 08:56:06,812 - __main__ - INFO - ================================================================================
2025-06-26 08:56:06,812 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:56:06,812 - __main__ - INFO - ================================================================================
2025-06-26 08:56:06,812 - __main__ - INFO - 当前实验配置
2025-06-26 08:56:06,812 - __main__ - INFO - ================================================================================
2025-06-26 08:56:06,812 - __main__ - INFO - 数据集: KAT
2025-06-26 08:56:06,812 - __main__ - INFO - 故障样本每类: [2]
2025-06-26 08:56:06,812 - __main__ - INFO - 健康样本总数: 0
2025-06-26 08:56:06,812 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:56:06,812 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:56:06,813 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:56:06,813 - __main__ - INFO - 每类生成样本数: [5]
2025-06-26 08:56:06,813 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:56:06,813 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:56:06,813 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:56:06,813 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:56:06,814 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:56:06,814 - __main__ - INFO - 设备: auto
2025-06-26 08:56:06,814 - __main__ - INFO - 性能模式: auto
2025-06-26 08:56:06,814 - __main__ - INFO - 随机种子: 42
2025-06-26 08:56:06,814 - __main__ - INFO - ================================================================================
2025-06-26 08:56:06,814 - __main__ - INFO - ============================================================
2025-06-26 08:56:06,814 - __main__ - INFO - 健康样本配置验证
2025-06-26 08:56:06,814 - __main__ - INFO - ============================================================
2025-06-26 08:56:06,814 - __main__ - INFO - 扩散训练健康样本数量: 0
2025-06-26 08:56:06,814 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:56:06,815 - __main__ - INFO - 分类器使用真实健康样本: False
2025-06-26 08:56:06,815 - __main__ - INFO - 真实健康样本数量: 0
2025-06-26 08:56:06,815 - __main__ - ERROR - 实验失败: 配置错误: 扩散训练中不包含健康样本(max_healthy_samples=0)时，生成阶段必须设置为只生成故障样本(generate_fault_only=true)
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1813, in main
    results = run_comparison_experiments(args.config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1578, in run_comparison_experiments
    return run_single_experiment(base_config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1784, in run_single_experiment
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1114, in run_experiment
    validate_healthy_sample_config(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 73, in validate_healthy_sample_config
    raise ValueError(
ValueError: 配置错误: 扩散训练中不包含健康样本(max_healthy_samples=0)时，生成阶段必须设置为只生成故障样本(generate_fault_only=true)
