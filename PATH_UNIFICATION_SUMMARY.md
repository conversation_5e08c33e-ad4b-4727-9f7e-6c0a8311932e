# 路径统一修复总结

## 📋 问题分析

用户发现了保存和加载权重的路径不一致问题：

### **原始问题**：
```
2025-06-26 11:03:09,397 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 11:03:10,038 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
```

**路径冲突**：
- 保存路径：`checkpoints/augmentation/cddpm/best_model.pth`
- 加载路径：`checkpoints/diffusion/best/best_model.pth`

## ✅ 修复方案

### **统一的检查点路径结构**

```
checkpoints/
├── augmentation/                    # 增强方法统一目录
│   ├── cddpm/
│   │   └── best_model.pth          # CDDPM模型
│   ├── cgan/
│   │   └── best_model.pth          # CGAN模型
│   ├── wgan/
│   │   └── best_model.pth          # WGAN模型
│   ├── adasyn/
│   │   └── best_model.pth          # ADASYN模型（如果需要）
│   └── ...
├── diffusion/                       # 扩散模型训练目录（内部使用）
│   ├── best/
│   │   └── best_model.pth          # 训练时的最佳模型
│   └── process/
│       ├── epoch_100.pth           # 过程检查点
│       └── ...
└── classifier/                      # 分类器目录
    ├── best/
    │   └── best_model.pth          # 最佳分类器
    └── process/
        ├── epoch_10.pth            # 过程检查点
        └── ...
```

### **统一的生成样本路径结构**

```
generated_samples/
└── {dataset_name}/                  # 数据集名称（如KAT）
    ├── CDDPM/
    │   ├── generated_data.npy       # 生成的数据
    │   └── generated_labels.npy     # 生成的标签
    ├── CGAN/
    │   ├── generated_data.npy
    │   └── generated_labels.npy
    ├── WGAN/
    │   ├── generated_data.npy
    │   └── generated_labels.npy
    └── ADASYN/
        ├── generated_data.npy
        └── generated_labels.npy
```

## 🔧 代码修改详情

### **1. 修改加载逻辑优先级**

**文件**: `main.py`

**修改前**：
```python
# 优先从扩散模型路径加载
best_diffusion_path = os.path.join(
    config['system']['save']['checkpoints_dir'],
    'diffusion', 'best', 'best_model.pth'
)
if os.path.exists(best_diffusion_path):
    generator.load_diffusion_model(best_diffusion_path)
else:
    # 备选：从增强方法路径加载
    model_save_path = os.path.join(
        config['system']['save']['checkpoints_dir'],
        'augmentation', method_name.lower(), 'best_model.pth'
    )
```

**修改后**：
```python
# 优先从增强方法路径加载
method_name = config['augmentation']['method']
model_save_path = os.path.join(
    config['system']['save']['checkpoints_dir'],
    'augmentation', method_name.lower(), 'best_model.pth'
)
if os.path.exists(model_save_path):
    generator.load_diffusion_model(model_save_path)
else:
    # 备选：从扩散模型路径加载
    best_diffusion_path = os.path.join(
        config['system']['save']['checkpoints_dir'],
        'diffusion', 'best', 'best_model.pth'
    )
```

### **2. 修改扩散模型重用逻辑**

**文件**: `main.py` - `train_diffusion_model_once` 函数

**修改前**：
```python
# 返回扩散模型路径
best_diffusion_path = os.path.join(
    config['system']['save']['checkpoints_dir'],
    'diffusion', 'best', 'best_model.pth'
)
return best_diffusion_path
```

**修改后**：
```python
# 返回增强方法保存的模型路径（统一路径）
method_name = config['augmentation']['method']
augmentation_model_path = os.path.join(
    config['system']['save']['checkpoints_dir'],
    'augmentation', method_name.lower(), 'best_model.pth'
)

# 将扩散模型复制到增强方法路径（统一管理）
best_diffusion_path = os.path.join(
    config['system']['save']['checkpoints_dir'],
    'diffusion', 'best', 'best_model.pth'
)

if os.path.exists(best_diffusion_path):
    import shutil
    os.makedirs(os.path.dirname(augmentation_model_path), exist_ok=True)
    shutil.copy2(best_diffusion_path, augmentation_model_path)
    logger.info(f"✅ 扩散模型已复制到统一路径: {augmentation_model_path}")

return augmentation_model_path
```

### **3. 更新目录创建逻辑**

**文件**: `common/utils.py` - `create_directories` 函数

**添加**：
```python
# 处理数据增强方法（可能是字符串或列表）
aug_method = config['augmentation']['method']
if isinstance(aug_method, list):
    # 如果是列表，为每个方法创建目录
    for method in aug_method:
        dirs_to_create.append(os.path.join(gen_samples_dir, method))
        # 同时创建增强方法的检查点目录
        dirs_to_create.append(os.path.join(config['system']['save']['checkpoints_dir'], 'augmentation', method.lower()))
else:
    # 如果是字符串，直接创建目录
    dirs_to_create.append(os.path.join(gen_samples_dir, aug_method))
    # 同时创建增强方法的检查点目录
    dirs_to_create.append(os.path.join(config['system']['save']['checkpoints_dir'], 'augmentation', aug_method.lower()))
```

## 🎯 统一后的优势

### **1. 路径一致性**
- ✅ 所有增强方法使用统一的保存路径格式
- ✅ 加载时优先从统一路径查找
- ✅ 避免路径混乱和找不到模型的问题

### **2. 方法对比便利性**
- ✅ 所有方法的模型都在 `checkpoints/augmentation/` 下
- ✅ 便于对比不同方法的模型大小和性能
- ✅ 便于管理和清理模型文件

### **3. 生成样本组织**
- ✅ 按数据集和方法分层组织
- ✅ 路径格式：`generated_samples/{dataset}/{method}/`
- ✅ 便于后续分析和可视化

### **4. 向后兼容性**
- ✅ 仍然支持从旧路径加载模型
- ✅ 渐进式迁移，不会破坏现有实验
- ✅ 自动复制模型到新路径

## 📊 路径对比

### **检查点路径**

| 方法 | 旧路径 | 新路径 | 状态 |
|------|--------|--------|------|
| **CDDPM** | `checkpoints/diffusion/best/best_model.pth` | `checkpoints/augmentation/cddpm/best_model.pth` | ✅ 统一 |
| **CGAN** | `checkpoints/augmentation/cgan/best_model.pth` | `checkpoints/augmentation/cgan/best_model.pth` | ✅ 一致 |
| **WGAN** | `checkpoints/augmentation/wgan/best_model.pth` | `checkpoints/augmentation/wgan/best_model.pth` | ✅ 一致 |
| **ADASYN** | 无需保存 | 无需保存 | ✅ 一致 |

### **生成样本路径**

| 格式 | 路径示例 | 状态 |
|------|----------|------|
| **当前格式** | `generated_samples/KAT/CDDPM/generated_data.npy` | ✅ 合理 |
| **旧格式** | `dataset/KAT/gen_samples/generated_data.npy` | ❌ 已弃用 |

## 🔍 验证方法

### **1. 检查路径一致性**
```bash
# 检查增强方法目录是否创建
ls checkpoints/augmentation/

# 检查生成样本目录结构
ls generated_samples/KAT/
```

### **2. 验证模型加载**
```python
# 运行单一实验，检查日志
python main.py --config config.yaml --mode full

# 查看日志中的模型加载路径
grep "模型已加载" logs/*.log
```

### **3. 验证重用功能**
```python
# 运行对比实验，检查重用逻辑
python main.py --config test_comparison.yaml --mode full

# 查看重用日志
grep "重用扩散模型" logs/*.log
```

## 🎉 总结

通过这次路径统一修复：

1. **✅ 解决了路径不一致问题**：所有增强方法使用统一的保存和加载路径
2. **✅ 提高了代码可维护性**：路径逻辑更加清晰和一致
3. **✅ 便于方法对比**：所有模型在同一目录下，便于管理
4. **✅ 保持了向后兼容性**：仍然支持从旧路径加载模型
5. **✅ 优化了重用逻辑**：扩散模型重用时自动复制到统一路径

**现在的路径结构更加合理、统一和易于管理！** 🚀
