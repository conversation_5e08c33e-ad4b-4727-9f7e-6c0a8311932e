# 论文参数对齐报告

## 📋 概述

根据论文《Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN》中的实验设置，对配置文件中的参数进行了对齐调整。

## 📚 论文参数依据

### **Table V: Training Hyperparameter Settings of Generative Models**

| Model | Optimizer | Learning Rate | Batch Size | Epoch | Loss |
|-------|-----------|---------------|------------|-------|------|
| CGAN | Adam | 0.00001 | 64 | 1000 | BCE |
| WGAN | Adam | 0.00001 | 64 | 1000 | MSE |
| WGAN-GP | Adam | 0.00001 | 64 | 1000 | WD |
| ACGAN | Adam | 0.0002 | 64 | 1000 | hinge_v2 |
| DDPM | Adam | 0.00001 | 64 | 300 | MAE |
| **Proposed** | **Adam** | **0.00001** | **64** | **300** | **MAE** |

### **分类器参数设置**

论文中提到：
> "All models are configured with the same hyperparameters: learning rate of 1×10^-4, batch size of 64, and training duration of 50 epochs."

## ✅ 参数调整详情

### **1. 扩散模型训练参数**

#### **调整前**：
```yaml
diffusion:
  epochs: 100                       # 训练轮数
  batch_size: 32                    # 批次大小
  learning_rate: 0.0001             # 学习率
  weight_decay: 0.0001              # 权重衰减
```

#### **调整后（基于论文Table V）**：
```yaml
diffusion:
  epochs: 300                       # 训练轮数（论文中DDPM和Proposed都是300轮）
  batch_size: 64                    # 批次大小（论文中统一使用64）
  learning_rate: 0.00001            # 学习率（论文中为0.00001，即1e-5）
  weight_decay: 0.0001              # 权重衰减（保持不变）
```

#### **学习率调度器调整**：
```yaml
scheduler:
  T_max: 300                        # cosine调度器的最大epoch（与epochs保持一致）
  eta_min: 0.000001                 # cosine调度器的最小学习率（比学习率小一个数量级）
  step_size: 60                     # step调度器的步长（epochs的1/5）
```

### **2. 分类器训练参数**

#### **调整前**：
```yaml
classifier:
  epochs: 100                       # 训练轮数
  batch_size: 64                    # 批次大小
  learning_rate: 0.0001             # 学习率
  weight_decay: 0.01                # 权重衰减
```

#### **调整后（基于论文实验设置）**：
```yaml
classifier:
  epochs: 50                        # 训练轮数（论文中分类器使用50轮）
  batch_size: 64                    # 批次大小（论文中统一使用64）
  learning_rate: 0.0001             # 学习率（论文中分类器使用1e-4）
  weight_decay: 0.01                # 权重衰减（保持不变）
```

#### **学习率调度器调整**：
```yaml
scheduler:
  T_max: 50                         # cosine调度器的最大epoch（与epochs保持一致）
```

#### **最佳模型判断策略调整**：
```yaml
best_model_criteria:
  metric: "val_loss"                # 改为验证损失（分类器通常使用验证损失防止过拟合）
```

#### **早停配置调整**：
```yaml
early_stopping:
  enabled: true                     # 启用早停
  monitor: "val_loss"               # 监控验证损失
```

### **3. 性能模式配置调整**

#### **标准模式（基于论文参数）**：
```yaml
standard:
  training:
    diffusion:
      batch_size: 64                # 论文中统一使用64
      epochs: 300                   # 论文中扩散模型使用300轮
    classifier:
      batch_size: 64                # 论文中统一使用64
      epochs: 50                    # 论文中分类器使用50轮
```

#### **快速模式（测试用）**：
```yaml
fast:
  training:
    diffusion:
      batch_size: 32                # 较小批次，快速测试
      epochs: 50                    # 减少轮数用于快速测试
    classifier:
      batch_size: 32                # 较小批次，快速测试
      epochs: 20                    # 减少轮数用于快速测试
```

#### **高性能模式（高端GPU）**：
```yaml
high_performance:
  training:
    diffusion:
      batch_size: 128               # 大批次，充分利用GPU
      epochs: 300                   # 论文标准轮数
    classifier:
      batch_size: 128               # 大批次，充分利用GPU
      epochs: 50                    # 论文标准轮数
```

#### **超高性能模式（顶级GPU）**：
```yaml
ultra:
  training:
    diffusion:
      batch_size: 256               # 超大批次，充分利用显存
      epochs: 500                   # 更多轮数获得更好效果
    classifier:
      batch_size: 256               # 超大批次，充分利用显存
      epochs: 100                   # 更多轮数获得更好效果
```

## 📊 参数对比表

| 参数类别 | 参数名称 | 调整前 | 调整后 | 论文依据 |
|---------|---------|--------|--------|----------|
| **扩散模型** | epochs | 100 | **300** | Table V: Proposed=300 |
| | batch_size | 32 | **64** | Table V: 统一使用64 |
| | learning_rate | 0.0001 | **0.00001** | Table V: 0.00001 |
| | T_max | 100 | **300** | 与epochs保持一致 |
| | eta_min | 0.00001 | **0.000001** | 比学习率小一个数量级 |
| **分类器** | epochs | 100 | **50** | 论文：50 epochs |
| | batch_size | 64 | **64** | 论文：batch size of 64 |
| | learning_rate | 0.0001 | **0.0001** | 论文：1×10^-4 |
| | T_max | 100 | **50** | 与epochs保持一致 |
| | best_model_criteria | weighted_loss | **val_loss** | 分类器防止过拟合 |
| | early_stopping.monitor | weighted_loss | **val_loss** | 与最佳模型判断一致 |

## 🎯 关键调整说明

### **1. 扩散模型学习率降低**
- **从 0.0001 → 0.00001**
- **原因**：论文中明确使用0.00001，扩散模型通常需要较小的学习率以确保稳定训练

### **2. 扩散模型训练轮数增加**
- **从 100 → 300**
- **原因**：论文Table V显示Proposed方法使用300轮，扩散模型需要更多轮数收敛

### **3. 分类器训练轮数减少**
- **从 100 → 50**
- **原因**：论文明确提到分类器使用50轮，避免过拟合

### **4. 批次大小统一**
- **扩散模型：32 → 64**
- **原因**：论文中所有模型统一使用batch size=64

### **5. 分类器最佳模型判断策略**
- **从 weighted_loss → val_loss**
- **原因**：分类器通常使用验证损失防止过拟合，这是标准做法

## 🔬 实验验证建议

### **验证步骤**：
1. **使用标准模式**运行完整实验
2. **对比论文结果**：检查是否能达到论文中的性能指标
3. **调整超参数**：如果结果不理想，可以微调学习率或轮数
4. **性能测试**：在不同GPU上测试各种性能模式

### **预期结果**：
- **扩散模型**：应该能生成高质量的故障样本
- **分类器**：在平衡数据集上应该达到98%+的准确率
- **训练稳定性**：损失曲线应该平滑收敛

## 📝 注意事项

### **1. 参数名称保持不变**
- ✅ 只修改参数值，不修改参数名称
- ✅ 保持配置文件结构不变
- ✅ 保持向后兼容性

### **2. 自适应调整**
- 根据具体数据集大小，可能需要微调轮数
- 根据GPU显存，可能需要调整批次大小
- 根据收敛情况，可能需要调整学习率

### **3. 监控指标**
- **扩散模型**：关注训练损失和验证损失的平衡
- **分类器**：关注验证准确率和过拟合情况
- **生成质量**：使用GAN-train/GAN-test评估生成样本质量

## 🎉 总结

通过与论文参数的对齐：

1. **✅ 扩散模型参数**完全按照论文Table V设置
2. **✅ 分类器参数**按照论文实验描述设置  
3. **✅ 性能模式**提供了从快速测试到高性能的完整配置
4. **✅ 保持兼容性**所有参数名称保持不变，只调整数值

这些调整确保了实验结果能够与论文保持一致，同时提供了灵活的配置选项适应不同的硬件环境和实验需求。
