2025-06-25 20:08:44,586 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250625_200844_20250625_200844.log
2025-06-25 20:08:44,586 - __main__ - INFO - ================================================================================
2025-06-25 20:08:44,587 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-25 20:08:44,588 - __main__ - INFO - 数据集: KAT
2025-06-25 20:08:44,588 - __main__ - INFO - ================================================================================
2025-06-25 20:08:44,589 - __main__ - INFO - 🚀 实验开始
2025-06-25 20:08:44,589 - __main__ - INFO - ================================================================================
2025-06-25 20:08:44,590 - __main__ - INFO - 当前实验配置
2025-06-25 20:08:44,590 - __main__ - INFO - ================================================================================
2025-06-25 20:08:44,590 - __main__ - INFO - 数据集: KAT
2025-06-25 20:08:44,591 - __main__ - INFO - 故障样本每类: [3]
2025-06-25 20:08:44,591 - __main__ - INFO - 健康样本总数: 1
2025-06-25 20:08:44,592 - __main__ - INFO - 信号长度: 1024
2025-06-25 20:08:44,592 - __main__ - INFO - 归一化方法: minmax
2025-06-25 20:08:44,593 - __main__ - INFO - 增强方法: CDDPM
2025-06-25 20:08:44,593 - __main__ - INFO - 每类生成样本数: [10]
2025-06-25 20:08:44,594 - __main__ - INFO - 只生成故障样本: True
2025-06-25 20:08:44,594 - __main__ - INFO - 扩散模型训练轮数: 20
2025-06-25 20:08:44,595 - __main__ - INFO - 分类器训练轮数: 200
2025-06-25 20:08:44,595 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-25 20:08:44,596 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-25 20:08:44,596 - __main__ - INFO - 设备: cuda
2025-06-25 20:08:44,597 - __main__ - INFO - 性能模式: auto
2025-06-25 20:08:44,597 - __main__ - INFO - 随机种子: 42
2025-06-25 20:08:44,597 - __main__ - INFO - ================================================================================
2025-06-25 20:08:44,598 - __main__ - INFO - 使用设备: cuda
2025-06-25 20:08:44,636 - __main__ - INFO - 加载数据...
2025-06-25 20:08:44,636 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 20:08:44,637 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 20:08:44,678 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 20:08:44,720 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 20:08:44,721 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 20:08:44,721 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 20:08:44,722 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多1个
2025-06-25 20:08:44,722 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-25 20:08:44,722 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 1 个
2025-06-25 20:08:44,723 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,723 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,724 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,724 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,725 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,725 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-25 20:08:44,726 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:44,730 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 20:08:44,735 - common.data_loader - INFO - 数据加载完成:
2025-06-25 20:08:44,736 - common.data_loader - INFO -   训练样本: 22
2025-06-25 20:08:44,737 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 20:08:44,737 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 20:08:44,738 - common.data_loader - INFO -   类别数: 8
2025-06-25 20:08:44,739 - __main__ - INFO - ==================================================
2025-06-25 20:08:44,740 - __main__ - INFO - 开始训练数据增强模型
2025-06-25 20:08:44,740 - __main__ - INFO - ==================================================
2025-06-25 20:08:44,742 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-25 20:08:44,742 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 20:08:44,743 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 20:08:44,743 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 20:08:44,743 - models.cddpm - INFO -   类别数量: 8
2025-06-25 20:08:44,951 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 20:08:44,952 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 20:08:44,952 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 20:08:44,953 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 20:08:45,087 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-25 20:08:45,088 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-25 20:08:45,089 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共20轮
2025-06-25 20:08:52,971 - models.augmentation_factory - INFO - Epoch   1/20: Train Loss: 0.837560, Val Loss: 0.866048, Weighted Loss: 0.846106 (Best✓)
2025-06-25 20:08:53,274 - models.augmentation_factory - INFO - Epoch   2/20: Train Loss: 0.856659, Val Loss: 0.808877, Weighted Loss: 0.842325 (Best✓)
2025-06-25 20:08:53,519 - models.augmentation_factory - INFO - Epoch   3/20: Train Loss: 0.819554, Val Loss: 0.810295, Weighted Loss: 0.816776 (Best✓)
2025-06-25 20:08:54,032 - models.augmentation_factory - INFO - Epoch   5/20: Train Loss: 0.817410, Val Loss: 0.793510, Weighted Loss: 0.810240 (Best✓)
2025-06-25 20:08:54,557 - models.augmentation_factory - INFO - Epoch   7/20: Train Loss: 0.810916, Val Loss: 0.794410, Weighted Loss: 0.805964 (Best✓)
2025-06-25 20:08:55,098 - models.augmentation_factory - INFO - Epoch   9/20: Train Loss: 0.798863, Val Loss: 0.806947, Weighted Loss: 0.801288 (Best✓)
2025-06-25 20:08:55,656 - models.augmentation_factory - INFO - Epoch  11/20: Train Loss: 0.805723, Val Loss: 0.788065, Weighted Loss: 0.800425 (Best✓)
2025-06-25 20:08:57,718 - models.augmentation_factory - INFO - Epoch  19/20: Train Loss: 0.797722, Val Loss: 0.791820, Weighted Loss: 0.795951 (Best✓)
2025-06-25 20:08:57,965 - models.augmentation_factory - INFO - Epoch  20/20: Train Loss: 0.786503, Val Loss: 0.783682, Weighted Loss: 0.785657 (Best✓)
2025-06-25 20:08:57,966 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-25 20:08:58,488 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-25 20:08:58,489 - __main__ - INFO - ==================================================
2025-06-25 20:08:58,489 - __main__ - INFO - 生成增强样本
2025-06-25 20:08:58,490 - __main__ - INFO - ==================================================
2025-06-25 20:09:05,383 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-25 20:09:05,384 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-25 20:09:05,385 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 20:09:05,386 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 20:09:05,386 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 20:09:05,387 - models.cddpm - INFO -   类别数量: 8
2025-06-25 20:09:05,560 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 20:09:05,561 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 20:09:05,562 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 20:09:05,562 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 20:09:10,242 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-25 20:09:10,248 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [10]
2025-06-25 20:09:10,248 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 10 个...
2025-06-25 20:09:10,250 - __main__ - INFO - 生成类别 0 的样本...
2025-06-25 20:09:53,615 - __main__ - INFO - 生成类别 1 的样本...
2025-06-25 20:10:36,788 - __main__ - INFO - 生成类别 2 的样本...
2025-06-25 20:11:21,840 - __main__ - INFO - 生成类别 3 的样本...
2025-06-25 20:12:06,933 - __main__ - INFO - 生成类别 4 的样本...
