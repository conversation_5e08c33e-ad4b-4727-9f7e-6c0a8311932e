2025-06-25 18:36:43,547 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250625_183643_20250625_183643.log
2025-06-25 18:36:43,548 - __main__ - INFO - ================================================================================
2025-06-25 18:36:43,548 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-25 18:36:43,549 - __main__ - INFO - 数据集: KAT
2025-06-25 18:36:43,549 - __main__ - INFO - ================================================================================
2025-06-25 18:36:43,550 - __main__ - INFO - 🚀 实验开始
2025-06-25 18:36:43,551 - __main__ - INFO - ================================================================================
2025-06-25 18:36:43,551 - __main__ - INFO - 当前实验配置
2025-06-25 18:36:43,551 - __main__ - INFO - ================================================================================
2025-06-25 18:36:43,552 - __main__ - INFO - 数据集: KAT
2025-06-25 18:36:43,552 - __main__ - INFO - 故障样本每类: [9]
2025-06-25 18:36:43,553 - __main__ - INFO - 健康样本总数: 1
2025-06-25 18:36:43,553 - __main__ - INFO - 信号长度: 1024
2025-06-25 18:36:43,554 - __main__ - INFO - 归一化方法: minmax
2025-06-25 18:36:43,554 - __main__ - INFO - 增强方法: CDDPM
2025-06-25 18:36:43,555 - __main__ - INFO - 每类生成样本数: [10]
2025-06-25 18:36:43,555 - __main__ - INFO - 只生成故障样本: True
2025-06-25 18:36:43,556 - __main__ - INFO - 扩散模型训练轮数: 20
2025-06-25 18:36:43,556 - __main__ - INFO - 分类器训练轮数: 200
2025-06-25 18:36:43,556 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-25 18:36:43,557 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-25 18:36:43,557 - __main__ - INFO - 设备: cuda
2025-06-25 18:36:43,558 - __main__ - INFO - 性能模式: auto
2025-06-25 18:36:43,559 - __main__ - INFO - 随机种子: 42
2025-06-25 18:36:43,559 - __main__ - INFO - ================================================================================
2025-06-25 18:36:43,561 - __main__ - INFO - 使用设备: cuda
2025-06-25 18:36:43,601 - __main__ - INFO - 加载数据...
2025-06-25 18:36:43,603 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 18:36:43,603 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 18:36:43,658 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 18:36:43,718 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 18:36:43,719 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 18:36:43,719 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 18:36:43,720 - common.data_loader - INFO - 样本配置: 故障样本每类最多9个, 健康样本最多1个
2025-06-25 18:36:43,721 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-25 18:36:43,721 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 1 个
2025-06-25 18:36:43,722 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,722 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,723 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,724 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,724 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,725 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 9 个
2025-06-25 18:36:43,725 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 9 个
2025-06-25 18:36:43,732 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 18:36:43,746 - common.data_loader - INFO - 数据加载完成:
2025-06-25 18:36:43,748 - common.data_loader - INFO -   训练样本: 64
2025-06-25 18:36:43,748 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 18:36:43,749 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 18:36:43,750 - common.data_loader - INFO -   类别数: 8
2025-06-25 18:36:43,755 - __main__ - INFO - ==================================================
2025-06-25 18:36:43,756 - __main__ - INFO - 开始训练数据增强模型
2025-06-25 18:36:43,756 - __main__ - INFO - ==================================================
2025-06-25 18:36:43,772 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-25 18:36:43,773 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 18:36:43,774 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 18:36:43,775 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 18:36:43,775 - models.cddpm - INFO -   类别数量: 8
2025-06-25 18:36:43,990 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 18:36:43,991 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 18:36:43,992 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 18:36:43,993 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 18:36:44,223 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-25 18:36:44,224 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-25 18:36:44,224 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共20轮
2025-06-25 18:36:51,792 - models.augmentation_factory - INFO - Epoch   1/20: Train Loss: 0.841566, Val Loss: 0.830533, Weighted Loss: 0.838256 (Best✓)
2025-06-25 18:36:52,509 - models.augmentation_factory - INFO - Epoch   2/20: Train Loss: 0.821881, Val Loss: 0.819606, Weighted Loss: 0.821198 (Best✓)
2025-06-25 18:36:53,195 - models.augmentation_factory - INFO - Epoch   3/20: Train Loss: 0.813777, Val Loss: 0.807267, Weighted Loss: 0.811824 (Best✓)
2025-06-25 18:36:53,837 - models.augmentation_factory - INFO - Epoch   4/20: Train Loss: 0.813847, Val Loss: 0.797825, Weighted Loss: 0.809040 (Best✓)
2025-06-25 18:36:54,480 - models.augmentation_factory - INFO - Epoch   5/20: Train Loss: 0.806331, Val Loss: 0.807587, Weighted Loss: 0.806708 (Best✓)
2025-06-25 18:36:55,119 - models.augmentation_factory - INFO - Epoch   6/20: Train Loss: 0.801634, Val Loss: 0.799550, Weighted Loss: 0.801009 (Best✓)
2025-06-25 18:36:55,769 - models.augmentation_factory - INFO - Epoch   7/20: Train Loss: 0.799632, Val Loss: 0.794066, Weighted Loss: 0.797962 (Best✓)
2025-06-25 18:36:56,413 - models.augmentation_factory - INFO - Epoch   8/20: Train Loss: 0.801129, Val Loss: 0.788599, Weighted Loss: 0.797370 (Best✓)
2025-06-25 18:36:57,053 - models.augmentation_factory - INFO - Epoch   9/20: Train Loss: 0.794200, Val Loss: 0.780861, Weighted Loss: 0.790198 (Best✓)
2025-06-25 18:36:57,687 - models.augmentation_factory - INFO - Epoch  10/20: Train Loss: 0.780300, Val Loss: 0.766841, Weighted Loss: 0.776262 (Best✓)
2025-06-25 18:36:58,323 - models.augmentation_factory - INFO - Epoch  11/20: Train Loss: 0.770960, Val Loss: 0.735986, Weighted Loss: 0.760467 (Best✓)
2025-06-25 18:36:58,956 - models.augmentation_factory - INFO - Epoch  12/20: Train Loss: 0.728981, Val Loss: 0.691976, Weighted Loss: 0.717879 (Best✓)
2025-06-25 18:36:59,589 - models.augmentation_factory - INFO - Epoch  13/20: Train Loss: 0.681369, Val Loss: 0.624700, Weighted Loss: 0.664369 (Best✓)
2025-06-25 18:37:00,221 - models.augmentation_factory - INFO - Epoch  14/20: Train Loss: 0.632478, Val Loss: 0.589993, Weighted Loss: 0.619732 (Best✓)
2025-06-25 18:37:00,858 - models.augmentation_factory - INFO - Epoch  15/20: Train Loss: 0.596984, Val Loss: 0.627044, Weighted Loss: 0.606002 (Best✓)
2025-06-25 18:37:01,493 - models.augmentation_factory - INFO - Epoch  16/20: Train Loss: 0.593180, Val Loss: 0.563867, Weighted Loss: 0.584386 (Best✓)
2025-06-25 18:37:02,134 - models.augmentation_factory - INFO - Epoch  17/20: Train Loss: 0.574436, Val Loss: 0.505900, Weighted Loss: 0.553876 (Best✓)
2025-06-25 18:37:02,773 - models.augmentation_factory - INFO - Epoch  18/20: Train Loss: 0.523904, Val Loss: 0.496165, Weighted Loss: 0.515582 (Best✓)
2025-06-25 18:37:03,417 - models.augmentation_factory - INFO - Epoch  19/20: Train Loss: 0.480128, Val Loss: 0.431525, Weighted Loss: 0.465547 (Best✓)
2025-06-25 18:37:04,065 - models.augmentation_factory - INFO - Epoch  20/20: Train Loss: 0.477544, Val Loss: 0.417851, Weighted Loss: 0.459636 (Best✓)
2025-06-25 18:37:04,066 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-25 18:37:04,649 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-25 18:37:04,650 - __main__ - INFO - ==================================================
2025-06-25 18:37:04,651 - __main__ - INFO - 生成增强样本
2025-06-25 18:37:04,651 - __main__ - INFO - ==================================================
2025-06-25 18:37:11,685 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-25 18:37:11,686 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-25 18:37:11,707 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 18:37:11,708 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 18:37:11,708 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 18:37:11,709 - models.cddpm - INFO -   类别数量: 8
2025-06-25 18:37:12,039 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 18:37:12,041 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 18:37:12,041 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 18:37:12,044 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 18:37:16,208 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-25 18:37:16,210 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 [10] 个...
2025-06-25 18:37:16,210 - __main__ - INFO - 生成类别 0 的样本...
2025-06-25 18:37:16,218 - __main__ - ERROR - 实验失败: full() received an invalid combination of arguments - got (size=tuple, fill_value=int, dtype=torch.dtype, device=torch.device, ), but expected one of:
 * (tuple of ints size, Number fill_value, *, tuple of names names, torch.dtype dtype, torch.layout layout, torch.device device, bool pin_memory, bool requires_grad)
 * (tuple of SymInts size, Number fill_value, *, Tensor out, torch.dtype dtype, torch.layout layout, torch.device device, bool pin_memory, bool requires_grad)
Traceback (most recent call last):
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1671, in main
    results = run_comparison_experiments(args.config)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1436, in run_comparison_experiments
    return run_single_experiment(base_config)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1642, in run_single_experiment
    results = run_experiment(config)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1093, in run_experiment
    generated_data, generated_labels = generator.generate_samples(
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 755, in generate_samples
    class_labels = torch.full(size=(num_samples_per_class,), fill_value=class_idx,
TypeError: full() received an invalid combination of arguments - got (size=tuple, fill_value=int, dtype=torch.dtype, device=torch.device, ), but expected one of:
 * (tuple of ints size, Number fill_value, *, tuple of names names, torch.dtype dtype, torch.layout layout, torch.device device, bool pin_memory, bool requires_grad)
 * (tuple of SymInts size, Number fill_value, *, Tensor out, torch.dtype dtype, torch.layout layout, torch.device device, bool pin_memory, bool requires_grad)

