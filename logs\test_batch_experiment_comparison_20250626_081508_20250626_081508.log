2025-06-26 08:15:08,186 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_batch_experiment_comparison_20250626_081508_20250626_081508.log
2025-06-26 08:15:08,186 - common.experiment_manager - INFO - 检测到训练数据参数变化，需要重新训练扩散模型
2025-06-26 08:15:08,186 - __main__ - INFO - ================================================================================
2025-06-26 08:15:08,186 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:15:08,186 - __main__ - INFO - 实验名称: test_batch_experiment
2025-06-26 08:15:08,186 - __main__ - INFO - 数据集: KAT
2025-06-26 08:15:08,186 - __main__ - INFO - 总实验数: 4
2025-06-26 08:15:08,187 - __main__ - INFO - 对比参数: ['dataset.data_loading.fault_samples.max_fault_samples_per_class', 'augmentation.num_generated_per_class']
2025-06-26 08:15:08,187 - __main__ - INFO - 🔥 扩散模型重用: 否
2025-06-26 08:15:08,187 - __main__ - INFO - ================================================================================
2025-06-26 08:15:08,187 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_081508
2025-06-26 08:15:08,188 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_081508\configs\original_config.yaml
2025-06-26 08:15:08,188 - __main__ - INFO - 🔧 使用传统独立实验模式
2025-06-26 08:15:08,188 - __main__ - INFO - ============================================================
2025-06-26 08:15:08,188 - __main__ - INFO - 开始实验 1/4
2025-06-26 08:15:08,188 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 5}
2025-06-26 08:15:08,188 - __main__ - INFO - ============================================================
2025-06-26 08:15:08,193 - __main__ - INFO - 缓存配置已保存: cache\20250626_081508\experiment_001_config.yaml
2025-06-26 08:15:08,193 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:15:08,193 - __main__ - INFO - ================================================================================
2025-06-26 08:15:08,193 - __main__ - INFO - 当前实验配置
2025-06-26 08:15:08,193 - __main__ - INFO - ================================================================================
2025-06-26 08:15:08,193 - __main__ - INFO - 数据集: KAT
2025-06-26 08:15:08,194 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:15:08,194 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:15:08,194 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:15:08,194 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:15:08,194 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:15:08,194 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:15:08,194 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:15:08,194 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:15:08,194 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:15:08,194 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:15:08,194 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:15:08,194 - __main__ - INFO - 设备: auto
2025-06-26 08:15:08,194 - __main__ - INFO - 性能模式: auto
2025-06-26 08:15:08,194 - __main__ - INFO - 随机种子: 42
2025-06-26 08:15:08,194 - __main__ - INFO - ================================================================================
2025-06-26 08:15:08,197 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:15:08,198 - __main__ - INFO - 加载数据...
2025-06-26 08:15:08,198 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:15:08,198 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:15:08,201 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:15:08,205 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:15:08,205 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:15:08,205 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:15:08,205 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:15:08,205 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,206 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:15:08,207 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:15:08,211 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:15:08,215 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:15:08,215 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:15:08,215 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:15:08,216 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:15:08,216 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:15:08,217 - __main__ - INFO - ==================================================
2025-06-26 08:15:08,217 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:15:08,217 - __main__ - INFO - ==================================================
2025-06-26 08:15:08,218 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:15:08,219 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:15:08,219 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:15:08,219 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:15:08,219 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:15:08,456 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:15:08,456 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:15:08,456 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:15:08,456 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:15:09,761 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:15:09,762 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:15:09,762 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:15:10,384 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837687, Val Loss: 0.867107 (Best✓)
2025-06-26 08:15:10,385 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:15:10,746 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:15:10,747 - __main__ - INFO - ==================================================
2025-06-26 08:15:10,747 - __main__ - INFO - 生成增强样本
2025-06-26 08:15:10,747 - __main__ - INFO - ==================================================
2025-06-26 08:15:11,232 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:15:11,232 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:15:11,232 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:15:11,232 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:15:11,233 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:15:11,233 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:15:11,423 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:15:11,423 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:15:11,423 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:15:11,423 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:15:11,831 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:15:11,832 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 08:15:11,832 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:15:11,832 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:15:11,832 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:15:28,658 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:15:45,644 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:16:02,544 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:16:19,410 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:16:37,102 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:16:54,547 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 08:17:11,725 - __main__ - INFO - 样本生成完成，总共生成 35 个样本
2025-06-26 08:17:11,727 - __main__ - INFO - 生成样本已保存:
2025-06-26 08:17:11,727 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 08:17:11,727 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 08:17:14,376 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 08:17:14,376 - __main__ - INFO - ==================================================
2025-06-26 08:17:14,376 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 08:17:14,377 - __main__ - INFO - ==================================================
2025-06-26 08:17:14,414 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:17:14,414 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:17:14,528 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0935 Acc: 0.0000 | Val Loss: 2.0794 Acc: 0.0000 | LR: 9.78e-05 | Time: 00:00
2025-06-26 08:17:14,553 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0771 Acc: 0.2727 | Val Loss: 2.0802 Acc: 0.0000 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:17:14,578 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0784 Acc: 0.2727 | Val Loss: 2.0822 Acc: 0.0000 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:17:14,606 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0651 Acc: 0.2727 | Val Loss: 2.0850 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:17:14,631 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0663 Acc: 0.1818 | Val Loss: 2.0880 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:17:14,656 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0588 Acc: 0.1818 | Val Loss: 2.0911 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:17:14,682 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0547 Acc: 0.1818 | Val Loss: 2.0940 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:17:14,710 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0480 Acc: 0.1818 | Val Loss: 2.0966 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:17:14,732 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0509 Acc: 0.2727 | Val Loss: 2.0987 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:17:14,754 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0516 Acc: 0.2727 | Val Loss: 2.1006 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:17:14,755 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:17:14,755 - __main__ - INFO - 最佳验证准确率: 0.0000
2025-06-26 08:17:15,168 - __main__ - INFO - 基线分类器性能:
2025-06-26 08:17:15,168 - __main__ - INFO -   准确率: 0.1249
2025-06-26 08:17:15,168 - __main__ - INFO -   精确率: 0.0159
2025-06-26 08:17:15,168 - __main__ - INFO -   召回率: 0.1249
2025-06-26 08:17:15,168 - __main__ - INFO -   F1分数: 0.0283
2025-06-26 08:17:15,168 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 08:17:15,168 - __main__ - INFO -   最终训练损失: 2.0516
2025-06-26 08:17:15,169 - __main__ - INFO -   最终验证损失: 2.1006
2025-06-26 08:17:15,169 - __main__ - INFO -   最终训练准确率: 0.2727
2025-06-26 08:17:15,169 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 08:17:15,169 - __main__ - INFO -   实际训练轮数: 10
2025-06-26 08:17:15,169 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 08:17:15,169 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 08:17:15,170 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 08:17:15,170 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 08:17:15,170 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 08:17:15,170 - __main__ - INFO - ==================================================
2025-06-26 08:17:15,170 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 08:17:15,170 - __main__ - INFO - ==================================================
2025-06-26 08:17:15,170 - __main__ - INFO - 数据来源确认:
2025-06-26 08:17:15,170 - __main__ - INFO -   基线训练数据来源: train_subset (11 样本)
2025-06-26 08:17:15,171 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 08:17:15,171 - __main__ - INFO -   验证数据来源: val_subset (5 样本)
2025-06-26 08:17:15,171 - __main__ - INFO - 数据维度检查:
2025-06-26 08:17:15,171 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:17:15,171 - __main__ - INFO -   生成数据形状: (35, 1024)
2025-06-26 08:17:15,171 - __main__ - INFO - 原始训练数据: 11 样本
2025-06-26 08:17:15,171 - __main__ - INFO - 生成数据: 35 样本
2025-06-26 08:17:15,171 - __main__ - INFO - 增强后训练数据: 46 样本
2025-06-26 08:17:15,171 - __main__ - INFO - 数据分布分析:
2025-06-26 08:17:15,171 - __main__ - INFO -   原始数据类别分布: {0: 1, 1: 2, 3: 2, 4: 2, 5: 1, 6: 1, 7: 2}
2025-06-26 08:17:15,171 - __main__ - INFO -   生成数据类别分布: {1: 5, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5, 7: 5}
2025-06-26 08:17:15,171 - __main__ - INFO -   增强数据类别分布: {0: 1, 1: 7, 2: 5, 3: 7, 4: 7, 5: 6, 6: 6, 7: 7}
2025-06-26 08:17:15,172 - __main__ - INFO - 健康样本平衡调整:
2025-06-26 08:17:15,172 - __main__ - INFO -   故障类别数量分布: [7, 5, 7, 7, 6, 6, 7]
2025-06-26 08:17:15,172 - __main__ - INFO -   平均故障样本数: 6
2025-06-26 08:17:15,172 - __main__ - INFO -   目标健康样本数: 6
2025-06-26 08:17:15,172 - __main__ - INFO -   当前健康样本数: 1
2025-06-26 08:17:15,172 - __main__ - WARNING -   可用健康样本不足: 2 < 6
2025-06-26 08:17:15,172 - __main__ - INFO -   调整后数据分布: {0: 2, 1: 7, 2: 5, 3: 7, 4: 7, 5: 6, 6: 6, 7: 7}
2025-06-26 08:17:15,173 - __main__ - INFO -   健康样本调整: 1 → 2
2025-06-26 08:17:15,173 - __main__ - INFO - 数据质量检查:
2025-06-26 08:17:15,173 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:17:15,174 - __main__ - INFO -   生成数据形状: (35, 1024)
2025-06-26 08:17:15,174 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 08:17:15,174 - __main__ - INFO -   生成数据范围: [-871.0819, 1086.2465]
2025-06-26 08:17:15,198 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:17:15,198 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:17:15,472 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0770 Acc: 0.1250 | Val Loss: 2.0816 Acc: 0.1333 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:17:15,518 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0677 Acc: 0.1562 | Val Loss: 2.0802 Acc: 0.0667 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:17:15,563 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0515 Acc: 0.1562 | Val Loss: 2.0833 Acc: 0.0667 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:17:15,609 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0450 Acc: 0.2188 | Val Loss: 2.0864 Acc: 0.0667 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:17:15,652 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0255 Acc: 0.3125 | Val Loss: 2.0879 Acc: 0.0667 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:17:15,699 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0203 Acc: 0.2812 | Val Loss: 2.0892 Acc: 0.1333 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:17:15,747 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0126 Acc: 0.2812 | Val Loss: 2.0903 Acc: 0.1333 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:17:15,791 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0156 Acc: 0.2500 | Val Loss: 2.0913 Acc: 0.1333 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:17:15,835 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0033 Acc: 0.3125 | Val Loss: 2.0922 Acc: 0.1333 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:17:15,878 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0008 Acc: 0.3125 | Val Loss: 2.0929 Acc: 0.1333 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:17:15,879 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:17:15,879 - __main__ - INFO - 最佳验证准确率: 0.1333
2025-06-26 08:17:16,242 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 08:17:16,243 - __main__ - INFO - ==================================================
2025-06-26 08:17:16,243 - __main__ - INFO - 评估分类器性能
2025-06-26 08:17:16,243 - __main__ - INFO - ==================================================
2025-06-26 08:17:16,581 - __main__ - INFO - 分类性能指标:
2025-06-26 08:17:16,581 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 08:17:16,581 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 08:17:16,581 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 08:17:16,581 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 08:17:17,004 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 08:17:17,004 - __main__ - INFO - ==================================================
2025-06-26 08:17:17,004 - __main__ - INFO - 性能对比总结
2025-06-26 08:17:17,004 - __main__ - INFO - ==================================================
2025-06-26 08:17:17,004 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 08:17:17,004 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:17:17,004 - __main__ - INFO -   精确率: 0.0159 → 0.0156 (Δ-0.0004)
2025-06-26 08:17:17,004 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:17:17,004 - __main__ - INFO -   F1分数: 0.0283 → 0.0277 (Δ-0.0006)
2025-06-26 08:17:17,005 - __main__ - INFO - 🏁 实验结束
2025-06-26 08:17:17,005 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,005 - __main__ - INFO - 当前实验配置
2025-06-26 08:17:17,005 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,005 - __main__ - INFO - 数据集: KAT
2025-06-26 08:17:17,005 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:17:17,005 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:17:17,005 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:17:17,005 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:17:17,005 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:17:17,006 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:17:17,006 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:17:17,006 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:17:17,006 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:17:17,006 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:17:17,006 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:17:17,006 - __main__ - INFO - 设备: auto
2025-06-26 08:17:17,006 - __main__ - INFO - 性能模式: auto
2025-06-26 08:17:17,007 - __main__ - INFO - 随机种子: 42
2025-06-26 08:17:17,007 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,061 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_081508\individual_experiments\experiment_001
2025-06-26 08:17:17,061 - __main__ - INFO - 实验 1 完成
2025-06-26 08:17:17,061 - __main__ - INFO - ============================================================
2025-06-26 08:17:17,061 - __main__ - INFO - 开始实验 2/4
2025-06-26 08:17:17,061 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 8}
2025-06-26 08:17:17,061 - __main__ - INFO - ============================================================
2025-06-26 08:17:17,066 - __main__ - INFO - 缓存配置已保存: cache\20250626_081508\experiment_002_config.yaml
2025-06-26 08:17:17,066 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:17:17,066 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,066 - __main__ - INFO - 当前实验配置
2025-06-26 08:17:17,067 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,067 - __main__ - INFO - 数据集: KAT
2025-06-26 08:17:17,067 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:17:17,067 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:17:17,067 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:17:17,067 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:17:17,067 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:17:17,067 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:17:17,067 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:17:17,067 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:17:17,068 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:17:17,068 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:17:17,068 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:17:17,068 - __main__ - INFO - 设备: auto
2025-06-26 08:17:17,068 - __main__ - INFO - 性能模式: auto
2025-06-26 08:17:17,068 - __main__ - INFO - 随机种子: 42
2025-06-26 08:17:17,068 - __main__ - INFO - ================================================================================
2025-06-26 08:17:17,070 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:17:17,071 - __main__ - INFO - 加载数据...
2025-06-26 08:17:17,071 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:17:17,071 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:17:17,077 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:17:17,082 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:17:17,082 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:17:17,082 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:17:17,082 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:17:17,082 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:17:17,082 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:17:17,083 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:17:17,087 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:17:17,089 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:17:17,089 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:17:17,089 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:17:17,089 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:17:17,089 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:17:17,090 - __main__ - INFO - ==================================================
2025-06-26 08:17:17,090 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:17:17,090 - __main__ - INFO - ==================================================
2025-06-26 08:17:17,091 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:17:17,091 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:17:17,091 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:17:17,092 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:17:17,092 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:17:17,294 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:17:17,294 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:17:17,296 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:17:17,296 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:17:17,321 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:17:17,321 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:17:17,322 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:17:17,499 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837687, Val Loss: 0.867107 (Best✓)
2025-06-26 08:17:17,500 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:17:17,799 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:17:17,799 - __main__ - INFO - ==================================================
2025-06-26 08:17:17,799 - __main__ - INFO - 生成增强样本
2025-06-26 08:17:17,799 - __main__ - INFO - ==================================================
2025-06-26 08:17:18,275 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:17:18,275 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:17:18,276 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:17:18,276 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:17:18,276 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:17:18,276 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:17:18,466 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:17:18,466 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:17:18,466 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:17:18,466 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:17:18,860 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:17:18,861 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 8 个...
2025-06-26 08:17:18,861 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:17:18,861 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:17:18,861 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:17:42,807 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:18:06,869 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:18:30,899 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:18:54,645 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:19:18,365 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:19:42,067 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 08:20:05,788 - __main__ - INFO - 样本生成完成，总共生成 56 个样本
2025-06-26 08:20:05,789 - __main__ - INFO - 生成样本已保存:
2025-06-26 08:20:05,789 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 08:20:05,790 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 08:20:09,055 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 08:20:09,055 - __main__ - INFO - ==================================================
2025-06-26 08:20:09,055 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 08:20:09,055 - __main__ - INFO - ==================================================
2025-06-26 08:20:09,114 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:20:09,114 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:20:09,212 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0935 Acc: 0.0000 | Val Loss: 2.0794 Acc: 0.0000 | LR: 9.78e-05 | Time: 00:00
2025-06-26 08:20:09,297 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0771 Acc: 0.2727 | Val Loss: 2.0802 Acc: 0.0000 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:20:09,346 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0784 Acc: 0.2727 | Val Loss: 2.0822 Acc: 0.0000 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:20:09,397 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0651 Acc: 0.2727 | Val Loss: 2.0850 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:20:09,446 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0663 Acc: 0.1818 | Val Loss: 2.0880 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:20:09,490 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0588 Acc: 0.1818 | Val Loss: 2.0911 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:20:09,516 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0547 Acc: 0.1818 | Val Loss: 2.0940 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:20:09,542 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0480 Acc: 0.1818 | Val Loss: 2.0966 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:20:09,569 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0509 Acc: 0.2727 | Val Loss: 2.0987 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:20:09,614 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0516 Acc: 0.2727 | Val Loss: 2.1006 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:20:09,615 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:20:09,615 - __main__ - INFO - 最佳验证准确率: 0.0000
2025-06-26 08:20:09,984 - __main__ - INFO - 基线分类器性能:
2025-06-26 08:20:09,985 - __main__ - INFO -   准确率: 0.1249
2025-06-26 08:20:09,985 - __main__ - INFO -   精确率: 0.0159
2025-06-26 08:20:09,985 - __main__ - INFO -   召回率: 0.1249
2025-06-26 08:20:09,986 - __main__ - INFO -   F1分数: 0.0283
2025-06-26 08:20:09,986 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 08:20:09,986 - __main__ - INFO -   最终训练损失: 2.0516
2025-06-26 08:20:09,986 - __main__ - INFO -   最终验证损失: 2.1006
2025-06-26 08:20:09,986 - __main__ - INFO -   最终训练准确率: 0.2727
2025-06-26 08:20:09,987 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 08:20:09,987 - __main__ - INFO -   实际训练轮数: 10
2025-06-26 08:20:09,987 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 08:20:09,987 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 08:20:09,987 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 08:20:09,987 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 08:20:09,987 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 08:20:09,987 - __main__ - INFO - ==================================================
2025-06-26 08:20:09,987 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 08:20:09,987 - __main__ - INFO - ==================================================
2025-06-26 08:20:09,988 - __main__ - INFO - 数据来源确认:
2025-06-26 08:20:09,988 - __main__ - INFO -   基线训练数据来源: train_subset (11 样本)
2025-06-26 08:20:09,988 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 08:20:09,988 - __main__ - INFO -   验证数据来源: val_subset (5 样本)
2025-06-26 08:20:09,989 - __main__ - INFO - 数据维度检查:
2025-06-26 08:20:09,989 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:20:09,989 - __main__ - INFO -   生成数据形状: (56, 1024)
2025-06-26 08:20:09,989 - __main__ - INFO - 原始训练数据: 11 样本
2025-06-26 08:20:09,989 - __main__ - INFO - 生成数据: 56 样本
2025-06-26 08:20:09,989 - __main__ - INFO - 增强后训练数据: 67 样本
2025-06-26 08:20:09,990 - __main__ - INFO - 数据分布分析:
2025-06-26 08:20:09,990 - __main__ - INFO -   原始数据类别分布: {0: 1, 1: 2, 3: 2, 4: 2, 5: 1, 6: 1, 7: 2}
2025-06-26 08:20:09,990 - __main__ - INFO -   生成数据类别分布: {1: 8, 2: 8, 3: 8, 4: 8, 5: 8, 6: 8, 7: 8}
2025-06-26 08:20:09,990 - __main__ - INFO -   增强数据类别分布: {0: 1, 1: 10, 2: 8, 3: 10, 4: 10, 5: 9, 6: 9, 7: 10}
2025-06-26 08:20:09,991 - __main__ - INFO - 健康样本平衡调整:
2025-06-26 08:20:09,991 - __main__ - INFO -   故障类别数量分布: [10, 8, 10, 10, 9, 9, 10]
2025-06-26 08:20:09,991 - __main__ - INFO -   平均故障样本数: 9
2025-06-26 08:20:09,992 - __main__ - INFO -   目标健康样本数: 9
2025-06-26 08:20:09,992 - __main__ - INFO -   当前健康样本数: 1
2025-06-26 08:20:09,992 - __main__ - WARNING -   可用健康样本不足: 2 < 9
2025-06-26 08:20:09,993 - __main__ - INFO -   调整后数据分布: {0: 2, 1: 10, 2: 8, 3: 10, 4: 10, 5: 9, 6: 9, 7: 10}
2025-06-26 08:20:09,993 - __main__ - INFO -   健康样本调整: 1 → 2
2025-06-26 08:20:09,993 - __main__ - INFO - 数据质量检查:
2025-06-26 08:20:09,994 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:20:09,994 - __main__ - INFO -   生成数据形状: (56, 1024)
2025-06-26 08:20:09,994 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 08:20:09,994 - __main__ - INFO -   生成数据范围: [-973.7434, 1086.2645]
2025-06-26 08:20:10,029 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:20:10,029 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:20:10,172 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0805 Acc: 0.1489 | Val Loss: 2.0470 Acc: 0.1429 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:20:10,248 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0628 Acc: 0.1489 | Val Loss: 2.0449 Acc: 0.1429 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:20:10,366 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0528 Acc: 0.1064 | Val Loss: 2.0454 Acc: 0.2381 | LR: 8.15e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:20:10,495 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0394 Acc: 0.1702 | Val Loss: 2.0441 Acc: 0.2857 | LR: 6.89e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:20:10,577 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0291 Acc: 0.2553 | Val Loss: 2.0400 Acc: 0.1905 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:20:10,662 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0182 Acc: 0.2553 | Val Loss: 2.0367 Acc: 0.1905 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:20:10,745 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0051 Acc: 0.3191 | Val Loss: 2.0338 Acc: 0.1905 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:20:10,820 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0045 Acc: 0.3404 | Val Loss: 2.0314 Acc: 0.1905 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:20:10,890 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9998 Acc: 0.2766 | Val Loss: 2.0300 Acc: 0.1905 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:20:10,960 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0046 Acc: 0.2340 | Val Loss: 2.0286 Acc: 0.1905 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:20:10,961 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:20:10,961 - __main__ - INFO - 最佳验证准确率: 0.2857
2025-06-26 08:20:11,394 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 08:20:11,395 - __main__ - INFO - ==================================================
2025-06-26 08:20:11,395 - __main__ - INFO - 评估分类器性能
2025-06-26 08:20:11,395 - __main__ - INFO - ==================================================
2025-06-26 08:20:11,768 - __main__ - INFO - 分类性能指标:
2025-06-26 08:20:11,768 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 08:20:11,768 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 08:20:11,769 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 08:20:11,769 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 08:20:12,275 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 08:20:12,276 - __main__ - INFO - ==================================================
2025-06-26 08:20:12,276 - __main__ - INFO - 性能对比总结
2025-06-26 08:20:12,276 - __main__ - INFO - ==================================================
2025-06-26 08:20:12,276 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 08:20:12,276 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:20:12,276 - __main__ - INFO -   精确率: 0.0159 → 0.0156 (Δ-0.0004)
2025-06-26 08:20:12,276 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:20:12,276 - __main__ - INFO -   F1分数: 0.0283 → 0.0277 (Δ-0.0006)
2025-06-26 08:20:12,277 - __main__ - INFO - 🏁 实验结束
2025-06-26 08:20:12,277 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,277 - __main__ - INFO - 当前实验配置
2025-06-26 08:20:12,277 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,278 - __main__ - INFO - 数据集: KAT
2025-06-26 08:20:12,278 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:20:12,278 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:20:12,278 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:20:12,278 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:20:12,278 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:20:12,278 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:20:12,278 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:20:12,278 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:20:12,279 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:20:12,279 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:20:12,279 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:20:12,279 - __main__ - INFO - 设备: auto
2025-06-26 08:20:12,279 - __main__ - INFO - 性能模式: auto
2025-06-26 08:20:12,279 - __main__ - INFO - 随机种子: 42
2025-06-26 08:20:12,279 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,356 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_081508\individual_experiments\experiment_002
2025-06-26 08:20:12,356 - __main__ - INFO - 实验 2 完成
2025-06-26 08:20:12,357 - __main__ - INFO - ============================================================
2025-06-26 08:20:12,357 - __main__ - INFO - 开始实验 3/4
2025-06-26 08:20:12,357 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 5}
2025-06-26 08:20:12,357 - __main__ - INFO - ============================================================
2025-06-26 08:20:12,364 - __main__ - INFO - 缓存配置已保存: cache\20250626_081508\experiment_003_config.yaml
2025-06-26 08:20:12,365 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:20:12,365 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,365 - __main__ - INFO - 当前实验配置
2025-06-26 08:20:12,365 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,365 - __main__ - INFO - 数据集: KAT
2025-06-26 08:20:12,365 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:20:12,366 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:20:12,366 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:20:12,366 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:20:12,366 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:20:12,367 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:20:12,367 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:20:12,367 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:20:12,367 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:20:12,367 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:20:12,368 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:20:12,368 - __main__ - INFO - 设备: auto
2025-06-26 08:20:12,368 - __main__ - INFO - 性能模式: auto
2025-06-26 08:20:12,368 - __main__ - INFO - 随机种子: 42
2025-06-26 08:20:12,368 - __main__ - INFO - ================================================================================
2025-06-26 08:20:12,369 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:20:12,371 - __main__ - INFO - 加载数据...
2025-06-26 08:20:12,371 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:20:12,371 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:20:12,378 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:20:12,386 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:20:12,387 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:20:12,387 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:20:12,388 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,388 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 08:20:12,390 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:20:12,395 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:20:12,397 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:20:12,397 - common.data_loader - INFO -   训练样本: 24
2025-06-26 08:20:12,398 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:20:12,398 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:20:12,398 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:20:12,399 - __main__ - INFO - ==================================================
2025-06-26 08:20:12,399 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:20:12,399 - __main__ - INFO - ==================================================
2025-06-26 08:20:12,400 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:20:12,400 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:20:12,400 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:20:12,402 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:20:12,402 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:20:12,694 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:20:12,695 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:20:12,695 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:20:12,695 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:20:12,736 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:20:12,737 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:20:12,737 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:20:12,967 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837600, Val Loss: 0.839663 (Best✓)
2025-06-26 08:20:12,968 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:20:13,318 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:20:13,318 - __main__ - INFO - ==================================================
2025-06-26 08:20:13,318 - __main__ - INFO - 生成增强样本
2025-06-26 08:20:13,318 - __main__ - INFO - ==================================================
2025-06-26 08:20:13,933 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:20:13,934 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:20:13,934 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:20:13,934 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:20:13,934 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:20:13,935 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:20:14,184 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:20:14,185 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:20:14,185 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:20:14,185 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:20:14,809 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:20:14,812 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 08:20:14,812 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:20:14,812 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:20:14,812 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:20:31,841 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:20:48,832 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:21:05,765 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:21:22,914 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:21:40,102 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:21:57,019 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 08:22:13,944 - __main__ - INFO - 样本生成完成，总共生成 35 个样本
2025-06-26 08:22:13,945 - __main__ - INFO - 生成样本已保存:
2025-06-26 08:22:13,945 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 08:22:13,945 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 08:22:16,640 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 08:22:16,640 - __main__ - INFO - ==================================================
2025-06-26 08:22:16,640 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 08:22:16,640 - __main__ - INFO - ==================================================
2025-06-26 08:22:16,676 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:22:16,677 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:22:16,831 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0850 Acc: 0.0625 | Val Loss: 2.0795 Acc: 0.1250 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:22:16,908 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0721 Acc: 0.1875 | Val Loss: 2.0798 Acc: 0.1250 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:22:16,980 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0783 Acc: 0.1250 | Val Loss: 2.0806 Acc: 0.1250 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:22:17,046 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0712 Acc: 0.1875 | Val Loss: 2.0817 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:22:17,076 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0739 Acc: 0.1250 | Val Loss: 2.0828 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:22:17,106 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0691 Acc: 0.1875 | Val Loss: 2.0839 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:22:17,137 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0737 Acc: 0.1250 | Val Loss: 2.0849 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:22:17,168 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0627 Acc: 0.1875 | Val Loss: 2.0860 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:22:17,198 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0596 Acc: 0.1875 | Val Loss: 2.0869 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:22:17,228 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0620 Acc: 0.1875 | Val Loss: 2.0877 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:22:17,228 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:22:17,228 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 08:22:17,571 - __main__ - INFO - 基线分类器性能:
2025-06-26 08:22:17,571 - __main__ - INFO -   准确率: 0.1249
2025-06-26 08:22:17,571 - __main__ - INFO -   精确率: 0.0156
2025-06-26 08:22:17,572 - __main__ - INFO -   召回率: 0.1249
2025-06-26 08:22:17,572 - __main__ - INFO -   F1分数: 0.0277
2025-06-26 08:22:17,572 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 08:22:17,572 - __main__ - INFO -   最终训练损失: 2.0620
2025-06-26 08:22:17,572 - __main__ - INFO -   最终验证损失: 2.0877
2025-06-26 08:22:17,572 - __main__ - INFO -   最终训练准确率: 0.1875
2025-06-26 08:22:17,572 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 08:22:17,572 - __main__ - INFO -   实际训练轮数: 10
2025-06-26 08:22:17,572 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 08:22:17,572 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 08:22:17,573 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 08:22:17,573 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 08:22:17,573 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 08:22:17,574 - __main__ - INFO - ==================================================
2025-06-26 08:22:17,574 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 08:22:17,574 - __main__ - INFO - ==================================================
2025-06-26 08:22:17,574 - __main__ - INFO - 数据来源确认:
2025-06-26 08:22:17,574 - __main__ - INFO -   基线训练数据来源: train_subset (16 样本)
2025-06-26 08:22:17,574 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 08:22:17,574 - __main__ - INFO -   验证数据来源: val_subset (8 样本)
2025-06-26 08:22:17,574 - __main__ - INFO - 数据维度检查:
2025-06-26 08:22:17,574 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 08:22:17,574 - __main__ - INFO -   生成数据形状: (35, 1024)
2025-06-26 08:22:17,574 - __main__ - INFO - 原始训练数据: 16 样本
2025-06-26 08:22:17,575 - __main__ - INFO - 生成数据: 35 样本
2025-06-26 08:22:17,575 - __main__ - INFO - 增强后训练数据: 51 样本
2025-06-26 08:22:17,575 - __main__ - INFO - 数据分布分析:
2025-06-26 08:22:17,575 - __main__ - INFO -   原始数据类别分布: {0: 2, 1: 3, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 1}
2025-06-26 08:22:17,575 - __main__ - INFO -   生成数据类别分布: {1: 5, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5, 7: 5}
2025-06-26 08:22:17,575 - __main__ - INFO -   增强数据类别分布: {0: 2, 1: 8, 2: 7, 3: 7, 4: 7, 5: 7, 6: 7, 7: 6}
2025-06-26 08:22:17,576 - __main__ - INFO - 健康样本平衡调整:
2025-06-26 08:22:17,576 - __main__ - INFO -   故障类别数量分布: [8, 7, 7, 7, 7, 7, 6]
2025-06-26 08:22:17,576 - __main__ - INFO -   平均故障样本数: 7
2025-06-26 08:22:17,576 - __main__ - INFO -   目标健康样本数: 7
2025-06-26 08:22:17,576 - __main__ - INFO -   当前健康样本数: 2
2025-06-26 08:22:17,576 - __main__ - WARNING -   可用健康样本不足: 3 < 7
2025-06-26 08:22:17,576 - __main__ - INFO -   调整后数据分布: {0: 3, 1: 8, 2: 7, 3: 7, 4: 7, 5: 7, 6: 7, 7: 6}
2025-06-26 08:22:17,576 - __main__ - INFO -   健康样本调整: 2 → 3
2025-06-26 08:22:17,577 - __main__ - INFO - 数据质量检查:
2025-06-26 08:22:17,577 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 08:22:17,577 - __main__ - INFO -   生成数据形状: (35, 1024)
2025-06-26 08:22:17,577 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 08:22:17,577 - __main__ - INFO -   生成数据范围: [-984.0272, 1042.2666]
2025-06-26 08:22:17,602 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:22:17,602 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:22:17,683 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0858 Acc: 0.0833 | Val Loss: 2.0804 Acc: 0.0000 | LR: 9.78e-05 | Time: 00:00
2025-06-26 08:22:17,738 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0523 Acc: 0.1667 | Val Loss: 2.0865 Acc: 0.0000 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:22:17,794 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0421 Acc: 0.2222 | Val Loss: 2.0958 Acc: 0.0000 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:22:17,849 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0499 Acc: 0.2222 | Val Loss: 2.1015 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:22:17,903 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0284 Acc: 0.3333 | Val Loss: 2.1026 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:22:17,959 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0192 Acc: 0.2778 | Val Loss: 2.1032 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:22:18,013 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0076 Acc: 0.2222 | Val Loss: 2.1041 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:22:18,068 - __main__ - INFO - Epoch   8/10 | Train Loss: 1.9594 Acc: 0.2778 | Val Loss: 2.1058 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:22:18,125 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9708 Acc: 0.3333 | Val Loss: 2.1073 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:22:18,181 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0106 Acc: 0.3333 | Val Loss: 2.1092 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:22:18,182 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:22:18,182 - __main__ - INFO - 最佳验证准确率: 0.0000
2025-06-26 08:22:18,538 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 08:22:18,538 - __main__ - INFO - ==================================================
2025-06-26 08:22:18,538 - __main__ - INFO - 评估分类器性能
2025-06-26 08:22:18,538 - __main__ - INFO - ==================================================
2025-06-26 08:22:18,879 - __main__ - INFO - 分类性能指标:
2025-06-26 08:22:18,880 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 08:22:18,880 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 08:22:18,880 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 08:22:18,880 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 08:22:19,294 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 08:22:19,294 - __main__ - INFO - ==================================================
2025-06-26 08:22:19,294 - __main__ - INFO - 性能对比总结
2025-06-26 08:22:19,294 - __main__ - INFO - ==================================================
2025-06-26 08:22:19,294 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 08:22:19,295 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:22:19,295 - __main__ - INFO -   精确率: 0.0156 → 0.0156 (Δ+0.0000)
2025-06-26 08:22:19,295 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:22:19,295 - __main__ - INFO -   F1分数: 0.0277 → 0.0277 (Δ+0.0000)
2025-06-26 08:22:19,295 - __main__ - INFO - 🏁 实验结束
2025-06-26 08:22:19,295 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,295 - __main__ - INFO - 当前实验配置
2025-06-26 08:22:19,295 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,295 - __main__ - INFO - 数据集: KAT
2025-06-26 08:22:19,295 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:22:19,295 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:22:19,296 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:22:19,296 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:22:19,296 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:22:19,296 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:22:19,296 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:22:19,296 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:22:19,296 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:22:19,296 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:22:19,296 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:22:19,296 - __main__ - INFO - 设备: auto
2025-06-26 08:22:19,296 - __main__ - INFO - 性能模式: auto
2025-06-26 08:22:19,296 - __main__ - INFO - 随机种子: 42
2025-06-26 08:22:19,297 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,342 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_081508\individual_experiments\experiment_003
2025-06-26 08:22:19,342 - __main__ - INFO - 实验 3 完成
2025-06-26 08:22:19,342 - __main__ - INFO - ============================================================
2025-06-26 08:22:19,342 - __main__ - INFO - 开始实验 4/4
2025-06-26 08:22:19,343 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 3, 'augmentation.num_generated_per_class': 8}
2025-06-26 08:22:19,343 - __main__ - INFO - ============================================================
2025-06-26 08:22:19,347 - __main__ - INFO - 缓存配置已保存: cache\20250626_081508\experiment_004_config.yaml
2025-06-26 08:22:19,347 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:22:19,347 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,347 - __main__ - INFO - 当前实验配置
2025-06-26 08:22:19,347 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,348 - __main__ - INFO - 数据集: KAT
2025-06-26 08:22:19,348 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:22:19,348 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:22:19,348 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:22:19,348 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:22:19,348 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:22:19,348 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:22:19,348 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:22:19,348 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:22:19,348 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:22:19,349 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:22:19,349 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:22:19,349 - __main__ - INFO - 设备: auto
2025-06-26 08:22:19,349 - __main__ - INFO - 性能模式: auto
2025-06-26 08:22:19,349 - __main__ - INFO - 随机种子: 42
2025-06-26 08:22:19,349 - __main__ - INFO - ================================================================================
2025-06-26 08:22:19,350 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:22:19,350 - __main__ - INFO - 加载数据...
2025-06-26 08:22:19,351 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:22:19,351 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:22:19,353 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:22:19,355 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:22:19,355 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:22:19,355 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:22:19,355 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 08:22:19,356 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 08:22:19,360 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:22:19,361 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:22:19,361 - common.data_loader - INFO -   训练样本: 24
2025-06-26 08:22:19,362 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:22:19,362 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:22:19,362 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:22:19,362 - __main__ - INFO - ==================================================
2025-06-26 08:22:19,363 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:22:19,363 - __main__ - INFO - ==================================================
2025-06-26 08:22:19,363 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:22:19,364 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:22:19,364 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:22:19,364 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:22:19,364 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:22:19,569 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:22:19,569 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:22:19,570 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:22:19,570 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:22:19,594 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:22:19,594 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:22:19,594 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:22:19,831 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837600, Val Loss: 0.839663 (Best✓)
2025-06-26 08:22:19,831 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:22:20,300 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:22:20,300 - __main__ - INFO - ==================================================
2025-06-26 08:22:20,300 - __main__ - INFO - 生成增强样本
2025-06-26 08:22:20,300 - __main__ - INFO - ==================================================
2025-06-26 08:22:20,787 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:22:20,788 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:22:20,788 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:22:20,788 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:22:20,788 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:22:20,788 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:22:20,972 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:22:20,972 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:22:20,972 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:22:20,973 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:22:21,247 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:22:21,248 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 8 个...
2025-06-26 08:22:21,249 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 08:22:21,249 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 08:22:21,249 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:22:44,911 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:23:08,587 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:23:32,160 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:23:55,756 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:24:19,316 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:24:43,002 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 08:25:06,568 - __main__ - INFO - 样本生成完成，总共生成 56 个样本
2025-06-26 08:25:06,569 - __main__ - INFO - 生成样本已保存:
2025-06-26 08:25:06,569 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 08:25:06,569 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 08:25:09,177 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 08:25:09,178 - __main__ - INFO - ==================================================
2025-06-26 08:25:09,178 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 08:25:09,178 - __main__ - INFO - ==================================================
2025-06-26 08:25:09,218 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:25:09,218 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:25:09,365 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0850 Acc: 0.0625 | Val Loss: 2.0795 Acc: 0.1250 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:25:09,446 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0721 Acc: 0.1875 | Val Loss: 2.0798 Acc: 0.1250 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:25:09,516 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0783 Acc: 0.1250 | Val Loss: 2.0806 Acc: 0.1250 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:25:09,586 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0712 Acc: 0.1875 | Val Loss: 2.0817 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:25:09,623 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0739 Acc: 0.1250 | Val Loss: 2.0828 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:25:09,653 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0691 Acc: 0.1875 | Val Loss: 2.0839 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:25:09,683 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0737 Acc: 0.1250 | Val Loss: 2.0849 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:25:09,715 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0627 Acc: 0.1875 | Val Loss: 2.0860 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:25:09,748 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0596 Acc: 0.1875 | Val Loss: 2.0869 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:25:09,777 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0620 Acc: 0.1875 | Val Loss: 2.0877 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:25:09,778 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:25:09,778 - __main__ - INFO - 最佳验证准确率: 0.1250
2025-06-26 08:25:10,143 - __main__ - INFO - 基线分类器性能:
2025-06-26 08:25:10,143 - __main__ - INFO -   准确率: 0.1249
2025-06-26 08:25:10,143 - __main__ - INFO -   精确率: 0.0156
2025-06-26 08:25:10,143 - __main__ - INFO -   召回率: 0.1249
2025-06-26 08:25:10,144 - __main__ - INFO -   F1分数: 0.0277
2025-06-26 08:25:10,144 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 08:25:10,144 - __main__ - INFO -   最终训练损失: 2.0620
2025-06-26 08:25:10,144 - __main__ - INFO -   最终验证损失: 2.0877
2025-06-26 08:25:10,144 - __main__ - INFO -   最终训练准确率: 0.1875
2025-06-26 08:25:10,144 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 08:25:10,144 - __main__ - INFO -   实际训练轮数: 10
2025-06-26 08:25:10,144 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 08:25:10,144 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 08:25:10,144 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 08:25:10,144 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 08:25:10,144 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 08:25:10,145 - __main__ - INFO - ==================================================
2025-06-26 08:25:10,145 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 08:25:10,145 - __main__ - INFO - ==================================================
2025-06-26 08:25:10,145 - __main__ - INFO - 数据来源确认:
2025-06-26 08:25:10,145 - __main__ - INFO -   基线训练数据来源: train_subset (16 样本)
2025-06-26 08:25:10,145 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 08:25:10,146 - __main__ - INFO -   验证数据来源: val_subset (8 样本)
2025-06-26 08:25:10,146 - __main__ - INFO - 数据维度检查:
2025-06-26 08:25:10,146 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 08:25:10,146 - __main__ - INFO -   生成数据形状: (56, 1024)
2025-06-26 08:25:10,146 - __main__ - INFO - 原始训练数据: 16 样本
2025-06-26 08:25:10,146 - __main__ - INFO - 生成数据: 56 样本
2025-06-26 08:25:10,146 - __main__ - INFO - 增强后训练数据: 72 样本
2025-06-26 08:25:10,146 - __main__ - INFO - 数据分布分析:
2025-06-26 08:25:10,146 - __main__ - INFO -   原始数据类别分布: {0: 2, 1: 3, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 1}
2025-06-26 08:25:10,147 - __main__ - INFO -   生成数据类别分布: {1: 8, 2: 8, 3: 8, 4: 8, 5: 8, 6: 8, 7: 8}
2025-06-26 08:25:10,147 - __main__ - INFO -   增强数据类别分布: {0: 2, 1: 11, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 9}
2025-06-26 08:25:10,147 - __main__ - INFO - 健康样本平衡调整:
2025-06-26 08:25:10,147 - __main__ - INFO -   故障类别数量分布: [11, 10, 10, 10, 10, 10, 9]
2025-06-26 08:25:10,147 - __main__ - INFO -   平均故障样本数: 10
2025-06-26 08:25:10,147 - __main__ - INFO -   目标健康样本数: 10
2025-06-26 08:25:10,147 - __main__ - INFO -   当前健康样本数: 2
2025-06-26 08:25:10,147 - __main__ - WARNING -   可用健康样本不足: 3 < 10
2025-06-26 08:25:10,148 - __main__ - INFO -   调整后数据分布: {0: 3, 1: 11, 2: 10, 3: 10, 4: 10, 5: 10, 6: 10, 7: 9}
2025-06-26 08:25:10,148 - __main__ - INFO -   健康样本调整: 2 → 3
2025-06-26 08:25:10,148 - __main__ - INFO - 数据质量检查:
2025-06-26 08:25:10,148 - __main__ - INFO -   原始数据形状: (16, 1024)
2025-06-26 08:25:10,148 - __main__ - INFO -   生成数据形状: (56, 1024)
2025-06-26 08:25:10,148 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 08:25:10,148 - __main__ - INFO -   生成数据范围: [-984.0558, 1091.2211]
2025-06-26 08:25:10,171 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:25:10,171 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:25:10,341 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0685 Acc: 0.1373 | Val Loss: 2.0773 Acc: 0.0909 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:25:10,425 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0596 Acc: 0.2353 | Val Loss: 2.0649 Acc: 0.0909 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:25:10,500 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0413 Acc: 0.2549 | Val Loss: 2.0577 Acc: 0.0909 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:25:10,618 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0276 Acc: 0.2353 | Val Loss: 2.0507 Acc: 0.2273 | LR: 6.89e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:25:10,704 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0190 Acc: 0.2549 | Val Loss: 2.0463 Acc: 0.0909 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:25:10,786 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0105 Acc: 0.2353 | Val Loss: 2.0406 Acc: 0.1364 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:25:10,863 - __main__ - INFO - Epoch   7/10 | Train Loss: 1.9795 Acc: 0.2353 | Val Loss: 2.0366 Acc: 0.1364 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:25:10,949 - __main__ - INFO - Epoch   8/10 | Train Loss: 1.9803 Acc: 0.2353 | Val Loss: 2.0344 Acc: 0.1364 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:25:11,025 - __main__ - INFO - Epoch   9/10 | Train Loss: 1.9953 Acc: 0.1961 | Val Loss: 2.0319 Acc: 0.1364 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:25:11,105 - __main__ - INFO - Epoch  10/10 | Train Loss: 1.9796 Acc: 0.2745 | Val Loss: 2.0321 Acc: 0.1364 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:25:11,106 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:25:11,106 - __main__ - INFO - 最佳验证准确率: 0.2273
2025-06-26 08:25:11,465 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 08:25:11,465 - __main__ - INFO - ==================================================
2025-06-26 08:25:11,465 - __main__ - INFO - 评估分类器性能
2025-06-26 08:25:11,465 - __main__ - INFO - ==================================================
2025-06-26 08:25:11,798 - __main__ - INFO - 分类性能指标:
2025-06-26 08:25:11,798 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 08:25:11,798 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 08:25:11,798 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 08:25:11,798 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 08:25:12,212 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 08:25:12,212 - __main__ - INFO - ==================================================
2025-06-26 08:25:12,212 - __main__ - INFO - 性能对比总结
2025-06-26 08:25:12,212 - __main__ - INFO - ==================================================
2025-06-26 08:25:12,212 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 08:25:12,212 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:25:12,213 - __main__ - INFO -   精确率: 0.0156 → 0.0156 (Δ+0.0000)
2025-06-26 08:25:12,213 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:25:12,213 - __main__ - INFO -   F1分数: 0.0277 → 0.0277 (Δ+0.0000)
2025-06-26 08:25:12,213 - __main__ - INFO - 🏁 实验结束
2025-06-26 08:25:12,213 - __main__ - INFO - ================================================================================
2025-06-26 08:25:12,213 - __main__ - INFO - 当前实验配置
2025-06-26 08:25:12,213 - __main__ - INFO - ================================================================================
2025-06-26 08:25:12,214 - __main__ - INFO - 数据集: KAT
2025-06-26 08:25:12,214 - __main__ - INFO - 故障样本每类: 3
2025-06-26 08:25:12,214 - __main__ - INFO - 健康样本总数: 3
2025-06-26 08:25:12,214 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:25:12,214 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:25:12,214 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:25:12,214 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:25:12,214 - __main__ - INFO - 只生成故障样本: True
2025-06-26 08:25:12,214 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:25:12,214 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:25:12,215 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:25:12,215 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:25:12,215 - __main__ - INFO - 设备: auto
2025-06-26 08:25:12,215 - __main__ - INFO - 性能模式: auto
2025-06-26 08:25:12,215 - __main__ - INFO - 随机种子: 42
2025-06-26 08:25:12,215 - __main__ - INFO - ================================================================================
2025-06-26 08:25:12,281 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_081508\individual_experiments\experiment_004
2025-06-26 08:25:12,281 - __main__ - INFO - 实验 4 完成
2025-06-26 08:25:12,282 - common.experiment_manager - INFO - 对比实验结果汇总已保存: results\KAT\20250626_081508\comparison_summary\comparison_results_summary.csv
2025-06-26 08:25:12,282 - common.experiment_manager - INFO - ================================================================================
2025-06-26 08:25:12,282 - common.experiment_manager - INFO - 对比实验结果汇总
2025-06-26 08:25:12,282 - common.experiment_manager - INFO - ================================================================================
2025-06-26 08:25:12,288 - common.experiment_manager - INFO - 
数据集: KAT
2025-06-26 08:25:12,288 - common.experiment_manager - INFO - ----------------------------------------
2025-06-26 08:25:12,288 - common.experiment_manager - INFO - 实验  1: 方法=CDDPM    | 样本=N/A | 生成=  5 | 准确率=0.1249 | 提升=+0.0000
2025-06-26 08:25:12,289 - common.experiment_manager - INFO - 实验  2: 方法=CDDPM    | 样本=N/A | 生成=  8 | 准确率=0.1249 | 提升=+0.0000
2025-06-26 08:25:12,289 - common.experiment_manager - INFO - 实验  3: 方法=CDDPM    | 样本=N/A | 生成=  5 | 准确率=0.1249 | 提升=+0.0000
2025-06-26 08:25:12,289 - common.experiment_manager - INFO - 实验  4: 方法=CDDPM    | 样本=N/A | 生成=  8 | 准确率=0.1249 | 提升=+0.0000
2025-06-26 08:25:12,289 - common.experiment_manager - INFO - 
最佳结果:
2025-06-26 08:25:12,289 - common.experiment_manager - INFO -   实验 1: 准确率 0.1249
2025-06-26 08:25:12,290 - common.experiment_manager - INFO -   参数: 方法=CDDPM, 样本=N/A, 生成=5
2025-06-26 08:25:12,310 - common.results_manager - INFO - 对比实验汇总已保存: results\KAT\20250626_081508\comparison_summary\comparison_summary.csv
2025-06-26 08:25:12,310 - __main__ - INFO - ================================================================================
2025-06-26 08:25:12,310 - __main__ - INFO - 对比实验全部完成，总用时: 10:04
2025-06-26 08:25:12,310 - __main__ - INFO - 结果保存在: results\KAT\20250626_081508
2025-06-26 08:25:12,310 - __main__ - INFO - 缓存配置保存在: cache\20250626_081508
2025-06-26 08:25:12,310 - __main__ - INFO - ================================================================================
2025-06-26 08:25:12,311 - __main__ - INFO - 实验完成
2025-06-26 08:25:12,311 - __main__ - INFO - 程序结束
