"""
传统数据增强方法模块
实现ADASYN、SMOTEENN、K-means-SMOTE等传统方法
"""

import numpy as np
from typing import Tuple, Dict
import logging
from sklearn.preprocessing import StandardScaler
import warnings

# 尝试导入imblearn，如果失败则设置标志
try:
    from imblearn.over_sampling import ADASYN, SMOTE
    from imblearn.combine import SMOTEENN
    from imblearn.over_sampling import KMeansSMOTE
    IMBLEARN_AVAILABLE = True
except ImportError:
    IMBLEARN_AVAILABLE = False
    ADASYN = SMOTE = SMOTEENN = KMeansSMOTE = None

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class TraditionalAugmentation:
    """传统数据增强方法类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.method_name = "Traditional"
        
    def generate_samples_adasyn(self, train_data: np.ndarray, train_labels: np.ndarray,
                               num_generated_per_class: int) -> <PERSON>ple[np.ndarray, np.ndarray]:
        """
        使用ADASYN生成样本

        Args:
            train_data: 训练数据 [N, signal_length]
            train_labels: 训练标签 [N]
            num_generated_per_class: 每个类别生成的样本数

        Returns:
            生成的数据和标签
        """
        if not IMBLEARN_AVAILABLE:
            logger.warning("imblearn未安装，无法使用ADASYN，使用RWO替代")
            return self.generate_samples_rwo(train_data, train_labels, num_generated_per_class)

        logger.info("使用ADASYN生成样本...")

        try:
            # 获取配置
            adasyn_config = self.config['augmentation']['traditional']['adasyn']
            
            # 计算目标样本数 - 修复ADASYN采样策略
            unique_labels, counts = np.unique(train_labels, return_counts=True)

            # ADASYN需要所有类别有相同的目标数量
            max_count = max(counts)
            target_count = max_count + num_generated_per_class

            # 为每个类别设置相同的目标样本数
            sampling_strategy = {label: target_count for label in unique_labels}

            logger.info(f"ADASYN采样策略: {sampling_strategy}")
            logger.info(f"当前类别分布: {dict(zip(unique_labels, counts))}")
            
            # 创建ADASYN对象 - 添加错误处理
            try:
                adasyn = ADASYN(
                    sampling_strategy=sampling_strategy,
                    n_neighbors=min(adasyn_config['n_neighbors'], len(train_data) - 1),
                    random_state=adasyn_config['random_state']
                )
            except Exception as e:
                logger.warning(f"ADASYN参数调整失败，使用默认参数: {e}")
                adasyn = ADASYN(random_state=adasyn_config['random_state'])
            
            # 生成样本
            X_resampled, y_resampled = adasyn.fit_resample(train_data, train_labels)
            
            # 提取新生成的样本
            original_size = len(train_data)
            generated_data = X_resampled[original_size:]
            generated_labels = y_resampled[original_size:]
            
            logger.info(f"ADASYN生成完成，生成 {len(generated_data)} 个样本")
            
            return generated_data, generated_labels
            
        except Exception as e:
            logger.error(f"ADASYN生成失败: {e}")
            # 返回空数组
            return np.array([]), np.array([])
    
    def generate_samples_smoteenn(self, train_data: np.ndarray, train_labels: np.ndarray,
                                 num_generated_per_class: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用SMOTEENN生成样本

        Args:
            train_data: 训练数据 [N, signal_length]
            train_labels: 训练标签 [N]
            num_generated_per_class: 每个类别生成的样本数

        Returns:
            生成的数据和标签
        """
        if not IMBLEARN_AVAILABLE:
            logger.warning("imblearn未安装，无法使用SMOTEENN，使用RWO替代")
            return self.generate_samples_rwo(train_data, train_labels, num_generated_per_class)

        logger.info("使用SMOTEENN生成样本...")

        try:
            # 获取配置
            smoteenn_config = self.config['augmentation']['traditional']['smoteenn']
            
            # 计算目标样本数 - 修复SMOTEENN采样策略
            unique_labels, counts = np.unique(train_labels, return_counts=True)

            # 为每个类别设置目标样本数
            sampling_strategy = {}
            for label, count in zip(unique_labels, counts):
                sampling_strategy[label] = count + num_generated_per_class

            logger.info(f"SMOTEENN采样策略: {sampling_strategy}")
            
            # 创建SMOTEENN对象
            smoteenn = SMOTEENN(
                sampling_strategy=sampling_strategy,
                random_state=smoteenn_config['random_state']
            )
            
            # 生成样本
            X_resampled, y_resampled = smoteenn.fit_resample(train_data, train_labels)
            
            # 提取新生成的样本（SMOTEENN可能会减少样本）
            if len(X_resampled) > len(train_data):
                original_size = len(train_data)
                generated_data = X_resampled[original_size:]
                generated_labels = y_resampled[original_size:]
            else:
                # 如果样本被减少，使用SMOTE单独生成
                smote = SMOTE(sampling_strategy=sampling_strategy, random_state=42)
                X_smote, y_smote = smote.fit_resample(train_data, train_labels)
                original_size = len(train_data)
                generated_data = X_smote[original_size:]
                generated_labels = y_smote[original_size:]
            
            logger.info(f"SMOTEENN生成完成，生成 {len(generated_data)} 个样本")
            
            return generated_data, generated_labels
            
        except Exception as e:
            logger.error(f"SMOTEENN生成失败: {e}")
            # 返回空数组
            return np.array([]), np.array([])
    
    def generate_samples_kmeans_smote(self, train_data: np.ndarray, train_labels: np.ndarray,
                                     num_generated_per_class: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用K-means-SMOTE生成样本

        Args:
            train_data: 训练数据 [N, signal_length]
            train_labels: 训练标签 [N]
            num_generated_per_class: 每个类别生成的样本数

        Returns:
            生成的数据和标签
        """
        if not IMBLEARN_AVAILABLE:
            logger.warning("imblearn未安装，无法使用K-means-SMOTE，使用RWO替代")
            return self.generate_samples_rwo(train_data, train_labels, num_generated_per_class)

        logger.info("使用K-means-SMOTE生成样本...")

        try:
            # 获取配置
            kmeans_config = self.config['augmentation']['traditional']['kmeans_smote']
            
            # 计算目标样本数 - 修复K-means-SMOTE采样策略
            unique_labels, counts = np.unique(train_labels, return_counts=True)

            # 为每个类别设置目标样本数
            sampling_strategy = {}
            for label, count in zip(unique_labels, counts):
                sampling_strategy[label] = count + num_generated_per_class

            logger.info(f"K-means-SMOTE采样策略: {sampling_strategy}")
            
            # 创建K-means-SMOTE对象
            kmeans_smote = KMeansSMOTE(
                sampling_strategy=sampling_strategy,
                k_neighbors=kmeans_config['k_neighbors'],
                cluster_balance_threshold=kmeans_config['cluster_balance_threshold'],
                random_state=kmeans_config['random_state']
            )
            
            # 生成样本
            X_resampled, y_resampled = kmeans_smote.fit_resample(train_data, train_labels)
            
            # 提取新生成的样本
            original_size = len(train_data)
            generated_data = X_resampled[original_size:]
            generated_labels = y_resampled[original_size:]
            
            logger.info(f"K-means-SMOTE生成完成，生成 {len(generated_data)} 个样本")
            
            return generated_data, generated_labels
            
        except Exception as e:
            logger.error(f"K-means-SMOTE生成失败: {e}")
            # 返回空数组
            return np.array([]), np.array([])
    
    def generate_samples_rwo(self, train_data: np.ndarray, train_labels: np.ndarray, 
                            num_generated_per_class: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用Random Walk Oversampling (RWO)生成样本
        
        Args:
            train_data: 训练数据 [N, signal_length]
            train_labels: 训练标签 [N]
            num_generated_per_class: 每个类别生成的样本数
            
        Returns:
            生成的数据和标签
        """
        logger.info("使用RWO生成样本...")
        
        try:
            generated_data = []
            generated_labels = []
            
            unique_labels = np.unique(train_labels)
            
            for label in unique_labels:
                # 获取当前类别的样本
                class_indices = np.where(train_labels == label)[0]
                class_data = train_data[class_indices]
                
                if len(class_data) < 2:
                    logger.warning(f"类别 {label} 样本数不足，跳过RWO生成")
                    continue
                
                # 为每个类别生成指定数量的样本
                for _ in range(num_generated_per_class):
                    # 随机选择两个样本
                    idx1, idx2 = np.random.choice(len(class_data), 2, replace=False)
                    sample1, sample2 = class_data[idx1], class_data[idx2]
                    
                    # 随机游走插值
                    alpha = np.random.random()
                    new_sample = alpha * sample1 + (1 - alpha) * sample2
                    
                    # 添加小量噪声
                    noise = np.random.normal(0, 0.01, new_sample.shape)
                    new_sample += noise
                    
                    generated_data.append(new_sample)
                    generated_labels.append(label)
            
            generated_data = np.array(generated_data)
            generated_labels = np.array(generated_labels)
            
            logger.info(f"RWO生成完成，生成 {len(generated_data)} 个样本")
            
            return generated_data, generated_labels
            
        except Exception as e:
            logger.error(f"RWO生成失败: {e}")
            # 返回空数组
            return np.array([]), np.array([])
    
    def generate_samples(self, method: str, train_data: np.ndarray, train_labels: np.ndarray, 
                        num_generated_per_class: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        根据方法名生成样本
        
        Args:
            method: 方法名称
            train_data: 训练数据
            train_labels: 训练标签
            num_generated_per_class: 每个类别生成的样本数
            
        Returns:
            生成的数据和标签
        """
        method = method.upper()
        
        if method == 'ADASYN':
            return self.generate_samples_adasyn(train_data, train_labels, num_generated_per_class)
        elif method == 'SMOTEENN':
            return self.generate_samples_smoteenn(train_data, train_labels, num_generated_per_class)
        elif method == 'KMEANS_SMOTE' or method == 'K-MEANS-SMOTE':
            return self.generate_samples_kmeans_smote(train_data, train_labels, num_generated_per_class)
        elif method == 'RWO' or method == 'RWO_SAMPLING':
            return self.generate_samples_rwo(train_data, train_labels, num_generated_per_class)
        else:
            logger.error(f"不支持的传统增强方法: {method}")
            return np.array([]), np.array([])
    
    def get_training_results(self) -> Dict:
        """
        获取训练结果（传统方法不需要训练）
        
        Returns:
            空的训练结果字典
        """
        return {
            'method': 'Traditional',
            'training_time': 0.0,
            'note': 'Traditional methods do not require training'
        }
