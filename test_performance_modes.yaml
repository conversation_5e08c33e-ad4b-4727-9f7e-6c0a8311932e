# 性能模式配置测试
# 验证epochs使用原始配置，num_workers自动判断功能

# ================================================================================
# 1. 数据集配置
# ================================================================================
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载配置
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    fault_samples:
      max_fault_samples_per_class: [3]  # 小数据集

    healthy_samples:
      max_healthy_samples: -1
      healthy_label: 0

# ================================================================================
# 2. 数据增强配置
# ================================================================================
augmentation:
  method: "CDDPM"
  num_generated_per_class: [5]
  save_generated: true
  generate_fault_only: true

  classifier_healthy_samples:
    use_real_when_no_generated: true
    real_healthy_count: -1

  generation_strategy:
    target_samples_per_class: -1
    initial_multiplier: 3.0
    min_multiplier: 2.0
    max_multiplier: 5.0
    adaptive_generation: true

  # CDDPM参数
  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02
    unconditional_prob: 0.1
    guidance_scale: 1.0

# ================================================================================
# 3. 模型配置
# ================================================================================
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# ================================================================================
# 4. 训练配置 - 原始配置，性能模式只调整批次大小
# ================================================================================
training:
  # 扩散模型训练参数（原始配置）
  diffusion:
    epochs: 300                       # 原始配置：300轮
    batch_size: 64                    # 原始配置：64批次
    learning_rate: 0.00001            # 原始配置：1e-5
    weight_decay: 0.0001

    best_model_criteria:
      metric: "weighted_loss"
      mode: "min"
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

    scheduler:
      type: "cosine"
      T_max: 300
      eta_min: 0.000001

    early_stopping:
      enabled: true
      patience: 50
      min_delta: 0.001
      monitor: "weighted_loss"
      mode: "min"
      restore_best_weights: true
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

  # 分类器训练参数（原始配置）
  classifier:
    epochs: 50                        # 原始配置：50轮
    batch_size: 64                    # 原始配置：64批次
    learning_rate: 0.0001             # 原始配置：1e-4
    weight_decay: 0.01

    best_model_criteria:
      metric: "val_loss"
      mode: "min"

    scheduler:
      type: "cosine"
      T_max: 50
      eta_min: 0.00001

    early_stopping:
      enabled: true
      patience: 20
      min_delta: 0.001
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

# ================================================================================
# 5. 数据筛选配置
# ================================================================================
data_screening:
  enabled: false

# ================================================================================
# 6. 评估配置
# ================================================================================
evaluation:
  metrics:
    classification: ["accuracy", "precision", "recall", "f1_score"]
    generation: ["gan_train", "gan_test"]
  
  gan_evaluation:
    classifier_epochs: 100
    batch_size: 64
    
  visualization:
    save_confusion_matrix: true
    save_tsne: true
    tsne_perplexity: 30
    tsne_n_iter: 1000
    save_sample_plots: true
    num_samples_to_plot: 10

# ================================================================================
# 7. 系统配置 - 测试auto功能
# ================================================================================
system:
  device: "auto"
  performance_mode: "standard"       # 测试标准模式
  seed: 42
  num_workers: "auto"                 # 测试自动判断功能
  pin_memory: false

  optimization:
    use_amp: false
    compile_model: false
    channels_last: false
    benchmark: true

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_process_checkpoints: true
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 3

# ================================================================================
# 8. 实验配置
# ================================================================================
experiment:
  name: "test_performance_modes"
  description: "测试性能模式配置：epochs使用原始配置，num_workers自动判断"
  tags: ["performance_modes", "auto_num_workers", "epochs_consistency"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# ================================================================================
# 9. 性能模式配置 - 修复后的配置
# ================================================================================
performance_profiles:
  # 标准模式 - 只调整批次大小，epochs使用原始配置
  standard:
    training:
      diffusion:
        batch_size: 64                # 论文中统一使用64
        # epochs使用原始training.diffusion.epochs配置（300轮）
      classifier:
        batch_size: 64                # 论文中统一使用64
        # epochs使用原始training.classifier.epochs配置（50轮）
    system:
      num_workers: "auto"             # 自动根据系统类型判断
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  # 快速模式 - 减少批次大小和轮数
  fast:
    training:
      diffusion:
        batch_size: 32                # 较小批次
        epochs: 50                    # 减少轮数用于快速测试
      classifier:
        batch_size: 32                # 较小批次
        epochs: 20                    # 减少轮数用于快速测试
    system:
      num_workers: "auto"             # 自动根据系统类型判断
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  # 高性能模式 - 增大批次大小，epochs使用原始配置
  high_performance:
    training:
      diffusion:
        batch_size: 128               # 大批次
        # epochs使用原始training.diffusion.epochs配置（300轮）
      classifier:
        batch_size: 128               # 大批次
        # epochs使用原始training.classifier.epochs配置（50轮）
    system:
      num_workers: "auto"             # 自动根据系统类型判断
      pin_memory: true
      optimization:
        use_amp: true
        compile_model: true
        channels_last: true
        benchmark: true

  # 超高性能模式 - 超大批次，epochs使用原始配置
  ultra:
    training:
      diffusion:
        batch_size: 256               # 超大批次
        # epochs使用原始training.diffusion.epochs配置（300轮）
      classifier:
        batch_size: 256               # 超大批次
        # epochs使用原始training.classifier.epochs配置（50轮）
    system:
      num_workers: "auto"             # 自动根据系统类型判断
      pin_memory: true
      optimization:
        use_amp: true
        compile_model: true
        channels_last: true
        benchmark: true
