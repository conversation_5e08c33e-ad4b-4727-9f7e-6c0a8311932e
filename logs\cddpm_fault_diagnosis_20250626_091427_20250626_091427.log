2025-06-26 09:14:27,814 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_091427_20250626_091427.log
2025-06-26 09:14:27,814 - __main__ - INFO - ================================================================================
2025-06-26 09:14:27,814 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 09:14:27,814 - __main__ - INFO - 数据集: KAT
2025-06-26 09:14:27,814 - __main__ - INFO - ================================================================================
2025-06-26 09:14:27,814 - __main__ - INFO - 🚀 实验开始
2025-06-26 09:14:27,814 - __main__ - INFO - ================================================================================
2025-06-26 09:14:27,815 - __main__ - INFO - 当前实验配置
2025-06-26 09:14:27,815 - __main__ - INFO - ================================================================================
2025-06-26 09:14:27,815 - __main__ - INFO - 数据集: KAT
2025-06-26 09:14:27,815 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 09:14:27,815 - __main__ - INFO - 健康样本总数: -1
2025-06-26 09:14:27,815 - __main__ - INFO - 信号长度: 1024
2025-06-26 09:14:27,815 - __main__ - INFO - 归一化方法: minmax
2025-06-26 09:14:27,815 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 09:14:27,815 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 09:14:27,815 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:14:27,815 - __main__ - INFO - 扩散模型训练轮数: 100
2025-06-26 09:14:27,816 - __main__ - INFO - 分类器训练轮数: 100
2025-06-26 09:14:27,816 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 09:14:27,816 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 09:14:27,816 - __main__ - INFO - 设备: auto
2025-06-26 09:14:27,816 - __main__ - INFO - 性能模式: auto
2025-06-26 09:14:27,816 - __main__ - INFO - 随机种子: 42
2025-06-26 09:14:27,816 - __main__ - INFO - ================================================================================
2025-06-26 09:14:27,816 - __main__ - INFO - ============================================================
2025-06-26 09:14:27,816 - __main__ - INFO - 健康样本配置验证
2025-06-26 09:14:27,816 - __main__ - INFO - ============================================================
2025-06-26 09:14:27,816 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 09:14:27,817 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:14:27,817 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 09:14:27,817 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 09:14:27,817 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 09:14:27,817 - __main__ - INFO - ============================================================
2025-06-26 09:14:27,817 - __main__ - INFO - 使用设备: cuda
2025-06-26 09:14:27,818 - __main__ - INFO - 加载数据...
2025-06-26 09:14:27,818 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 09:14:27,818 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 09:14:27,820 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:14:27,822 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:14:27,822 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:14:27,823 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:14:27,823 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 09:14:27,823 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,824 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 09:14:27,825 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:27,828 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 09:14:27,833 - common.data_loader - INFO - 数据加载完成:
2025-06-26 09:14:27,833 - common.data_loader - INFO -   训练样本: 24
2025-06-26 09:14:27,833 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 09:14:27,834 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 09:14:27,834 - common.data_loader - INFO -   类别数: 8
2025-06-26 09:14:27,834 - __main__ - INFO - ==================================================
2025-06-26 09:14:27,834 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 09:14:27,834 - __main__ - INFO - ==================================================
2025-06-26 09:14:27,835 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 09:14:27,835 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:14:27,835 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:14:27,836 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:14:27,836 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:14:28,037 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:14:28,038 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:14:28,038 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:14:28,039 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:14:28,179 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 09:14:28,180 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 09:14:28,180 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共100轮
2025-06-26 09:14:34,869 - models.augmentation_factory - INFO - Epoch   1/100: Train Loss: 0.835232, Val Loss: 0.853872 (Best✓)
2025-06-26 09:14:35,140 - models.augmentation_factory - INFO - Epoch   2/100: Train Loss: 0.849903, Val Loss: 0.826392 (Best✓)
2025-06-26 09:14:35,413 - models.augmentation_factory - INFO - Epoch   3/100: Train Loss: 0.818965, Val Loss: 0.803280 (Best✓)
2025-06-26 09:14:36,197 - models.augmentation_factory - INFO - Epoch   6/100: Train Loss: 0.810409, Val Loss: 0.797106 (Best✓)
2025-06-26 09:14:37,538 - models.augmentation_factory - INFO - Epoch  11/100: Train Loss: 0.802077, Val Loss: 0.793362 (Best✓)
2025-06-26 09:14:37,804 - models.augmentation_factory - INFO - Epoch  12/100: Train Loss: 0.803089, Val Loss: 0.788938 (Best✓)
2025-06-26 09:14:39,654 - models.augmentation_factory - INFO - Epoch  19/100: Train Loss: 0.793557, Val Loss: 0.771601 (Best✓)
2025-06-26 09:14:40,709 - models.augmentation_factory - INFO - Epoch  23/100: Train Loss: 0.778501, Val Loss: 0.755200 (Best✓)
2025-06-26 09:14:40,972 - models.augmentation_factory - INFO - Epoch  24/100: Train Loss: 0.782986, Val Loss: 0.744274 (Best✓)
2025-06-26 09:14:41,235 - models.augmentation_factory - INFO - Epoch  25/100: Train Loss: 0.759405, Val Loss: 0.716615 (Best✓)
2025-06-26 09:14:41,502 - models.augmentation_factory - INFO - Epoch  26/100: Train Loss: 0.743861, Val Loss: 0.709139 (Best✓)
2025-06-26 09:14:41,767 - models.augmentation_factory - INFO - Epoch  27/100: Train Loss: 0.711316, Val Loss: 0.680394 (Best✓)
2025-06-26 09:14:42,030 - models.augmentation_factory - INFO - Epoch  28/100: Train Loss: 0.703461, Val Loss: 0.675231 (Best✓)
2025-06-26 09:14:42,295 - models.augmentation_factory - INFO - Epoch  29/100: Train Loss: 0.667764, Val Loss: 0.642358 (Best✓)
2025-06-26 09:14:42,816 - models.augmentation_factory - INFO - Epoch  31/100: Train Loss: 0.661316, Val Loss: 0.630177 (Best✓)
2025-06-26 09:14:43,608 - models.augmentation_factory - INFO - Epoch  34/100: Train Loss: 0.608859, Val Loss: 0.593665 (Best✓)
2025-06-26 09:14:43,868 - models.augmentation_factory - INFO - Epoch  35/100: Train Loss: 0.617162, Val Loss: 0.566971 (Best✓)
2025-06-26 09:14:44,388 - models.augmentation_factory - INFO - Epoch  37/100: Train Loss: 0.574798, Val Loss: 0.540907 (Best✓)
2025-06-26 09:14:44,651 - models.augmentation_factory - INFO - Epoch  38/100: Train Loss: 0.569497, Val Loss: 0.497758 (Best✓)
2025-06-26 09:14:45,181 - models.augmentation_factory - INFO - Epoch  40/100: Train Loss: 0.521958, Val Loss: 0.464558 (Best✓)
2025-06-26 09:14:45,722 - models.augmentation_factory - INFO - Epoch  42/100: Train Loss: 0.488807, Val Loss: 0.457349 (Best✓)
2025-06-26 09:14:46,247 - models.augmentation_factory - INFO - Epoch  44/100: Train Loss: 0.448344, Val Loss: 0.373938 (Best✓)
2025-06-26 09:14:47,301 - models.augmentation_factory - INFO - Epoch  48/100: Train Loss: 0.339113, Val Loss: 0.358461 (Best✓)
2025-06-26 09:14:47,567 - models.augmentation_factory - INFO - Epoch  49/100: Train Loss: 0.341332, Val Loss: 0.293905 (Best✓)
2025-06-26 09:14:47,830 - models.augmentation_factory - INFO - Epoch  50/100: Train Loss: 0.324266, Val Loss: 0.312062
2025-06-26 09:14:48,615 - models.augmentation_factory - INFO - Epoch  53/100: Train Loss: 0.296441, Val Loss: 0.291885 (Best✓)
2025-06-26 09:14:49,135 - models.augmentation_factory - INFO - Epoch  55/100: Train Loss: 0.253268, Val Loss: 0.217092 (Best✓)
2025-06-26 09:14:49,659 - models.augmentation_factory - INFO - Epoch  57/100: Train Loss: 0.342964, Val Loss: 0.213535 (Best✓)
2025-06-26 09:14:50,986 - models.augmentation_factory - INFO - Epoch  62/100: Train Loss: 0.189761, Val Loss: 0.209708 (Best✓)
2025-06-26 09:14:52,041 - models.augmentation_factory - INFO - Epoch  66/100: Train Loss: 0.278795, Val Loss: 0.199296 (Best✓)
2025-06-26 09:14:52,307 - models.augmentation_factory - INFO - Epoch  67/100: Train Loss: 0.303743, Val Loss: 0.173709 (Best✓)
2025-06-26 09:14:52,835 - models.augmentation_factory - INFO - Epoch  69/100: Train Loss: 0.289668, Val Loss: 0.156666 (Best✓)
2025-06-26 09:14:53,910 - models.augmentation_factory - INFO - Epoch  73/100: Train Loss: 0.199390, Val Loss: 0.092775 (Best✓)
2025-06-26 09:15:01,003 - models.augmentation_factory - INFO - Epoch 100/100: Train Loss: 0.176817, Val Loss: 0.122193
2025-06-26 09:15:01,003 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 09:15:01,474 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 09:15:01,475 - __main__ - INFO - ==================================================
2025-06-26 09:15:01,475 - __main__ - INFO - 生成增强样本
2025-06-26 09:15:01,475 - __main__ - INFO - ==================================================
2025-06-26 09:15:02,024 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:15:02,024 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 09:15:02,024 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:15:02,025 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:15:02,025 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:15:02,025 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:15:02,179 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 09:15:02,179 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 09:15:02,179 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 09:15:02,179 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 09:15:02,645 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 09:15:02,647 - __main__ - WARNING - 配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: [3]
2025-06-26 09:15:02,647 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 3 个...
2025-06-26 09:15:02,647 - __main__ - INFO - 只生成故障样本，跳过健康样本（标签=0）
2025-06-26 09:15:02,647 - __main__ - INFO - 跳过类别 0 (健康样本)
2025-06-26 09:15:02,647 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 09:15:19,672 - __main__ - INFO - 生成类别 2 的样本...
