# 早停功能测试配置
# 测试早停功能是否有效，使用较小的耐心值

# ================================================================================
# 1. 数据集配置
# ================================================================================
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载配置
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    fault_samples:
      max_fault_samples_per_class: [2]  # 小数据集

    healthy_samples:
      max_healthy_samples: -1
      healthy_label: 0

# ================================================================================
# 2. 数据增强配置
# ================================================================================
augmentation:
  method: "CDDPM"
  num_generated_per_class: [3]
  save_generated: true
  generate_fault_only: true

  classifier_healthy_samples:
    use_real_when_no_generated: true
    real_healthy_count: -1

  generation_strategy:
    target_samples_per_class: 10
    initial_multiplier: 5.0
    min_multiplier: 2.0
    max_multiplier: 10.0
    adaptive_generation: true

  # CDDPM参数
  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02
    unconditional_prob: 0.1
    guidance_scale: 1.0

# ================================================================================
# 3. 模型配置
# ================================================================================
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# ================================================================================
# 4. 训练配置 - 重点测试早停功能
# ================================================================================
training:
  # 扩散模型训练参数 - 测试早停
  diffusion:
    epochs: 100                       # 设置较多轮数，期望早停触发
    batch_size: 8
    learning_rate: 0.0001
    weight_decay: 0.0001

    # 最佳模型判断配置
    best_model_criteria:
      metric: "val_loss"              # 使用验证损失
      mode: "min"

      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

    # 学习率调度器
    scheduler:
      type: "cosine"
      T_max: 100
      eta_min: 0.00001

    # 早停配置 - 小耐心值测试早停
    early_stopping:
      enabled: true                   # 启用早停
      patience: 5                     # 很小的耐心值，容易触发早停
      min_delta: 0.001
      monitor: "val_loss"             # 监控验证损失
      mode: "min"
      restore_best_weights: true

      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

  # 分类器训练参数 - 测试早停
  classifier:
    epochs: 50                        # 设置较多轮数，期望早停触发
    batch_size: 16
    learning_rate: 0.0001
    weight_decay: 0.01

    # 最佳模型判断配置
    best_model_criteria:
      metric: "val_loss"
      mode: "min"

      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

    # 学习率调度器
    scheduler:
      type: "cosine"
      T_max: 50
      eta_min: 0.00001

    # 早停配置 - 小耐心值测试早停
    early_stopping:
      enabled: true                   # 启用早停
      patience: 3                     # 很小的耐心值，容易触发早停
      min_delta: 0.001
      monitor: "val_loss"             # 监控验证损失
      mode: "min"
      restore_best_weights: true

      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

# ================================================================================
# 5. 数据筛选配置
# ================================================================================
data_screening:
  enabled: false

# ================================================================================
# 6. 评估配置
# ================================================================================
evaluation:
  metrics:
    classification: true
    generation: false

# ================================================================================
# 7. 系统配置
# ================================================================================
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 3

# ================================================================================
# 8. 实验配置
# ================================================================================
experiment:
  name: "test_early_stopping"
  description: "测试早停功能是否有效"
  tags: ["early_stopping", "CDDPM"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# ================================================================================
# 9. 性能模式配置
# ================================================================================
performance_profiles:
  standard:
    training:
      diffusion:
        batch_size: 8
        epochs: 100
      classifier:
        batch_size: 16
        epochs: 50
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  fast:
    training:
      diffusion:
        batch_size: 4
        epochs: 20
      classifier:
        batch_size: 8
        epochs: 10
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  balanced:
    training:
      diffusion:
        batch_size: 16
        epochs: 100
      classifier:
        batch_size: 32
        epochs: 50
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  quality:
    training:
      diffusion:
        batch_size: 32
        epochs: 200
      classifier:
        batch_size: 64
        epochs: 100
    system:
      num_workers: 0
      pin_memory: true
      optimization:
        use_amp: true
        compile_model: true
        channels_last: true
        benchmark: true
