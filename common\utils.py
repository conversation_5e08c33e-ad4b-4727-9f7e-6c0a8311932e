"""
工具函数模块
包含项目中使用的各种工具函数
"""

import os
import random
import numpy as np
import torch
import yaml
import logging
from typing import Dict, Any, Optional
import time
from datetime import datetime
import json


def setup_logging(log_dir: str, experiment_name: str) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        log_dir: 日志目录
        experiment_name: 实验名称
        
    Returns:
        配置好的logger
    """
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{experiment_name}_{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    return logger


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config


def save_config(config: Dict[str, Any], save_path: str):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        save_path: 保存路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    with open(save_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)


def set_seed(seed: int):
    """
    设置随机种子以确保实验可重复性
    
    Args:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_device(device_config: str = "auto") -> torch.device:
    """
    获取计算设备
    
    Args:
        device_config: 设备配置 ("auto", "cpu", "cuda")
        
    Returns:
        torch设备对象
    """
    if device_config == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    elif device_config == "cuda":
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA不可用，但配置要求使用CUDA")
        device = torch.device("cuda")
    elif device_config == "cpu":
        device = torch.device("cpu")
    else:
        raise ValueError(f"不支持的设备配置: {device_config}")
    
    return device


def create_directories(config: Dict[str, Any]):
    """
    创建必要的目录结构
    
    Args:
        config: 配置字典
    """
    dirs_to_create = [
        config['system']['save']['checkpoints_dir'],
        config['system']['save']['results_dir'],
        config['system']['save']['logs_dir'],
        os.path.join(config['system']['save']['checkpoints_dir'], 'diffusion', 'best'),
        os.path.join(config['system']['save']['checkpoints_dir'], 'diffusion', 'process'),
        os.path.join(config['system']['save']['checkpoints_dir'], 'classifier', 'best'),
        os.path.join(config['system']['save']['checkpoints_dir'], 'classifier', 'process'),
        os.path.join(config['system']['save']['checkpoints_dir'], 'augmentation'),
    ]
    
    # 创建生成样本目录
    dataset_name = config['dataset']['name']
    gen_samples_dir = config['system']['save']['generated_samples_dir'].format(dataset_name=dataset_name)
    dirs_to_create.append(gen_samples_dir)

    # 处理数据增强方法（可能是字符串或列表）
    aug_method = config['augmentation']['method']
    if isinstance(aug_method, list):
        # 如果是列表，为每个方法创建目录
        for method in aug_method:
            dirs_to_create.append(os.path.join(gen_samples_dir, method))
            # 同时创建增强方法的检查点目录
            dirs_to_create.append(os.path.join(config['system']['save']['checkpoints_dir'], 'augmentation', method.lower()))
    else:
        # 如果是字符串，直接创建目录
        dirs_to_create.append(os.path.join(gen_samples_dir, aug_method))
        # 同时创建增强方法的检查点目录
        dirs_to_create.append(os.path.join(config['system']['save']['checkpoints_dir'], 'augmentation', aug_method.lower()))
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)


class Timer:
    """计时器类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
    
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
    
    def elapsed(self) -> float:
        """获取经过的时间（秒）"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time if self.end_time is not None else time.time()
        return end - self.start_time
    
    def elapsed_str(self) -> str:
        """获取格式化的经过时间"""
        elapsed = self.elapsed()
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        seconds = int(elapsed % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"


class EarlyStopping:
    """早停类 - 支持多种监控指标"""

    def __init__(self, patience: int = 10, min_delta: float = 0.0, monitor: str = "val_loss",
                 mode: str = "min", enabled: bool = True, restore_best_weights: bool = True,
                 weighted_loss_config: Optional[Dict] = None):
        """
        初始化早停

        Args:
            patience: 耐心值，即容忍多少个epoch没有改善
            min_delta: 最小改善阈值
            monitor: 监控的指标名称 ("train_loss", "val_loss", "weighted_loss")
            mode: 优化方向 ("min" 或 "max")
            enabled: 是否启用早停
            restore_best_weights: 是否恢复最佳权重
            weighted_loss_config: 加权损失配置（当monitor为"weighted_loss"时使用）
        """
        self.patience = patience
        self.min_delta = min_delta
        self.monitor = monitor
        self.mode = mode
        self.enabled = enabled
        self.restore_best_weights = restore_best_weights

        # 加权损失配置
        self.weighted_loss_config = weighted_loss_config or {}
        self.train_weight = self.weighted_loss_config.get('train_weight', 0.7)
        self.val_weight = self.weighted_loss_config.get('val_weight', 0.3)

        self.counter = 0
        self.best_score = None
        self.early_stop = False
        self.best_weights = None

        # 根据模式初始化最佳分数
        if self.mode == "min":
            self.best_score = float('inf')
        else:
            self.best_score = float('-inf')

    def __call__(self, train_loss: float = None, val_loss: float = None,
                 model_state_dict: dict = None) -> bool:
        """
        检查是否应该早停

        Args:
            train_loss: 训练损失
            val_loss: 验证损失
            model_state_dict: 模型状态字典（用于保存最佳权重）

        Returns:
            是否应该早停
        """
        if not self.enabled:
            return False

        # 根据监控指标计算当前分数
        if self.monitor == "train_loss":
            if train_loss is None:
                raise ValueError("train_loss is required when monitor='train_loss'")
            current_score = train_loss
        elif self.monitor == "val_loss":
            if val_loss is None:
                raise ValueError("val_loss is required when monitor='val_loss'")
            current_score = val_loss
        elif self.monitor == "weighted_loss":
            if train_loss is None or val_loss is None:
                raise ValueError("Both train_loss and val_loss are required when monitor='weighted_loss'")
            current_score = self.train_weight * train_loss + self.val_weight * val_loss
        else:
            raise ValueError(f"Unknown monitor: {self.monitor}")

        # 首次调用
        if self.best_score is None or (
            self.mode == "min" and self.best_score == float('inf')
        ) or (
            self.mode == "max" and self.best_score == float('-inf')
        ):
            self.best_score = current_score
            if model_state_dict is not None and self.restore_best_weights:
                self.best_weights = {k: v.clone() for k, v in model_state_dict.items()}
        elif self._is_better(current_score):
            self.best_score = current_score
            self.counter = 0
            if model_state_dict is not None and self.restore_best_weights:
                self.best_weights = {k: v.clone() for k, v in model_state_dict.items()}
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True

        return self.early_stop

    def _is_better(self, score: float) -> bool:
        """判断当前分数是否更好"""
        if self.mode == "min":
            # 越小越好（如损失）
            return score < self.best_score - self.min_delta
        else:
            # 越大越好（如准确率）
            return score > self.best_score + self.min_delta

    def get_best_weights(self):
        """获取最佳权重"""
        return self.best_weights


def save_results(results: Dict[str, Any], save_path: str):
    """
    保存实验结果
    
    Args:
        results: 结果字典
        save_path: 保存路径
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 转换numpy数组为列表以便JSON序列化
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        else:
            return obj
    
    results_converted = convert_numpy(results)
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(results_converted, f, indent=2, ensure_ascii=False)


def load_results(load_path: str) -> Dict[str, Any]:
    """
    加载实验结果
    
    Args:
        load_path: 加载路径
        
    Returns:
        结果字典
    """
    with open(load_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    return results


def format_time(seconds: float) -> str:
    """
    格式化时间
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"


def count_parameters(model: torch.nn.Module) -> int:
    """
    计算模型参数数量

    Args:
        model: PyTorch模型

    Returns:
        参数数量
    """
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def fix_compiled_state_dict(state_dict: dict) -> dict:
    """
    修复torch.compile导致的状态字典参数名问题

    Args:
        state_dict: 原始状态字典

    Returns:
        修复后的状态字典
    """
    if any(key.startswith('_orig_mod.') for key in state_dict.keys()):
        # 移除_orig_mod.前缀
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith('_orig_mod.'):
                new_key = key[10:]  # 移除'_orig_mod.'前缀
                new_state_dict[new_key] = value
            else:
                new_state_dict[key] = value
        return new_state_dict
    return state_dict


def manage_checkpoints(checkpoint_dir: str, max_keep: int):
    """
    管理检查点文件，删除多余的旧检查点

    Args:
        checkpoint_dir: 检查点目录
        max_keep: 最多保留的检查点数量
    """
    if not os.path.exists(checkpoint_dir):
        return

    # 获取所有检查点文件
    checkpoint_files = []
    for file in os.listdir(checkpoint_dir):
        if file.endswith('.pth') and file.startswith('epoch_'):
            file_path = os.path.join(checkpoint_dir, file)
            # 获取文件修改时间
            mtime = os.path.getmtime(file_path)
            checkpoint_files.append((file_path, mtime))

    # 按时间排序，最新的在前
    checkpoint_files.sort(key=lambda x: x[1], reverse=True)

    # 删除多余的检查点
    if len(checkpoint_files) > max_keep:
        for file_path, _ in checkpoint_files[max_keep:]:
            try:
                os.remove(file_path)
                logging.info(f"删除旧检查点: {file_path}")
            except Exception as e:
                logging.warning(f"删除检查点失败: {file_path}, 错误: {e}")
