"""
评估指标模块
包含分类和生成模型的各种评估指标
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
from sklearn.metrics import classification_report
from scipy import linalg
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ClassificationMetrics:
    """分类指标计算类"""
    
    @staticmethod
    def compute_metrics(y_true: np.ndarray, y_pred: np.ndarray, 
                       class_names: Optional[List[str]] = None) -> Dict[str, float]:
        """
        计算分类指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            class_names: 类别名称列表
            
        Returns:
            指标字典
        """
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }
        
        # 计算每个类别的指标
        precision_per_class = precision_score(y_true, y_pred, average=None, zero_division=0)
        recall_per_class = recall_score(y_true, y_pred, average=None, zero_division=0)
        f1_per_class = f1_score(y_true, y_pred, average=None, zero_division=0)
        
        if class_names is not None:
            for i, class_name in enumerate(class_names):
                if i < len(precision_per_class):
                    metrics[f'precision_{class_name}'] = precision_per_class[i]
                    metrics[f'recall_{class_name}'] = recall_per_class[i]
                    metrics[f'f1_{class_name}'] = f1_per_class[i]
        
        return metrics
    
    @staticmethod
    def compute_confusion_matrix(y_true: np.ndarray, y_pred: np.ndarray) -> np.ndarray:
        """
        计算混淆矩阵
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            
        Returns:
            混淆矩阵
        """
        return confusion_matrix(y_true, y_pred)
    
    @staticmethod
    def classification_report_dict(y_true: np.ndarray, y_pred: np.ndarray, 
                                 class_names: Optional[List[str]] = None) -> Dict:
        """
        生成分类报告字典
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            class_names: 类别名称列表
            
        Returns:
            分类报告字典
        """
        target_names = class_names if class_names is not None else None
        return classification_report(y_true, y_pred, target_names=target_names, 
                                   output_dict=True, zero_division=0)


class GenerationMetrics:
    """生成模型评估指标类"""
    
    @staticmethod
    def calculate_fid(real_features: np.ndarray, fake_features: np.ndarray) -> float:
        """
        计算Fréchet Inception Distance (FID)
        
        Args:
            real_features: 真实数据特征
            fake_features: 生成数据特征
            
        Returns:
            FID分数
        """
        # 计算均值和协方差
        mu1, sigma1 = real_features.mean(axis=0), np.cov(real_features, rowvar=False)
        mu2, sigma2 = fake_features.mean(axis=0), np.cov(fake_features, rowvar=False)
        
        # 计算均值差的平方
        ssdiff = np.sum((mu1 - mu2) ** 2.0)
        
        # 计算协方差矩阵的平方根
        covmean = linalg.sqrtm(sigma1.dot(sigma2))
        
        # 检查数值稳定性
        if np.iscomplexobj(covmean):
            covmean = covmean.real
        
        # 计算FID
        fid = ssdiff + np.trace(sigma1 + sigma2 - 2.0 * covmean)
        
        return float(fid)
    
    @staticmethod
    def calculate_is(predictions: np.ndarray, splits: int = 10) -> Tuple[float, float]:
        """
        计算Inception Score (IS)
        
        Args:
            predictions: 预测概率分布
            splits: 分割数量
            
        Returns:
            IS均值和标准差
        """
        # 计算边际分布
        p_y = np.mean(predictions, axis=0)
        
        # 计算每个分割的IS
        scores = []
        for i in range(splits):
            part = predictions[i * (len(predictions) // splits): (i + 1) * (len(predictions) // splits)]
            p_yx = np.mean(part, axis=0)
            
            # 计算KL散度
            kl_div = np.sum(p_yx * np.log(p_yx / p_y + 1e-8))
            scores.append(np.exp(kl_div))
        
        return np.mean(scores), np.std(scores)


class GANEvaluator:
    """GAN评估器，用于计算GAN-train和GAN-test指标"""
    
    def __init__(self, classifier_class, config: Dict):
        """
        初始化GAN评估器
        
        Args:
            classifier_class: 分类器类
            config: 配置字典
        """
        self.classifier_class = classifier_class
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    def _train_classifier(self, train_data: torch.Tensor, train_labels: torch.Tensor,
                         epochs: int = 100) -> nn.Module:
        """
        训练分类器
        
        Args:
            train_data: 训练数据
            train_labels: 训练标签
            epochs: 训练轮数
            
        Returns:
            训练好的分类器
        """
        # 创建分类器
        classifier = self.classifier_class(self.config)
        classifier = classifier.to(self.device)
        
        # 创建数据加载器
        dataset = torch.utils.data.TensorDataset(train_data, train_labels)
        dataloader = torch.utils.data.DataLoader(
            dataset, 
            batch_size=self.config['evaluation']['gan_evaluation']['batch_size'],
            shuffle=True
        )
        
        # 训练设置
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(classifier.parameters(), lr=0.001)
        
        classifier.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch_data, batch_labels in dataloader:
                batch_data = batch_data.to(self.device)
                batch_labels = batch_labels.to(self.device)
                
                optimizer.zero_grad()
                outputs = classifier(batch_data)
                loss = criterion(outputs, batch_labels)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if (epoch + 1) % 20 == 0:
                logger.info(f"分类器训练 Epoch {epoch+1}/{epochs}, Loss: {total_loss/len(dataloader):.4f}")
        
        return classifier
    
    def _evaluate_classifier(self, classifier: nn.Module, test_data: torch.Tensor, 
                           test_labels: torch.Tensor) -> float:
        """
        评估分类器
        
        Args:
            classifier: 分类器模型
            test_data: 测试数据
            test_labels: 测试标签
            
        Returns:
            准确率
        """
        classifier.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            # 分批处理以避免内存问题
            batch_size = self.config['evaluation']['gan_evaluation']['batch_size']
            for i in range(0, len(test_data), batch_size):
                batch_data = test_data[i:i+batch_size].to(self.device)
                batch_labels = test_labels[i:i+batch_size].to(self.device)
                
                outputs = classifier(batch_data)
                _, predicted = torch.max(outputs.data, 1)
                total += batch_labels.size(0)
                correct += (predicted == batch_labels).sum().item()
        
        accuracy = correct / total
        return accuracy
    
    def calculate_gan_train(self, generated_data: torch.Tensor, generated_labels: torch.Tensor,
                          real_test_data: torch.Tensor, real_test_labels: torch.Tensor) -> float:
        """
        计算GAN-train指标
        用生成数据训练分类器，在真实测试集上评估
        
        Args:
            generated_data: 生成的数据
            generated_labels: 生成数据的标签
            real_test_data: 真实测试数据
            real_test_labels: 真实测试标签
            
        Returns:
            GAN-train准确率
        """
        logger.info("计算GAN-train指标...")
        
        # 用生成数据训练分类器
        classifier = self._train_classifier(
            generated_data, generated_labels,
            epochs=self.config['evaluation']['gan_evaluation']['classifier_epochs']
        )
        
        # 在真实测试集上评估
        accuracy = self._evaluate_classifier(classifier, real_test_data, real_test_labels)
        
        logger.info(f"GAN-train准确率: {accuracy:.4f}")
        return accuracy
    
    def calculate_gan_test(self, real_train_data: torch.Tensor, real_train_labels: torch.Tensor,
                         generated_data: torch.Tensor, generated_labels: torch.Tensor) -> float:
        """
        计算GAN-test指标
        用真实训练数据训练分类器，在生成数据上评估
        
        Args:
            real_train_data: 真实训练数据
            real_train_labels: 真实训练标签
            generated_data: 生成的数据
            generated_labels: 生成数据的标签
            
        Returns:
            GAN-test准确率
        """
        logger.info("计算GAN-test指标...")
        
        # 用真实数据训练分类器
        classifier = self._train_classifier(
            real_train_data, real_train_labels,
            epochs=self.config['evaluation']['gan_evaluation']['classifier_epochs']
        )
        
        # 在生成数据上评估
        accuracy = self._evaluate_classifier(classifier, generated_data, generated_labels)
        
        logger.info(f"GAN-test准确率: {accuracy:.4f}")
        return accuracy


def extract_features(model: nn.Module, data_loader: torch.utils.data.DataLoader, 
                    device: torch.device) -> np.ndarray:
    """
    从模型中提取特征
    
    Args:
        model: 特征提取模型
        data_loader: 数据加载器
        device: 计算设备
        
    Returns:
        提取的特征
    """
    model.eval()
    features = []
    
    with torch.no_grad():
        for data, _ in data_loader:
            data = data.to(device)
            if hasattr(model, 'features'):
                feat = model.features(data)
            else:
                # 如果模型没有features方法，使用倒数第二层的输出
                feat = model(data)
                if len(feat.shape) > 2:
                    feat = feat.view(feat.size(0), -1)
            
            features.append(feat.cpu().numpy())
    
    return np.concatenate(features, axis=0)
