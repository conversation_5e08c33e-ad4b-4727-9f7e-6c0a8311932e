2025-06-26 08:50:11,328 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\test_healthy_sample_handling_comparison_20250626_085011_20250626_085011.log
2025-06-26 08:50:11,329 - common.experiment_manager - INFO - 检测到训练数据参数变化，需要重新训练扩散模型
2025-06-26 08:50:11,329 - __main__ - INFO - ================================================================================
2025-06-26 08:50:11,329 - __main__ - INFO - 检测到对比实验配置
2025-06-26 08:50:11,330 - __main__ - INFO - 实验名称: test_healthy_sample_handling
2025-06-26 08:50:11,330 - __main__ - INFO - 数据集: KAT
2025-06-26 08:50:11,330 - __main__ - INFO - 总实验数: 4
2025-06-26 08:50:11,330 - __main__ - INFO - 对比参数: ['dataset.data_loading.fault_samples.max_fault_samples_per_class', 'augmentation.num_generated_per_class']
2025-06-26 08:50:11,330 - __main__ - INFO - 🔥 扩散模型重用: 否
2025-06-26 08:50:11,331 - __main__ - INFO - ================================================================================
2025-06-26 08:50:11,331 - __main__ - INFO - 缓存配置文件保存在: cache\20250626_085011
2025-06-26 08:50:11,333 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250626_085011\configs\original_config.yaml
2025-06-26 08:50:11,333 - __main__ - INFO - 🔧 使用传统独立实验模式
2025-06-26 08:50:11,333 - __main__ - INFO - ============================================================
2025-06-26 08:50:11,334 - __main__ - INFO - 开始实验 1/4
2025-06-26 08:50:11,334 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 5}
2025-06-26 08:50:11,334 - __main__ - INFO - ============================================================
2025-06-26 08:50:11,339 - __main__ - INFO - 缓存配置已保存: cache\20250626_085011\experiment_001_config.yaml
2025-06-26 08:50:11,340 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:50:11,340 - __main__ - INFO - ================================================================================
2025-06-26 08:50:11,340 - __main__ - INFO - 当前实验配置
2025-06-26 08:50:11,340 - __main__ - INFO - ================================================================================
2025-06-26 08:50:11,341 - __main__ - INFO - 数据集: KAT
2025-06-26 08:50:11,341 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:50:11,341 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:50:11,341 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:50:11,341 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:50:11,341 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:50:11,342 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:50:11,342 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:50:11,342 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:50:11,342 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:50:11,343 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:50:11,343 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:50:11,343 - __main__ - INFO - 设备: auto
2025-06-26 08:50:11,344 - __main__ - INFO - 性能模式: auto
2025-06-26 08:50:11,344 - __main__ - INFO - 随机种子: 42
2025-06-26 08:50:11,344 - __main__ - INFO - ================================================================================
2025-06-26 08:50:11,344 - __main__ - INFO - ============================================================
2025-06-26 08:50:11,344 - __main__ - INFO - 健康样本配置验证
2025-06-26 08:50:11,344 - __main__ - INFO - ============================================================
2025-06-26 08:50:11,344 - __main__ - INFO - 扩散训练健康样本数量: 2
2025-06-26 08:50:11,345 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:50:11,345 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 08:50:11,345 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 08:50:11,345 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 08:50:11,346 - __main__ - INFO - ============================================================
2025-06-26 08:50:11,350 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:50:11,351 - __main__ - INFO - 加载数据...
2025-06-26 08:50:11,352 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:50:11,352 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:50:11,361 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:50:11,369 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:50:11,369 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:50:11,369 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:50:11,369 - common.data_loader - INFO - 健康样本数量设置为指定值: 2
2025-06-26 08:50:11,370 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:50:11,370 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:50:11,371 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,371 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,371 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,371 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,372 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,372 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,372 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:50:11,372 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:50:11,380 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:50:11,394 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:50:11,394 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:50:11,394 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:50:11,395 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:50:11,395 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:50:11,401 - __main__ - INFO - ==================================================
2025-06-26 08:50:11,401 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:50:11,401 - __main__ - INFO - ==================================================
2025-06-26 08:50:11,416 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:50:11,416 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:50:11,416 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:50:11,417 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:50:11,417 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:50:11,738 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:50:11,739 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:50:11,739 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:50:11,742 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:50:13,650 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:50:13,651 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:50:13,651 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:50:14,952 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837687, Val Loss: 0.867107 (Best✓)
2025-06-26 08:50:14,953 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:50:15,328 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:50:15,328 - __main__ - INFO - ==================================================
2025-06-26 08:50:15,328 - __main__ - INFO - 生成增强样本
2025-06-26 08:50:15,328 - __main__ - INFO - ==================================================
2025-06-26 08:50:15,851 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:50:15,851 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:50:15,851 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:50:15,851 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:50:15,851 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:50:15,852 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:50:16,051 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:50:16,051 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:50:16,052 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:50:16,052 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:50:16,459 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:50:16,460 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 5 个...
2025-06-26 08:50:16,461 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成5个
2025-06-26 08:50:16,461 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 08:50:33,276 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:50:50,131 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:51:06,937 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:51:23,987 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:51:40,848 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:51:57,638 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:52:14,499 - __main__ - INFO - 生成类别 7 的样本...
2025-06-26 08:52:31,270 - __main__ - INFO - 样本生成完成，总共生成 40 个样本
2025-06-26 08:52:31,271 - __main__ - INFO - 生成样本已保存:
2025-06-26 08:52:31,271 - __main__ - INFO -   数据: generated_samples/KAT\CDDPM\generated_data.npy
2025-06-26 08:52:31,271 - __main__ - INFO -   标签: generated_samples/KAT\CDDPM\generated_labels.npy
2025-06-26 08:52:33,853 - common.visualization - INFO - 信号样本图已保存: results\generated_samples.png
2025-06-26 08:52:33,853 - __main__ - INFO - ==================================================
2025-06-26 08:52:33,853 - __main__ - INFO - 训练基线分类器（仅原始数据）
2025-06-26 08:52:33,853 - __main__ - INFO - ==================================================
2025-06-26 08:52:33,887 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:52:33,887 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:52:34,019 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0902 Acc: 0.0000 | Val Loss: 2.0793 Acc: 0.0000 | LR: 9.78e-05 | Time: 00:00
2025-06-26 08:52:34,090 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0835 Acc: 0.0909 | Val Loss: 2.0801 Acc: 0.0000 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:52:34,138 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0681 Acc: 0.4545 | Val Loss: 2.0820 Acc: 0.0000 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:52:34,185 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0709 Acc: 0.1818 | Val Loss: 2.0848 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:52:34,235 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0638 Acc: 0.1818 | Val Loss: 2.0878 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:52:34,285 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0502 Acc: 0.3636 | Val Loss: 2.0907 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:52:34,310 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0499 Acc: 0.2727 | Val Loss: 2.0934 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:52:34,334 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0506 Acc: 0.1818 | Val Loss: 2.0958 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:52:34,359 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0508 Acc: 0.0909 | Val Loss: 2.0978 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:52:34,382 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0432 Acc: 0.1818 | Val Loss: 2.0994 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:52:34,382 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:52:34,382 - __main__ - INFO - 最佳验证准确率: 0.0000
2025-06-26 08:52:34,748 - __main__ - INFO - 基线分类器性能:
2025-06-26 08:52:34,748 - __main__ - INFO -   准确率: 0.1249
2025-06-26 08:52:34,748 - __main__ - INFO -   精确率: 0.0156
2025-06-26 08:52:34,749 - __main__ - INFO -   召回率: 0.1249
2025-06-26 08:52:34,749 - __main__ - INFO -   F1分数: 0.0277
2025-06-26 08:52:34,749 - __main__ - INFO - 基线分类器训练情况:
2025-06-26 08:52:34,749 - __main__ - INFO -   最终训练损失: 2.0432
2025-06-26 08:52:34,749 - __main__ - INFO -   最终验证损失: 2.0994
2025-06-26 08:52:34,749 - __main__ - INFO -   最终训练准确率: 0.1818
2025-06-26 08:52:34,749 - __main__ - INFO -   最终验证准确率: 0.0000
2025-06-26 08:52:34,750 - __main__ - INFO -   实际训练轮数: 10
2025-06-26 08:52:34,750 - __main__ - WARNING - ⚠️  基线分类器性能异常低，可能的原因:
2025-06-26 08:52:34,750 - __main__ - WARNING -    1. 数据集太小或质量差
2025-06-26 08:52:34,750 - __main__ - WARNING -    2. 模型配置不当
2025-06-26 08:52:34,750 - __main__ - WARNING -    3. 训练时间不足
2025-06-26 08:52:34,750 - __main__ - WARNING -    4. 学习率设置问题
2025-06-26 08:52:34,750 - __main__ - INFO - ==================================================
2025-06-26 08:52:34,750 - __main__ - INFO - 训练增强分类器（原始数据 + 生成数据）
2025-06-26 08:52:34,750 - __main__ - INFO - ==================================================
2025-06-26 08:52:34,751 - __main__ - INFO - 数据来源确认:
2025-06-26 08:52:34,751 - __main__ - INFO -   基线训练数据来源: train_subset (11 样本)
2025-06-26 08:52:34,751 - __main__ - INFO -   测试数据来源: test_loader.dataset (1001 样本)
2025-06-26 08:52:34,751 - __main__ - INFO -   验证数据来源: val_subset (5 样本)
2025-06-26 08:52:34,751 - __main__ - INFO - 数据维度检查:
2025-06-26 08:52:34,751 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:52:34,751 - __main__ - INFO -   生成数据形状: (40, 1024)
2025-06-26 08:52:34,752 - __main__ - INFO - 原始训练数据: 11 样本
2025-06-26 08:52:34,752 - __main__ - INFO - 生成数据: 40 样本
2025-06-26 08:52:34,752 - __main__ - INFO - 增强后训练数据: 51 样本
2025-06-26 08:52:34,752 - __main__ - INFO - 数据分布分析:
2025-06-26 08:52:34,752 - __main__ - INFO -   原始数据类别分布: {0: 1, 1: 2, 3: 2, 4: 2, 5: 1, 6: 1, 7: 2}
2025-06-26 08:52:34,752 - __main__ - INFO -   生成数据类别分布: {0: 5, 1: 5, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5, 7: 5}
2025-06-26 08:52:34,752 - __main__ - INFO -   增强数据类别分布: {0: 6, 1: 7, 2: 5, 3: 7, 4: 7, 5: 6, 6: 6, 7: 7}
2025-06-26 08:52:34,752 - __main__ - INFO - 健康样本处理逻辑:
2025-06-26 08:52:34,752 - __main__ - INFO -   只生成故障样本: False
2025-06-26 08:52:34,752 - __main__ - INFO -   使用真实健康样本: True
2025-06-26 08:52:34,752 - __main__ - INFO -   真实健康样本数量配置: -1
2025-06-26 08:52:34,752 - __main__ - INFO -   检测到生成的健康样本，使用生成的健康样本
2025-06-26 08:52:34,753 - __main__ - INFO - 数据质量检查:
2025-06-26 08:52:34,753 - __main__ - INFO -   原始数据形状: (11, 1024)
2025-06-26 08:52:34,754 - __main__ - INFO -   生成数据形状: (40, 1024)
2025-06-26 08:52:34,754 - __main__ - INFO -   原始数据范围: [0.0000, 1.0000]
2025-06-26 08:52:34,754 - __main__ - INFO -   生成数据范围: [-951.4166, 849.5403]
2025-06-26 08:52:34,780 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-26 08:52:34,780 - __main__ - INFO - 开始训练分类器...
2025-06-26 08:52:35,055 - __main__ - INFO - Epoch   1/10 | Train Loss: 2.0925 Acc: 0.0857 | Val Loss: 2.0945 Acc: 0.0625 | LR: 9.78e-05 | Time: 00:00 | Save: Best✓
2025-06-26 08:52:35,111 - __main__ - INFO - Epoch   2/10 | Train Loss: 2.0674 Acc: 0.1714 | Val Loss: 2.1018 Acc: 0.0000 | LR: 9.14e-05 | Time: 00:00
2025-06-26 08:52:35,166 - __main__ - INFO - Epoch   3/10 | Train Loss: 2.0728 Acc: 0.2000 | Val Loss: 2.1128 Acc: 0.0000 | LR: 8.15e-05 | Time: 00:00
2025-06-26 08:52:35,221 - __main__ - INFO - Epoch   4/10 | Train Loss: 2.0481 Acc: 0.2000 | Val Loss: 2.1233 Acc: 0.0000 | LR: 6.89e-05 | Time: 00:00
2025-06-26 08:52:35,276 - __main__ - INFO - Epoch   5/10 | Train Loss: 2.0498 Acc: 0.2000 | Val Loss: 2.1360 Acc: 0.0000 | LR: 5.50e-05 | Time: 00:00
2025-06-26 08:52:35,330 - __main__ - INFO - Epoch   6/10 | Train Loss: 2.0327 Acc: 0.2000 | Val Loss: 2.1453 Acc: 0.0000 | LR: 4.11e-05 | Time: 00:00
2025-06-26 08:52:35,383 - __main__ - INFO - Epoch   7/10 | Train Loss: 2.0442 Acc: 0.2000 | Val Loss: 2.1548 Acc: 0.0000 | LR: 2.85e-05 | Time: 00:00
2025-06-26 08:52:35,437 - __main__ - INFO - Epoch   8/10 | Train Loss: 2.0539 Acc: 0.2000 | Val Loss: 2.1598 Acc: 0.0000 | LR: 1.86e-05 | Time: 00:00
2025-06-26 08:52:35,492 - __main__ - INFO - Epoch   9/10 | Train Loss: 2.0339 Acc: 0.2000 | Val Loss: 2.1640 Acc: 0.0000 | LR: 1.22e-05 | Time: 00:00
2025-06-26 08:52:35,546 - __main__ - INFO - Epoch  10/10 | Train Loss: 2.0501 Acc: 0.2286 | Val Loss: 2.1657 Acc: 0.0000 | LR: 1.00e-05 | Time: 00:00
2025-06-26 08:52:35,546 - __main__ - INFO - 分类器训练完成，用时: 00:00
2025-06-26 08:52:35,546 - __main__ - INFO - 最佳验证准确率: 0.0625
2025-06-26 08:52:35,881 - common.visualization - INFO - 训练曲线已保存: results\classifier_training_curves.png
2025-06-26 08:52:35,881 - __main__ - INFO - ==================================================
2025-06-26 08:52:35,881 - __main__ - INFO - 评估分类器性能
2025-06-26 08:52:35,881 - __main__ - INFO - ==================================================
2025-06-26 08:52:36,222 - __main__ - INFO - 分类性能指标:
2025-06-26 08:52:36,222 - __main__ - INFO -   准确率 (Accuracy): 0.1249
2025-06-26 08:52:36,222 - __main__ - INFO -   精确率 (Precision): 0.0156
2025-06-26 08:52:36,222 - __main__ - INFO -   召回率 (Recall): 0.1249
2025-06-26 08:52:36,222 - __main__ - INFO -   F1分数 (F1-Score): 0.0277
2025-06-26 08:52:36,633 - common.visualization - INFO - 混淆矩阵已保存: results\confusion_matrix_augmented.png
2025-06-26 08:52:36,633 - __main__ - INFO - ==================================================
2025-06-26 08:52:36,633 - __main__ - INFO - 性能对比总结
2025-06-26 08:52:36,633 - __main__ - INFO - ==================================================
2025-06-26 08:52:36,633 - __main__ - INFO - 性能对比 (增强 vs 基线):
2025-06-26 08:52:36,633 - __main__ - INFO -   准确率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:52:36,634 - __main__ - INFO -   精确率: 0.0156 → 0.0156 (Δ+0.0000)
2025-06-26 08:52:36,634 - __main__ - INFO -   召回率: 0.1249 → 0.1249 (Δ+0.0000)
2025-06-26 08:52:36,634 - __main__ - INFO -   F1分数: 0.0277 → 0.0277 (Δ+0.0000)
2025-06-26 08:52:36,634 - __main__ - INFO - 🏁 实验结束
2025-06-26 08:52:36,634 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,634 - __main__ - INFO - 当前实验配置
2025-06-26 08:52:36,634 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,634 - __main__ - INFO - 数据集: KAT
2025-06-26 08:52:36,634 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:52:36,635 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:52:36,635 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:52:36,635 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:52:36,635 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:52:36,635 - __main__ - INFO - 每类生成样本数: 5
2025-06-26 08:52:36,635 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:52:36,635 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:52:36,635 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:52:36,635 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:52:36,635 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:52:36,635 - __main__ - INFO - 设备: auto
2025-06-26 08:52:36,635 - __main__ - INFO - 性能模式: auto
2025-06-26 08:52:36,636 - __main__ - INFO - 随机种子: 42
2025-06-26 08:52:36,636 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,697 - common.results_manager - INFO - 单次实验结果已保存: results\KAT\20250626_085011\individual_experiments\experiment_001
2025-06-26 08:52:36,697 - __main__ - INFO - 实验 1 完成
2025-06-26 08:52:36,697 - __main__ - INFO - ============================================================
2025-06-26 08:52:36,697 - __main__ - INFO - 开始实验 2/4
2025-06-26 08:52:36,697 - __main__ - INFO - 实验参数: {'dataset.data_loading.fault_samples.max_fault_samples_per_class': 2, 'augmentation.num_generated_per_class': 8}
2025-06-26 08:52:36,697 - __main__ - INFO - ============================================================
2025-06-26 08:52:36,702 - __main__ - INFO - 缓存配置已保存: cache\20250626_085011\experiment_002_config.yaml
2025-06-26 08:52:36,702 - __main__ - INFO - 🚀 实验开始
2025-06-26 08:52:36,702 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,703 - __main__ - INFO - 当前实验配置
2025-06-26 08:52:36,703 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,703 - __main__ - INFO - 数据集: KAT
2025-06-26 08:52:36,703 - __main__ - INFO - 故障样本每类: 2
2025-06-26 08:52:36,703 - __main__ - INFO - 健康样本总数: 2
2025-06-26 08:52:36,703 - __main__ - INFO - 信号长度: 1024
2025-06-26 08:52:36,704 - __main__ - INFO - 归一化方法: minmax
2025-06-26 08:52:36,704 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 08:52:36,704 - __main__ - INFO - 每类生成样本数: 8
2025-06-26 08:52:36,704 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:52:36,704 - __main__ - INFO - 扩散模型训练轮数: 1
2025-06-26 08:52:36,704 - __main__ - INFO - 分类器训练轮数: 10
2025-06-26 08:52:36,705 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 08:52:36,705 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 08:52:36,705 - __main__ - INFO - 设备: auto
2025-06-26 08:52:36,705 - __main__ - INFO - 性能模式: auto
2025-06-26 08:52:36,705 - __main__ - INFO - 随机种子: 42
2025-06-26 08:52:36,705 - __main__ - INFO - ================================================================================
2025-06-26 08:52:36,705 - __main__ - INFO - ============================================================
2025-06-26 08:52:36,705 - __main__ - INFO - 健康样本配置验证
2025-06-26 08:52:36,705 - __main__ - INFO - ============================================================
2025-06-26 08:52:36,705 - __main__ - INFO - 扩散训练健康样本数量: 2
2025-06-26 08:52:36,705 - __main__ - INFO - 只生成故障样本: False
2025-06-26 08:52:36,705 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 08:52:36,705 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 08:52:36,705 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 08:52:36,705 - __main__ - INFO - ============================================================
2025-06-26 08:52:36,707 - __main__ - INFO - 使用设备: cuda
2025-06-26 08:52:36,708 - __main__ - INFO - 加载数据...
2025-06-26 08:52:36,708 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 08:52:36,708 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 08:52:36,710 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:52:36,712 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 08:52:36,712 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:52:36,713 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 08:52:36,713 - common.data_loader - INFO - 健康样本数量设置为指定值: 2
2025-06-26 08:52:36,713 - common.data_loader - INFO - 样本配置: 故障样本每类最多2个, 健康样本最多2个
2025-06-26 08:52:36,713 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 08:52:36,713 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,713 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 2 个
2025-06-26 08:52:36,714 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 2 个
2025-06-26 08:52:36,717 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 08:52:36,719 - common.data_loader - INFO - 数据加载完成:
2025-06-26 08:52:36,719 - common.data_loader - INFO -   训练样本: 16
2025-06-26 08:52:36,719 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 08:52:36,719 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 08:52:36,719 - common.data_loader - INFO -   类别数: 8
2025-06-26 08:52:36,720 - __main__ - INFO - ==================================================
2025-06-26 08:52:36,720 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 08:52:36,720 - __main__ - INFO - ==================================================
2025-06-26 08:52:36,721 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 08:52:36,721 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:52:36,721 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:52:36,721 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:52:36,721 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:52:36,934 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:52:36,934 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:52:36,934 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:52:36,934 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:52:36,961 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-26 08:52:36,961 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-26 08:52:36,961 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共1轮
2025-06-26 08:52:37,137 - models.augmentation_factory - INFO - Epoch   1/1: Train Loss: 0.837687, Val Loss: 0.867107 (Best✓)
2025-06-26 08:52:37,138 - models.augmentation_factory - INFO - CDDPM训练完成
2025-06-26 08:52:37,434 - common.visualization - INFO - 训练曲线已保存: results\cddpm_training_curves.png
2025-06-26 08:52:37,434 - __main__ - INFO - ==================================================
2025-06-26 08:52:37,435 - __main__ - INFO - 生成增强样本
2025-06-26 08:52:37,435 - __main__ - INFO - ==================================================
2025-06-26 08:52:37,948 - models.augmentation_factory - INFO - CDDPM模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:52:37,949 - models.augmentation_factory - INFO - CDDPM 模型已保存: checkpoints\augmentation\cddpm\best_model.pth
2025-06-26 08:52:37,949 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 08:52:37,949 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 08:52:37,949 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 08:52:37,949 - models.cddpm - INFO -   类别数量: 8
2025-06-26 08:52:38,127 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-26 08:52:38,127 - models.cddpm - INFO -   时间步数: 1000
2025-06-26 08:52:38,127 - models.cddpm - INFO -   噪声调度: linear
2025-06-26 08:52:38,128 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-26 08:52:38,372 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-26 08:52:38,373 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 8 个...
2025-06-26 08:52:38,373 - __main__ - INFO - 生成故障样本+健康样本，健康样本标签=0，每类生成8个
2025-06-26 08:52:38,374 - __main__ - INFO - 生成类别 0 的样本...
2025-06-26 08:53:01,901 - __main__ - INFO - 生成类别 1 的样本...
2025-06-26 08:53:25,361 - __main__ - INFO - 生成类别 2 的样本...
2025-06-26 08:53:48,830 - __main__ - INFO - 生成类别 3 的样本...
2025-06-26 08:54:12,304 - __main__ - INFO - 生成类别 4 的样本...
2025-06-26 08:54:36,141 - __main__ - INFO - 生成类别 5 的样本...
2025-06-26 08:55:00,214 - __main__ - INFO - 生成类别 6 的样本...
2025-06-26 08:55:23,818 - __main__ - INFO - 生成类别 7 的样本...
