# 健康样本处理功能测试配置文件
# 测试新的健康样本处理逻辑

# 数据集配置
dataset:
  name: "KAT"  # 单一数据集
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载参数
  data_loading:
    # 公共参数
    data_type: "sequential"      # 数据类型: "random" 或 "sequential"
    sample_selection: "sequential"  # 样本选择方式: "random" 或 "sequential"
    original_length: 1024        # 原始信号长度
    signal_length: 1024          # 截取后的信号长度（论文中使用1024）
    train_val_split: 0.7         # 训练集在训练数据中的比例（剩余为验证集）
    normalize: true              # 是否归一化
    normalization_method: "minmax"  # 归一化方法: minmax, zscore, robust

    # 样本数量配置（分开配置）
    fault_samples:
      max_fault_samples_per_class: [2, 3]          # 故障样本：多值对比实验

    healthy_samples:                  # 健康样本处理配置
      max_healthy_samples: -1         # 健康样本数量控制：
                                      #   -1: 与故障样本数量保持一致（动态匹配）
                                      #   0: 扩散训练中不使用健康样本
                                      #   >0: 使用指定数量的健康样本
      healthy_label: 0                # 健康样本的标签值（通常为0）

# 数据增强配置
augmentation:
  method: "CDDPM"    # 单一方法
  num_generated_per_class: [5, 8]    # 多值对比实验
  save_generated: true           # 是否保存生成的样本
  generate_fault_only: false    # 生成样本控制：
                                 #   true: 只生成故障样本，不生成健康样本
                                 #   false: 生成故障样本+健康样本（数量与num_generated_per_class一致）
  
  # 分类器训练的健康样本配置
  classifier_healthy_samples:
    use_real_when_no_generated: true    # 当没有生成健康样本时，是否使用真实健康样本
    real_healthy_count: -1              # 使用真实健康样本的数量：
                                        #   -1: 与故障样本数量保持一致
                                        #   0: 不使用健康样本（不推荐）
                                        #   >0: 使用指定数量

  # CDDPM参数
  cddpm:
    timesteps: 1000             # 扩散步数
    beta_schedule: "linear"     # 噪声调度: linear, cosine
    beta_start: 0.0001         # 起始beta值
    beta_end: 0.02             # 结束beta值

    # Classifier-Free Guidance 参数（论文中的条件/无条件混合训练）
    unconditional_prob: 0.1    # 无条件训练概率（10%的样本进行无条件训练）
    guidance_scale: 1.0        # 引导强度（1.0=标准条件生成，>1.0=增强条件控制）

# 模型配置
models:
  # 扩散模型配置（用于CDDPM）
  diffusion:
    in_channels: 1              # 输入通道数（1D信号）
    out_channels: 1             # 输出通道数
    model_channels: 64          # 基础通道数
    num_res_blocks: 2           # 残差块数量
    attention_resolutions: [16, 8]  # 注意力分辨率
    channel_mult: [1, 2, 4]     # 通道倍数
    dropout: 0.1                # Dropout率
    use_scale_shift_norm: true  # 使用缩放偏移归一化

  # U-Net配置（用于CDDPM）
  unet:
    in_channels: 1              # 输入通道数（1D信号）
    out_channels: 1             # 输出通道数
    model_channels: 64          # 基础通道数
    num_res_blocks: 2           # 残差块数量
    attention_resolutions: [16, 8]  # 注意力分辨率
    channel_mult: [1, 2, 4]     # 通道倍数
    dropout: 0.1                # Dropout率
    use_scale_shift_norm: true  # 使用缩放偏移归一化

  # 多尺度残差CNN配置
  mrcnn:
    input_channels: 1           # 输入通道数
    num_classes: 8              # 类别数（根据数据集自动调整）
    base_channels: 64           # 基础通道数
    num_blocks: 4               # 残差块数量
    dropout: 0.1                # Dropout率
    use_attention: true         # 使用注意力机制

# 训练配置
training:
  # 扩散模型训练参数
  diffusion:
    epochs: 1                   # 快速测试用1轮
    batch_size: 8               # 批次大小
    learning_rate: 0.0001       # 学习率
    weight_decay: 0.01          # 权重衰减
    
    # 学习率调度器
    scheduler:
      type: "cosine"            # 调度器类型: cosine, step, exponential
      T_max: 1                  # 余弦退火周期
      eta_min: 0.00001          # 最小学习率

    # 早停配置
    early_stopping:
      enabled: false            # 快速测试关闭早停
      patience: 50              # 耐心值
      min_delta: 0.001          # 最小改进
      monitor: "val_loss"       # 监控指标
      mode: "min"               # 模式
      restore_best_weights: true # 恢复最佳权重

  # 分类器训练参数
  classifier:
    epochs: 10                  # 快速测试用10轮
    batch_size: 16              # 批次大小
    learning_rate: 0.0001       # 学习率
    weight_decay: 0.01          # 权重衰减
    
    # 学习率调度器
    scheduler:
      type: "cosine"            # 调度器类型
      T_max: 10                 # 余弦退火周期
      eta_min: 0.00001          # 最小学习率

    # 早停配置
    early_stopping:
      enabled: false            # 快速测试关闭早停
      patience: 20              # 耐心值
      min_delta: 0.001          # 最小改进
      monitor: "train_loss"     # 监控指标
      mode: "min"               # 模式
      restore_best_weights: true # 恢复最佳权重

# 评估配置
evaluation:
  metrics:
    classification: true        # 计算分类指标
    generation: false           # 跳过生成指标（加快测试）

# 系统配置
system:
  device: "auto"                # 设备选择: auto, cpu, cuda
  performance_mode: "auto"      # 性能模式: auto, fast, balanced, quality
  seed: 42                      # 随机种子
  num_workers: 0                # 数据加载器工作进程数（Windows设为0）
  pin_memory: false             # 是否固定内存

  # 保存配置
  save:
    results_dir: "results"      # 结果保存目录
    checkpoints_dir: "checkpoints"  # 检查点保存目录
    logs_dir: "logs"            # 日志保存目录
    generated_samples_dir: "generated_samples/{dataset_name}"  # 生成样本保存目录
    save_every_n_epochs: 1000   # 过程权重保存间隔（轮数）
    save_best_only: true        # 是否只保存最佳模型
    max_checkpoints_to_keep: 1  # 最多保留多少个过程检查点

# 实验配置
experiment:
  name: "test_healthy_sample_handling"  # 实验名称
  description: "测试新的健康样本处理逻辑"
  tags: ["CDDPM", "healthy_samples", "fault_diagnosis"]

  # 结果保存配置
  results:
    save_individual: true       # 保存单次实验结果
    save_comparison_csv: true   # 保存对比实验汇总CSV
    save_plots_csv: true        # 保存绘图数据的CSV文件
    create_timestamp_folder: true  # 创建时间戳文件夹

# 性能模式配置
performance_profiles:
  # 标准模式 - 适用于大多数GPU
  standard:
    training:
      diffusion:
        batch_size: 8          # 小批次，适合测试
      classifier:
        batch_size: 16         # 小批次，适合测试
    system:
      num_workers: 0           # Windows设为0
      pin_memory: false        # 不启用内存固定
      optimization:
        use_amp: false         # 不使用混合精度
        compile_model: false   # 不使用模型编译
        channels_last: false   # 不使用channels_last
        benchmark: true        # 启用cuDNN benchmark
