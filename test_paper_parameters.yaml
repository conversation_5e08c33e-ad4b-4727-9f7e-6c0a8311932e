# 论文参数验证配置
# 基于论文Table V和实验设置的参数配置测试

# ================================================================================
# 1. 数据集配置
# ================================================================================
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载配置
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    fault_samples:
      max_fault_samples_per_class: [5]  # 小数据集快速测试

    healthy_samples:
      max_healthy_samples: -1           # 自动匹配故障样本数量
      healthy_label: 0

# ================================================================================
# 2. 数据增强配置
# ================================================================================
augmentation:
  method: "CDDPM"
  num_generated_per_class: [8]          # 生成8个样本
  save_generated: true
  generate_fault_only: true

  classifier_healthy_samples:
    use_real_when_no_generated: true
    real_healthy_count: -1

  generation_strategy:
    target_samples_per_class: -1       # 自动匹配故障样本数量
    initial_multiplier: 3.0
    min_multiplier: 2.0
    max_multiplier: 5.0
    adaptive_generation: true

  # CDDPM参数（论文标准设置）
  cddpm:
    timesteps: 1000                     # 扩散步数
    beta_schedule: "linear"             # 噪声调度
    beta_start: 0.0001                  # 起始beta值
    beta_end: 0.02                      # 结束beta值
    unconditional_prob: 0.1             # 无条件训练概率
    guidance_scale: 1.0                 # 引导强度

# ================================================================================
# 3. 模型配置
# ================================================================================
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# ================================================================================
# 4. 训练配置 - 基于论文参数设置
# ================================================================================
training:
  # 扩散模型训练参数（基于论文Table V）
  diffusion:
    epochs: 50                        # 快速测试，使用较少轮数
    batch_size: 64                    # 论文中统一使用64
    learning_rate: 0.00001            # 论文中为0.00001（1e-5）
    weight_decay: 0.0001              # 权重衰减

    # 最佳模型判断配置
    best_model_criteria:
      metric: "weighted_loss"         # 使用权重损失
      mode: "min"
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

    # 学习率调度器
    scheduler:
      type: "cosine"
      T_max: 50                       # 与epochs保持一致
      eta_min: 0.000001               # 比学习率小一个数量级

    # 早停配置
    early_stopping:
      enabled: true
      patience: 15                    # 适中的耐心值
      min_delta: 0.001
      monitor: "weighted_loss"
      mode: "min"
      restore_best_weights: true
      weighted_loss:
        train_weight: 0.7
        val_weight: 0.3

  # 分类器训练参数（基于论文实验设置）
  classifier:
    epochs: 25                        # 快速测试，使用较少轮数
    batch_size: 64                    # 论文中统一使用64
    learning_rate: 0.0001             # 论文中分类器使用1e-4
    weight_decay: 0.01                # 权重衰减

    # 最佳模型判断配置
    best_model_criteria:
      metric: "val_loss"              # 分类器使用验证损失
      mode: "min"

    # 学习率调度器
    scheduler:
      type: "cosine"
      T_max: 25                       # 与epochs保持一致
      eta_min: 0.00001

    # 早停配置
    early_stopping:
      enabled: true
      patience: 8                     # 分类器较小耐心值
      min_delta: 0.001
      monitor: "val_loss"             # 监控验证损失
      mode: "min"
      restore_best_weights: true

# ================================================================================
# 5. 数据筛选配置
# ================================================================================
data_screening:
  enabled: false                      # 快速测试关闭筛选

# ================================================================================
# 6. 评估配置
# ================================================================================
evaluation:
  metrics:
    classification: ["accuracy", "precision", "recall", "f1_score"]
    generation: ["gan_train", "gan_test"]
  
  gan_evaluation:
    classifier_epochs: 100            # GAN评估轮数
    batch_size: 64
    
  visualization:
    save_confusion_matrix: true
    save_tsne: true
    tsne_perplexity: 30
    tsne_n_iter: 1000
    save_sample_plots: true
    num_samples_to_plot: 10

# ================================================================================
# 7. 系统配置
# ================================================================================
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  optimization:
    use_amp: false
    compile_model: false
    channels_last: false
    benchmark: true

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_process_checkpoints: true
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 3

# ================================================================================
# 8. 实验配置
# ================================================================================
experiment:
  name: "test_paper_parameters"
  description: "验证基于论文参数设置的训练配置"
  tags: ["paper_parameters", "CDDPM", "validation"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# ================================================================================
# 9. 性能模式配置
# ================================================================================
performance_profiles:
  # 快速测试模式
  fast:
    training:
      diffusion:
        batch_size: 32
        epochs: 20
      classifier:
        batch_size: 32
        epochs: 10
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  # 标准模式（论文参数）
  standard:
    training:
      diffusion:
        batch_size: 64                # 论文参数
        epochs: 300                   # 论文参数
      classifier:
        batch_size: 64                # 论文参数
        epochs: 50                    # 论文参数
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true

  # 高性能模式
  high_performance:
    training:
      diffusion:
        batch_size: 128
        epochs: 300
      classifier:
        batch_size: 128
        epochs: 50
    system:
      num_workers: 0
      pin_memory: true
      optimization:
        use_amp: true
        compile_model: true
        channels_last: true
        benchmark: true
