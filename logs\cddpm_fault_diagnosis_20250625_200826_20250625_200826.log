2025-06-25 20:08:26,507 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250625_200826_20250625_200826.log
2025-06-25 20:08:26,507 - __main__ - INFO - ================================================================================
2025-06-25 20:08:26,508 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-25 20:08:26,509 - __main__ - INFO - 数据集: KAT
2025-06-25 20:08:26,509 - __main__ - INFO - ================================================================================
2025-06-25 20:08:26,510 - __main__ - INFO - 🚀 实验开始
2025-06-25 20:08:26,510 - __main__ - INFO - ================================================================================
2025-06-25 20:08:26,511 - __main__ - INFO - 当前实验配置
2025-06-25 20:08:26,511 - __main__ - INFO - ================================================================================
2025-06-25 20:08:26,512 - __main__ - INFO - 数据集: KAT
2025-06-25 20:08:26,513 - __main__ - INFO - 故障样本每类: [3]
2025-06-25 20:08:26,513 - __main__ - INFO - 健康样本总数: 1
2025-06-25 20:08:26,514 - __main__ - INFO - 信号长度: 1024
2025-06-25 20:08:26,514 - __main__ - INFO - 归一化方法: minmax
2025-06-25 20:08:26,515 - __main__ - INFO - 增强方法: CDDPM
2025-06-25 20:08:26,515 - __main__ - INFO - 每类生成样本数: [10]
2025-06-25 20:08:26,516 - __main__ - INFO - 只生成故障样本: True
2025-06-25 20:08:26,516 - __main__ - INFO - 扩散模型训练轮数: 20
2025-06-25 20:08:26,517 - __main__ - INFO - 分类器训练轮数: 200
2025-06-25 20:08:26,517 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-25 20:08:26,518 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-25 20:08:26,518 - __main__ - INFO - 设备: cuda
2025-06-25 20:08:26,519 - __main__ - INFO - 性能模式: auto
2025-06-25 20:08:26,519 - __main__ - INFO - 随机种子: 42
2025-06-25 20:08:26,520 - __main__ - INFO - ================================================================================
2025-06-25 20:08:26,521 - __main__ - INFO - 使用设备: cuda
2025-06-25 20:08:26,570 - __main__ - INFO - 加载数据...
2025-06-25 20:08:26,570 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 20:08:26,571 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 20:08:26,642 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 20:08:26,712 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 20:08:26,713 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 20:08:26,714 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 20:08:26,715 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多1个
2025-06-25 20:08:26,715 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-25 20:08:26,716 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 1 个
2025-06-25 20:08:26,717 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,717 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,718 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,719 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,719 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,720 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-25 20:08:26,721 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-25 20:08:26,726 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 20:08:26,738 - common.data_loader - INFO - 数据加载完成:
2025-06-25 20:08:26,739 - common.data_loader - INFO -   训练样本: 22
2025-06-25 20:08:26,740 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 20:08:26,741 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 20:08:26,742 - common.data_loader - INFO -   类别数: 8
2025-06-25 20:08:26,748 - __main__ - INFO - ==================================================
2025-06-25 20:08:26,748 - __main__ - INFO - 开始训练数据增强模型
2025-06-25 20:08:26,749 - __main__ - INFO - ==================================================
2025-06-25 20:08:26,769 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-25 20:08:26,770 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 20:08:26,771 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 20:08:26,772 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 20:08:26,772 - models.cddpm - INFO -   类别数量: 8
2025-06-25 20:08:27,028 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 20:08:27,028 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 20:08:27,029 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 20:08:27,033 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 20:08:27,268 - __main__ - INFO - 数据增强方法已初始化: CDDPM
2025-06-25 20:08:27,269 - models.augmentation_factory - INFO - 开始训练 CDDPM 模型...
2025-06-25 20:08:27,270 - models.augmentation_factory - INFO - 开始训练CDDPM模型，共20轮
2025-06-25 20:08:34,790 - models.augmentation_factory - INFO - Epoch   1/20: Train Loss: 0.837560, Val Loss: 0.866048, Weighted Loss: 0.846106 (Best✓)
2025-06-25 20:08:35,039 - models.augmentation_factory - INFO - Epoch   2/20: Train Loss: 0.856659, Val Loss: 0.808877, Weighted Loss: 0.842325 (Best✓)
2025-06-25 20:08:35,304 - models.augmentation_factory - INFO - Epoch   3/20: Train Loss: 0.819554, Val Loss: 0.810295, Weighted Loss: 0.816776 (Best✓)
2025-06-25 20:08:35,855 - models.augmentation_factory - INFO - Epoch   5/20: Train Loss: 0.817410, Val Loss: 0.793510, Weighted Loss: 0.810240 (Best✓)
2025-06-25 20:08:36,405 - models.augmentation_factory - INFO - Epoch   7/20: Train Loss: 0.810916, Val Loss: 0.794410, Weighted Loss: 0.805964 (Best✓)
2025-06-25 20:08:36,999 - models.augmentation_factory - INFO - Epoch   9/20: Train Loss: 0.798863, Val Loss: 0.806947, Weighted Loss: 0.801288 (Best✓)
2025-06-25 20:08:37,500 - models.augmentation_factory - INFO - Epoch  11/20: Train Loss: 0.805723, Val Loss: 0.788065, Weighted Loss: 0.800425 (Best✓)
