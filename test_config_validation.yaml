# 配置验证测试文件 - 测试不一致配置的错误处理
# 这个配置故意设置为不一致，用于测试验证功能

# 数据集配置
dataset:
  name: "KAT"
  datasets:
    KAT:
      num_classes: 8
      class_names: ["K001", "KA01", "KA05", "KA09", "KI01", "KI03", "KI05", "KI08"]

  # 数据加载参数
  data_loading:
    data_type: "sequential"
    sample_selection: "sequential"
    original_length: 1024
    signal_length: 1024
    train_val_split: 0.7
    normalize: true
    normalization_method: "minmax"

    fault_samples:
      max_fault_samples_per_class: [2]

    healthy_samples:
      max_healthy_samples: 0          # 扩散训练中不使用健康样本
      healthy_label: 0

# 数据增强配置
augmentation:
  method: "CDDPM"
  num_generated_per_class: [5]
  save_generated: true
  generate_fault_only: false         # 错误配置：扩散训练不包含健康样本，但生成时要生成健康样本
  
  classifier_healthy_samples:
    use_real_when_no_generated: false  # 错误配置：只生成故障样本，但不使用真实健康样本
    real_healthy_count: 0

  cddpm:
    timesteps: 1000
    beta_schedule: "linear"
    beta_start: 0.0001
    beta_end: 0.02
    unconditional_prob: 0.1
    guidance_scale: 1.0

# 模型配置
models:
  diffusion:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  unet:
    in_channels: 1
    out_channels: 1
    model_channels: 64
    num_res_blocks: 2
    attention_resolutions: [16, 8]
    channel_mult: [1, 2, 4]
    dropout: 0.1
    use_scale_shift_norm: true

  mrcnn:
    input_channels: 1
    num_classes: 8
    base_channels: 64
    num_blocks: 4
    dropout: 0.1
    use_attention: true

# 训练配置
training:
  diffusion:
    epochs: 1
    batch_size: 8
    learning_rate: 0.0001
    weight_decay: 0.01
    scheduler:
      type: "cosine"
      T_max: 1
      eta_min: 0.00001
    early_stopping:
      enabled: false
      patience: 50
      min_delta: 0.001
      monitor: "val_loss"
      mode: "min"
      restore_best_weights: true

  classifier:
    epochs: 10
    batch_size: 16
    learning_rate: 0.0001
    weight_decay: 0.01
    scheduler:
      type: "cosine"
      T_max: 10
      eta_min: 0.00001
    early_stopping:
      enabled: false
      patience: 20
      min_delta: 0.001
      monitor: "train_loss"
      mode: "min"
      restore_best_weights: true

# 评估配置
evaluation:
  metrics:
    classification: true
    generation: false

# 系统配置
system:
  device: "auto"
  performance_mode: "auto"
  seed: 42
  num_workers: 0
  pin_memory: false

  save:
    results_dir: "results"
    checkpoints_dir: "checkpoints"
    logs_dir: "logs"
    generated_samples_dir: "generated_samples/{dataset_name}"
    save_every_n_epochs: 1000
    save_best_only: true
    max_checkpoints_to_keep: 1

# 实验配置
experiment:
  name: "test_config_validation"
  description: "测试配置验证功能"
  tags: ["config_validation", "error_handling"]

  results:
    save_individual: true
    save_comparison_csv: true
    save_plots_csv: true
    create_timestamp_folder: true

# 性能模式配置
performance_profiles:
  standard:
    training:
      diffusion:
        batch_size: 8
      classifier:
        batch_size: 16
    system:
      num_workers: 0
      pin_memory: false
      optimization:
        use_amp: false
        compile_model: false
        channels_last: false
        benchmark: true
