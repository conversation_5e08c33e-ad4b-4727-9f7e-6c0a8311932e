"""
条件去噪扩散概率模型 (Conditional Denoising Diffusion Probabilistic Model, CDDPM)
用于一维振动信号的数据增强
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple, Dict
import logging
from .unet import UNet1D

logger = logging.getLogger(__name__)


def cosine_beta_schedule(timesteps: int, s: float = 0.008) -> torch.Tensor:
    """
    余弦噪声调度
    
    Args:
        timesteps: 时间步数
        s: 偏移参数
        
    Returns:
        beta值序列
    """
    steps = timesteps + 1
    x = torch.linspace(0, timesteps, steps)
    alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * torch.pi * 0.5) ** 2
    alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
    betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
    return torch.clip(betas, 0.0001, 0.9999)


def linear_beta_schedule(timesteps: int, beta_start: float = 0.0001, beta_end: float = 0.02) -> torch.Tensor:
    """
    线性噪声调度
    
    Args:
        timesteps: 时间步数
        beta_start: 起始beta值
        beta_end: 结束beta值
        
    Returns:
        beta值序列
    """
    return torch.linspace(beta_start, beta_end, timesteps)


class CDDPM(nn.Module):
    """条件去噪扩散概率模型"""
    
    def __init__(self, config: Dict):
        super().__init__()
        
        self.config = config
        cddpm_config = config['augmentation']['cddpm']
        
        # 扩散参数
        self.timesteps = cddpm_config['timesteps']
        self.beta_schedule = cddpm_config['beta_schedule']
        
        # 初始化噪声调度
        if self.beta_schedule == "linear":
            betas = linear_beta_schedule(
                self.timesteps, 
                cddpm_config['beta_start'], 
                cddpm_config['beta_end']
            )
        elif self.beta_schedule == "cosine":
            betas = cosine_beta_schedule(self.timesteps)
        else:
            raise ValueError(f"不支持的噪声调度: {self.beta_schedule}")
        
        # 预计算扩散过程中的常数
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        alphas_cumprod_prev = F.pad(alphas_cumprod[:-1], (1, 0), value=1.0)
        
        # 注册为buffer，这样它们会随模型一起移动到GPU
        self.register_buffer('betas', betas)
        self.register_buffer('alphas', alphas)
        self.register_buffer('alphas_cumprod', alphas_cumprod)
        self.register_buffer('alphas_cumprod_prev', alphas_cumprod_prev)
        
        # 计算采样过程中需要的常数
        self.register_buffer('sqrt_alphas_cumprod', torch.sqrt(alphas_cumprod))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', torch.sqrt(1.0 - alphas_cumprod))
        self.register_buffer('log_one_minus_alphas_cumprod', torch.log(1.0 - alphas_cumprod))
        self.register_buffer('sqrt_recip_alphas_cumprod', torch.sqrt(1.0 / alphas_cumprod))
        self.register_buffer('sqrt_recipm1_alphas_cumprod', torch.sqrt(1.0 / alphas_cumprod - 1))
        
        # 计算后验分布的方差
        posterior_variance = betas * (1.0 - alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        self.register_buffer('posterior_variance', posterior_variance)
        self.register_buffer('posterior_log_variance_clipped', 
                           torch.log(torch.clamp(posterior_variance, min=1e-20)))
        self.register_buffer('posterior_mean_coef1', 
                           betas * torch.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod))
        self.register_buffer('posterior_mean_coef2', 
                           (1.0 - alphas_cumprod_prev) * torch.sqrt(alphas) / (1.0 - alphas_cumprod))
        
        # 去噪网络
        self.denoise_model = UNet1D(config)
        
        logger.info(f"CDDPM初始化完成:")
        logger.info(f"  时间步数: {self.timesteps}")
        logger.info(f"  噪声调度: {self.beta_schedule}")
        logger.info(f"  beta范围: [{betas.min():.6f}, {betas.max():.6f}]")
    
    def q_sample(self, x_start: torch.Tensor, t: torch.Tensor, 
                 noise: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向扩散过程：q(x_t | x_0)
        
        Args:
            x_start: 原始信号
            t: 时间步
            noise: 噪声（如果不提供则随机生成）
            
        Returns:
            加噪后的信号
        """
        if noise is None:
            noise = torch.randn_like(x_start)
        
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].reshape(-1, 1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].reshape(-1, 1, 1)
        
        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    
    def q_posterior_mean_variance(self, x_start: torch.Tensor, x_t: torch.Tensor, 
                                 t: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        计算后验分布 q(x_{t-1} | x_t, x_0) 的均值和方差
        
        Args:
            x_start: 原始信号
            x_t: t时刻的信号
            t: 时间步
            
        Returns:
            后验均值、后验方差、后验对数方差
        """
        posterior_mean_coef1_t = self.posterior_mean_coef1[t].reshape(-1, 1, 1)
        posterior_mean_coef2_t = self.posterior_mean_coef2[t].reshape(-1, 1, 1)
        
        posterior_mean = posterior_mean_coef1_t * x_start + posterior_mean_coef2_t * x_t
        posterior_variance = self.posterior_variance[t].reshape(-1, 1, 1)
        posterior_log_variance_clipped = self.posterior_log_variance_clipped[t].reshape(-1, 1, 1)
        
        return posterior_mean, posterior_variance, posterior_log_variance_clipped
    
    def predict_start_from_noise(self, x_t: torch.Tensor, t: torch.Tensor, 
                                noise: torch.Tensor) -> torch.Tensor:
        """
        从噪声预测原始信号
        
        Args:
            x_t: t时刻的信号
            t: 时间步
            noise: 预测的噪声
            
        Returns:
            预测的原始信号
        """
        sqrt_recip_alphas_cumprod_t = self.sqrt_recip_alphas_cumprod[t].reshape(-1, 1, 1)
        sqrt_recipm1_alphas_cumprod_t = self.sqrt_recipm1_alphas_cumprod[t].reshape(-1, 1, 1)
        
        return sqrt_recip_alphas_cumprod_t * x_t - sqrt_recipm1_alphas_cumprod_t * noise
    
    def p_mean_variance(self, x_t: torch.Tensor, t: torch.Tensor, 
                       class_labels: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算反向过程的均值和方差
        
        Args:
            x_t: t时刻的信号
            t: 时间步
            class_labels: 类别标签
            
        Returns:
            预测均值、预测方差
        """
        # 预测噪声
        predicted_noise = self.denoise_model(x_t, t, class_labels)
        
        # 预测原始信号
        x_start = self.predict_start_from_noise(x_t, t, predicted_noise)
        
        # 计算后验均值和方差
        model_mean, posterior_variance, _ = self.q_posterior_mean_variance(x_start, x_t, t)
        
        return model_mean, posterior_variance
    
    def p_sample(self, x_t: torch.Tensor, t: torch.Tensor, 
                class_labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        反向采样一步
        
        Args:
            x_t: t时刻的信号
            t: 时间步
            class_labels: 类别标签
            
        Returns:
            t-1时刻的信号
        """
        model_mean, model_variance = self.p_mean_variance(x_t, t, class_labels)
        
        noise = torch.randn_like(x_t)
        # 当t=0时不添加噪声
        nonzero_mask = (t != 0).float().reshape(-1, 1, 1)
        
        return model_mean + nonzero_mask * torch.sqrt(model_variance) * noise
    
    def p_sample_loop(self, shape: Tuple[int, ...], 
                     class_labels: Optional[torch.Tensor] = None,
                     device: Optional[torch.device] = None) -> torch.Tensor:
        """
        完整的反向采样过程
        
        Args:
            shape: 生成信号的形状
            class_labels: 类别标签
            device: 计算设备
            
        Returns:
            生成的信号
        """
        if device is None:
            device = next(self.parameters()).device
        
        # 从纯噪声开始
        if isinstance(shape, torch.Tensor):
            shape = tuple(shape.tolist())
        x = torch.randn(*shape, device=device)
        
        # 反向采样
        for i in reversed(range(self.timesteps)):
            t = torch.full((shape[0],), i, device=device, dtype=torch.long)
            x = self.p_sample(x, t, class_labels)
        
        return x
    
    def sample(self, num_samples: int, signal_length: int,
              class_labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        生成样本
        
        Args:
            num_samples: 生成样本数量
            signal_length: 信号长度
            class_labels: 类别标签
            
        Returns:
            生成的信号
        """
        shape = (num_samples, 1, signal_length)
        return self.p_sample_loop(shape, class_labels)
    
    def training_losses(self, x_start: torch.Tensor, 
                       class_labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        计算训练损失
        
        Args:
            x_start: 原始信号
            class_labels: 类别标签
            
        Returns:
            损失字典
        """
        batch_size = x_start.shape[0]
        device = x_start.device
        
        # 随机采样时间步
        t = torch.randint(0, self.timesteps, (batch_size,), device=device)
        
        # 生成噪声
        noise = torch.randn_like(x_start)
        
        # 前向扩散
        x_t = self.q_sample(x_start, t, noise)
        
        # 预测噪声
        predicted_noise = self.denoise_model(x_t, t, class_labels)
        
        # 计算损失
        loss = F.mse_loss(predicted_noise, noise)
        
        return {"loss": loss}
    
    def forward(self, x_start: torch.Tensor, 
               class_labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播（训练时使用）
        
        Args:
            x_start: 原始信号
            class_labels: 类别标签
            
        Returns:
            损失字典
        """
        return self.training_losses(x_start, class_labels)
