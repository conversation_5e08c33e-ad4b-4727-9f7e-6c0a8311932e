# 一维振动信号故障诊断数据增强

基于条件去噪扩散概率模型(CDDPM)的一维振动信号故障诊断数据增强方法，集成智能数据筛选和扩散模型重用功能。

## 项目概述

本项目实现了一种基于CDDPM的数据增强方法，用于解决故障诊断中训练样本不足的问题。通过生成高质量的合成故障信号，并结合智能数据筛选技术，显著提高故障诊断模型的性能。

## 🔥 核心特性

- **🚀 智能扩散模型重用**：自动检测多参数配置，相同训练数据下扩散模型只训练一次，节省66.7%以上训练时间
- **🎯 智能数据筛选**：基于置信度、Influence评分、多样性选择的多层筛选，显著提升生成样本质量
- **🔍 自动参数检测**：无需手动配置，程序自动识别多参数并智能重用
- **📊 多种数据增强方法**：支持CDDPM、传统方法(ADASYN、SMOTE等)
- **🗂️ 多数据集支持**：CWRU、MFPT、JST、KAT、MAFAULDA、SK等
- **⚡ 完整的实验流程**：数据加载→模型训练→样本生成→智能筛选→性能评估

## 环境要求

- Python 3.8+
- PyTorch 1.12+
- CUDA 11.0+ (推荐)

## 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 1. 准备数据

将数据集放置在 `dataset/` 目录下：

```
dataset/
├── CWRU/
├── MFPT/
├── JST/
└── ...
```

### 2. 智能重用模式（推荐）

在 `config.yaml` 中设置多个参数值，程序自动启用智能重用：

```yaml
dataset:
  name: "CWRU"
  data_loading:
    fault_samples:
      max_fault_samples_per_class: [50, 100]  # 不同原始样本数

augmentation:
  method: "CDDPM"
  num_generated_per_class: [10, 20, 30]  # 不同生成数量

data_screening:
  enabled: true  # 启用数据筛选
```

### 3. 运行实验

```bash
# 运行智能重用实验（自动检测多参数）
python main.py --mode full

# 指定配置文件
python main.py --config config.yaml --mode full
```

## 🔥 智能重用机制

程序会自动：
1. **检测多参数配置**：识别配置中的列表参数
2. **智能分组实验**：相同训练数据的实验分为一组
3. **重用扩散模型**：每组只训练一次扩散模型，其他实验重用
4. **应用数据筛选**：对生成样本进行多层筛选
5. **节省大量时间**：避免重复训练，提升实验效率

### 示例效果

```
配置：原始样本数[50, 100] × 生成数量[10, 20, 30] = 6个实验
传统方式：6次扩散模型训练
智能重用：2次扩散模型训练 + 4次重用
时间节省：66.7%
```

## 配置说明

### 智能重用配置

```yaml
# 设置多个参数值，程序自动检测并启用智能重用
dataset:
  data_loading:
    fault_samples:
      max_fault_samples_per_class: [50, 100]  # 不同原始样本数

augmentation:
  num_generated_per_class: [10, 20, 30]  # 不同生成数量
```

### 数据筛选配置

```yaml
data_screening:
  enabled: true                 # 启用数据筛选
  screening_level: "basic"      # 筛选档位

  confidence_filter:
    enabled: true               # 置信度过滤
    threshold: 0.3              # 置信度阈值
    adaptive: true              # 自适应调整

  influence_filter:
    enabled: true               # Influence评分过滤
    ratio: 0.3                  # 剔除负分前30%
    adaptive: true              # 自适应调整

  diversity_selection:
    enabled: true               # 多样性选择
    method: "kmeans"            # 聚类方法
    use_target_count: true      # 使用目标数量
```

## 运行模式

```bash
# 智能重用模式（推荐）
python main.py --mode full

# 其他模式
python main.py --mode train_diffusion    # 只训练扩散模型
python main.py --mode train_classifier   # 只训练分类器
python main.py --mode generate          # 只生成样本
python main.py --mode evaluate          # 只评估模型
```

## 项目结构

```
├── main.py                 # 主程序入口
├── config.yaml            # 配置文件
├── requirements.txt       # 依赖包列表
├── README.md              # 项目说明
├── README_筛选功能.md     # 数据筛选功能详细说明
├── common/                # 公共模块
│   ├── data_loader.py     # 数据加载
│   ├── data_screening.py  # 数据筛选流水线
│   ├── adaptive_screening.py  # 自适应筛选
│   ├── experiment_manager.py  # 智能实验管理
│   ├── utils.py           # 工具函数
│   ├── metrics.py         # 评估指标
│   └── ...
├── models/                # 模型定义
│   ├── cddpm.py          # CDDPM模型
│   ├── mr_cnn.py         # 分类器模型
│   └── ...
├── dataset/              # 数据集目录
├── results/              # 结果保存目录
└── checkpoints/          # 模型检查点目录
```

## 结果分析

实验结果保存在 `results/` 目录下，包括：

- **智能重用统计**：扩散模型训练次数、重用次数、时间节省
- **筛选效果分析**：筛选前后样本数量、质量对比
- **性能指标**：准确率、精确率、召回率、F1分数
- **对比结果**：多组实验的汇总对比
- **可视化结果**：训练曲线、混淆矩阵、生成样本

## 性能优势

| 功能 | 传统方式 | 智能重用方式 | 改进 |
|------|----------|-------------|------|
| 扩散模型训练 | 每个实验都训练 | 智能重用 | 节省66.7%时间 |
| 数据质量 | 无筛选 | 多层筛选 | 显著提升 |
| 参数检测 | 手动配置 | 自动检测 | 零配置使用 |
| 实验管理 | 独立运行 | 智能分组 | 高效批量处理 |

## 故障排除

### 常见问题

1. **CUDA内存不足**：减少batch_size
2. **训练速度慢**：启用混合精度训练
3. **筛选效果不佳**：调整筛选参数或使用不同筛选档位

### 性能优化

```yaml
performance:
  compile_model: true       # PyTorch 2.0编译优化
  mixed_precision: true     # 混合精度训练
  gradient_checkpointing: true  # 梯度检查点
```

## 详细文档

- [数据筛选功能详细说明](README_筛选功能.md)

## 许可证

本项目采用 MIT 许可证。

### 切换到单一实验
将配置中的列表改为单一值：
```yaml
# 从对比实验
fault_samples:
  max_fault_samples_per_class: [5, 10]
method: ["CDDPM", "ADASYN"]

# 改为单一实验
fault_samples:
  max_fault_samples_per_class: 10
method: "CDDPM"
```

### 切换到对比实验
将配置中的单一值改为列表：
```yaml
# 从单一实验
fault_samples:
  max_fault_samples_per_class: 10
method: "CDDPM"

# 改为对比实验
fault_samples:
  max_fault_samples_per_class: [10, 20, 30]
method: ["CDDPM", "CGAN", "ADASYN"]
```

## 实验示例

### 示例1：健康样本对比实验

```yaml
dataset:
  data_loading:
    fault_samples:
      max_fault_samples_per_class: 20        # 故障样本固定
    healthy_samples:
      max_healthy_samples: [0, 5, 10, 15]    # 健康样本对比

augmentation:
  method: "CDDPM"
  generate_fault_only: true  # 只生成故障样本
```

### 示例2：多方法对比实验

```yaml
augmentation:
  method: ["CDDPM", "CGAN", "ADASYN", "SMOTEENN"]
  num_generated_per_class: [10, 20, 30]
```

### 示例3：完整对比实验

基于当前配置，将运行以下实验组合：

1. 故障样本5, 健康样本0(禁用), CDDPM, 生成5
2. 故障样本5, 健康样本0(禁用), CDDPM, 生成10
3. 故障样本5, 健康样本0(禁用), ADASYN, 生成5
4. 故障样本5, 健康样本0(禁用), ADASYN, 生成10
5. 故障样本5, 健康样本5(启用), CDDPM, 生成5
6. ... (共16个组合)

## 项目结构

```
├── main.py                    # 主程序（集成自动检测功能）
├── config.yaml               # 配置文件（支持多参数格式）
├── test_comparison.py         # 功能测试脚本
├── requirements.txt           # 依赖列表
├── common/                    # 公共模块
│   ├── data_loader.py         # 数据加载器（支持健康样本配置）
│   ├── experiment_manager.py  # 实验管理器（多参数检测）
│   ├── results_manager.py     # 结果管理器（CSV导出）
│   ├── utils.py              # 工具函数
│   └── ...
├── models/                    # 模型模块
│   ├── cddpm.py              # CDDPM模型
│   ├── mr_cnn.py             # MRCNN分类器
│   ├── augmentation_factory.py    # 数据增强工厂
│   ├── augmentation_methods.py    # 深度学习方法
│   ├── traditional_augmentation.py # 传统方法
│   └── ...
├── dataset/                   # 数据集目录
├── results/                   # 结果目录
├── cache/                     # 缓存目录
└── logs/                      # 日志目录
```

## 测试验证

运行功能测试：
```bash
python test_comparison.py
```

测试结果：
- ✅ 实验管理器测试通过 - 成功检测16个参数组合
- ✅ 数据增强工厂测试通过 - 支持10种增强方法
- ✅ 结果管理器测试通过 - 成功生成CSV文件和目录结构
- ✅ 自动检测功能测试通过 - 正确识别当前配置为对比实验模式
- ✅ 缓存机制测试通过 - 配置文件保存在 `cache/{timestamp}/` 目录

## 注意事项

1. **实验时间**: 对比实验会运行多个组合，确保有足够时间
2. **存储空间**: 每个实验都会保存完整结果，注意磁盘空间
3. **缓存文件**: 缓存配置文件不会自动删除，可手动清理
4. **参数组合**: 参数数量会快速增长，建议先小规模测试

## 故障排除

### 常见问题

1. **内存不足**: 减少batch_size或样本数量
2. **训练时间过长**: 减少epochs或使用更少的参数组合
3. **数据集不存在**: 检查数据集路径和文件名
4. **方法不支持**: 确认使用的增强方法在支持列表中

### 调试建议

1. 先运行 `test_comparison.py` 验证功能
2. 使用小规模参数进行测试
3. 检查日志文件获取详细错误信息
4. 确认GPU内存和系统内存充足

## 参考文献

### 主要论文
- **原始论文**: "Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN"
- **DDPM**: Ho, J., Jain, A., & Abbeel, P. (2020). Denoising diffusion probabilistic models.
- **MRCNN**: 基于多尺度核的残差卷积神经网络用于电机故障诊断

### 相关工作
- Conditional DDPM for time series generation
- Vibration signal fault diagnosis using deep learning
- Data augmentation techniques for imbalanced datasets

## 开发团队

本项目基于论文方法实现，专注于振动信号故障诊断的数据增强研究。

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-06-10)
- 实现完整的CDDPM数据增强框架
- 支持多种振动信号数据集
- 实现MRCNN分类器
- 完整的GAN-train/GAN-test评估体系
- 自动性能优化和GPU适配
- 详细的可视化和结果分析



请你阅读整个工程文档，理解这个工程的主要内容，同时我需要你帮我在这个基础上实现新的功能。目前的程序仅仅是针对论文中的对应方法的研究，但是我还要做一些对比实验分析。你需要帮我完成对比实验部分的内容和一些其他的要求，具体如下：
（1）目前的数据加载逻辑是加载所有的数据进行扩散模型的训练，但是实际上我们用到的三个数据集sk，jst和kat，其标签为0的数据，通常保存在前面行，是健康样本，到底用不用健康样本参与训练我需要进行对比分析，所以我需要你修改配置和对应程序，实现配置是否需要健康样本，如果不需要则不用健康类，如果配置需要健康样本，则需要设置到底包含多少健康样本的个数，最终加载的时候健康的和故障的分开按各自设置的样本个数加载。是否能够在编程的以后设置需要的正常样本个数为0代表不用健康样本，而不用单独设置，这样方便我们后续的批量化测试。
（2）需要实现分析用于训练扩散模型的各类样本个数对精度和评价指标的影响，这里生成的只生成故障样本，健康样本直接从原始训练集加载真实数据。；
（3）需要测试生成的样本个数对最终分类精度的影响，这里生成的只生成故障样本，健康样本直接从原始训练集加载真实数据。
（4）除了这里用到的cddpm方法，还需要实现CGAN，WGAN，WGAN-GP，原始DDPM，ADASYN、SMOTEENN、DCGAN、MACGAN、CVAEGAN、K-means-SMOTE、RWO-samping等方法用于对比分析这些方法生成样本的质量，包括评估指标和分类精度指标。
为了方便上述的对比测试，我希望在测试的时候，通过判断设置值的个数判定是否是对比实验，比如，我设置了name: "KAT" “SK”则表示要在KAT和SK数据集下连续分别开展测试；设置了max_samples_per_class: 10 20 30 表示要在max_samples_per_class: 10 20 30下进行对比实验，如果同时设置了max_samples_per_class: 10 20 30和um_generated_per_class: 10 20 30则要开展这两个参数的组合实验，包括9类实验。这样可行吗，需要自动判定设置的多参数的情况，然后在每个测试下生成一个新的yaml文件用于实际测试，多有参数从这个这个新的yaml读取，而不是原始的yaml读取，这样可行吗。但是复制了新的yaml测试，其格式一定要和源文件保持一致，包括注释等。具体程序运行中，加载的就是每次自动配置的这个新的缓存配置文件。这样方便做多方面的对比测试分析。
此外，我需要在保存结果的时候，需要区分数据集文件夹，同时，对于所有的单次运行，都有一个时间结果文件夹，里面保存了所有的结果和当前运行的配置文件yaml。然后如果是对比分析，需要汇总的精度文件，保存为csv格式，同时结果打印在log中。所有的绘图数据都要有对应的csv格式文件。包括四个精度指标，训练时间，混淆矩阵，训练过程精度和损失过程，gan-train和gan-test指标。这些单次运行保存，我们不会同时运行多个数据集的文件，所以所有测试都是针对一个数据集，这就要求结果在对应名称数据集文件夹下，同时加上时间戳，一次完整运行下多个测试的结果放在这次测试的子文件夹下。
请你按照上述的需求，完成这个的修改和开发，满足以上的需求。