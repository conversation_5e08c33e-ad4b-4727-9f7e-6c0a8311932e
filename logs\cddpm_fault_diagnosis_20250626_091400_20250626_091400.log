2025-06-26 09:14:00,939 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_20250626_091400_20250626_091400.log
2025-06-26 09:14:00,940 - __main__ - INFO - ================================================================================
2025-06-26 09:14:00,940 - __main__ - INFO - 开始单一实验: cddpm_fault_diagnosis
2025-06-26 09:14:00,940 - __main__ - INFO - 数据集: KAT
2025-06-26 09:14:00,940 - __main__ - INFO - ================================================================================
2025-06-26 09:14:00,940 - __main__ - INFO - 🚀 实验开始
2025-06-26 09:14:00,940 - __main__ - INFO - ================================================================================
2025-06-26 09:14:00,941 - __main__ - INFO - 当前实验配置
2025-06-26 09:14:00,941 - __main__ - INFO - ================================================================================
2025-06-26 09:14:00,941 - __main__ - INFO - 数据集: KAT
2025-06-26 09:14:00,941 - __main__ - INFO - 故障样本每类: [3]
2025-06-26 09:14:00,941 - __main__ - INFO - 健康样本总数: -1
2025-06-26 09:14:00,941 - __main__ - INFO - 信号长度: 1024
2025-06-26 09:14:00,941 - __main__ - INFO - 归一化方法: minmax
2025-06-26 09:14:00,941 - __main__ - INFO - 增强方法: CDDPM
2025-06-26 09:14:00,941 - __main__ - INFO - 每类生成样本数: [3]
2025-06-26 09:14:00,941 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:14:00,942 - __main__ - INFO - 扩散模型训练轮数: 100
2025-06-26 09:14:00,942 - __main__ - INFO - 分类器训练轮数: 100
2025-06-26 09:14:00,942 - __main__ - INFO - 扩散模型学习率: 0.0001
2025-06-26 09:14:00,942 - __main__ - INFO - 分类器学习率: 0.0001
2025-06-26 09:14:00,942 - __main__ - INFO - 设备: auto
2025-06-26 09:14:00,942 - __main__ - INFO - 性能模式: auto
2025-06-26 09:14:00,942 - __main__ - INFO - 随机种子: 42
2025-06-26 09:14:00,942 - __main__ - INFO - ================================================================================
2025-06-26 09:14:00,942 - __main__ - INFO - ============================================================
2025-06-26 09:14:00,942 - __main__ - INFO - 健康样本配置验证
2025-06-26 09:14:00,942 - __main__ - INFO - ============================================================
2025-06-26 09:14:00,942 - __main__ - INFO - 扩散训练健康样本数量: -1
2025-06-26 09:14:00,942 - __main__ - INFO - 只生成故障样本: True
2025-06-26 09:14:00,942 - __main__ - INFO - 分类器使用真实健康样本: True
2025-06-26 09:14:00,942 - __main__ - INFO - 真实健康样本数量: -1
2025-06-26 09:14:00,943 - __main__ - INFO - ✅ 健康样本配置验证通过
2025-06-26 09:14:00,943 - __main__ - INFO - ============================================================
2025-06-26 09:14:00,943 - __main__ - INFO - 使用设备: cuda
2025-06-26 09:14:00,944 - __main__ - INFO - 加载数据...
2025-06-26 09:14:00,945 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-26 09:14:00,945 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-26 09:14:00,947 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:14:00,949 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-26 09:14:00,949 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:14:00,949 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-26 09:14:00,949 - common.data_loader - INFO - 健康样本数量设置为-1，自动匹配故障样本数量: 3
2025-06-26 09:14:00,949 - common.data_loader - INFO - 样本配置: 故障样本每类最多3个, 健康样本最多3个
2025-06-26 09:14:00,949 - common.data_loader - INFO - 健康样本使用状态: 启用
2025-06-26 09:14:00,949 - common.data_loader - INFO - 类别 0 (健康样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 3 个
2025-06-26 09:14:00,950 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 3 个
2025-06-26 09:14:00,957 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-26 09:14:00,961 - common.data_loader - INFO - 数据加载完成:
2025-06-26 09:14:00,962 - common.data_loader - INFO -   训练样本: 24
2025-06-26 09:14:00,962 - common.data_loader - INFO -   测试样本: 1001
2025-06-26 09:14:00,962 - common.data_loader - INFO -   信号长度: 1024
2025-06-26 09:14:00,962 - common.data_loader - INFO -   类别数: 8
2025-06-26 09:14:00,963 - __main__ - INFO - ==================================================
2025-06-26 09:14:00,963 - __main__ - INFO - 开始训练数据增强模型
2025-06-26 09:14:00,963 - __main__ - INFO - ==================================================
2025-06-26 09:14:00,964 - models.augmentation_factory - INFO - 创建数据增强方法: CDDPM
2025-06-26 09:14:00,964 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-26 09:14:00,965 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-26 09:14:00,965 - models.cddpm - INFO -   引导强度: 1.0
2025-06-26 09:14:00,965 - models.cddpm - INFO -   类别数量: 8
2025-06-26 09:14:00,980 - __main__ - ERROR - 实验失败: 'diffusion'
Traceback (most recent call last):
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1813, in main
    results = run_comparison_experiments(args.config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1578, in run_comparison_experiments
    return run_single_experiment(base_config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1784, in run_single_experiment
    results = run_experiment(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 1180, in run_experiment
    augmentation_results = generator.train_method(train_loader_split, val_loader)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 757, in train_method
    self.initialize_method()
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\main.py", line 748, in initialize_method
    self.augmentation_method = UnifiedAugmentationInterface(
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 416, in __init__
    self.method = AugmentationFactory.create_method(method_name, config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 335, in create_method
    return CDDPMWrapper(config, device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\augmentation_factory.py", line 36, in __init__
    self.model = CDDPM(config).to(device)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\cddpm.py", line 118, in __init__
    self.denoise_model = UNet1D(config)
  File "E:\BaiduSyncdisk\1-科研\16____故障诊断14_一维数据增强\250610_code\models\unet.py", line 200, in __init__
    model_config = config['models']['diffusion']
KeyError: 'diffusion'
