"""
生成数据筛选模块
基于置信度过滤、Influence评分、离群检测和多样性选择的数据筛选流水线
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from sklearn.cluster import KMeans
from sklearn.neighbors import LocalOutlierFactor
from sklearn.svm import OneClassSVM
from sklearn.covariance import EmpiricalCovariance
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class ConfidenceFilter:
    """置信度过滤器"""
    
    def __init__(self, config: Dict):
        self.config = config['data_screening']['confidence_filter']
        self.threshold = self.config['threshold']
        self.per_class = self.config['per_class']
        
    def filter(self, classifier: nn.Module, data: torch.Tensor, 
               labels: torch.Tensor, device: torch.device) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        基于置信度过滤数据
        
        Args:
            classifier: 训练好的分类器
            data: 生成的数据
            labels: 数据标签
            device: 计算设备
            
        Returns:
            filtered_data: 过滤后的数据
            filtered_labels: 过滤后的标签
        """
        logger.info(f"开始置信度过滤，阈值: {self.threshold}")
        
        classifier.eval()
        all_indices = []
        
        with torch.no_grad():
            # 分批处理以避免内存问题
            batch_size = 64
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i+batch_size].to(device)
                batch_labels = labels[i:i+batch_size]
                
                # 获取预测概率
                outputs = classifier(batch_data)
                probs = F.softmax(outputs, dim=1)
                
                # 获取最大概率（置信度）
                max_probs, predicted = torch.max(probs, 1)
                
                # 筛选高置信度样本
                if self.per_class:
                    # 按类别独立筛选
                    batch_indices = []
                    for class_idx in torch.unique(batch_labels):
                        class_mask = batch_labels == class_idx
                        class_probs = max_probs[class_mask]
                        class_threshold = torch.quantile(class_probs, 1 - self.threshold)
                        class_valid = class_probs >= class_threshold
                        class_indices = torch.where(class_mask)[0][class_valid]
                        batch_indices.extend((i + class_indices).tolist())
                else:
                    # 全局筛选
                    valid_mask = max_probs >= self.threshold
                    batch_indices = (i + torch.where(valid_mask)[0]).tolist()
                
                all_indices.extend(batch_indices)
        
        # 应用筛选
        filtered_data = data[all_indices]
        filtered_labels = labels[all_indices]
        
        logger.info(f"置信度过滤完成: {len(data)} -> {len(filtered_data)} "
                   f"(保留率: {len(filtered_data)/len(data):.2%})")
        
        return filtered_data, filtered_labels


class InfluenceFilter:
    """Influence评分过滤器"""
    
    def __init__(self, config: Dict):
        self.config = config['data_screening']['influence_filter']
        self.method = self.config['method']
        self.ratio = self.config['ratio']
        self.per_class = self.config['per_class']
        self.batch_size = self.config['batch_size']
        
    def filter(self, classifier: nn.Module, candidate_data: torch.Tensor, 
               candidate_labels: torch.Tensor, val_data: torch.Tensor, 
               val_labels: torch.Tensor, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于Influence评分过滤数据
        
        Args:
            classifier: 训练好的分类器
            candidate_data: 候选数据
            candidate_labels: 候选标签
            val_data: 验证数据
            val_labels: 验证标签
            device: 计算设备
            
        Returns:
            filtered_data: 过滤后的数据
            filtered_labels: 过滤后的标签
        """
        logger.info(f"开始Influence评分过滤，方法: {self.method}, 剔除比例: {self.ratio}")
        
        if self.method == "tracin":
            influence_scores = self._compute_tracin_scores(
                classifier, candidate_data, candidate_labels, 
                val_data, val_labels, device
            )
        else:
            raise NotImplementedError(f"Influence方法 {self.method} 尚未实现")
        
        # 筛选正面影响的样本
        if self.per_class:
            # 按类别独立筛选
            keep_indices = []
            for class_idx in torch.unique(candidate_labels):
                class_mask = candidate_labels == class_idx
                class_scores = influence_scores[class_mask]
                class_indices = torch.where(class_mask)[0]
                
                # 保留正面影响的样本（剔除负分前ratio）
                n_remove = int(len(class_scores) * self.ratio)
                if n_remove > 0:
                    _, sorted_indices = torch.sort(class_scores, descending=True)
                    keep_class_indices = class_indices[sorted_indices[:-n_remove]]
                else:
                    keep_class_indices = class_indices
                
                keep_indices.extend(keep_class_indices.tolist())
        else:
            # 全局筛选
            n_remove = int(len(influence_scores) * self.ratio)
            if n_remove > 0:
                _, sorted_indices = torch.sort(influence_scores, descending=True)
                keep_indices = sorted_indices[:-n_remove].tolist()
            else:
                keep_indices = list(range(len(candidate_data)))
        
        # 应用筛选
        filtered_data = candidate_data[keep_indices]
        filtered_labels = candidate_labels[keep_indices]
        
        logger.info(f"Influence过滤完成: {len(candidate_data)} -> {len(filtered_data)} "
                   f"(保留率: {len(filtered_data)/len(candidate_data):.2%})")
        
        return filtered_data, filtered_labels
    
    def _compute_tracin_scores(self, classifier: nn.Module, candidate_data: torch.Tensor,
                              candidate_labels: torch.Tensor, val_data: torch.Tensor,
                              val_labels: torch.Tensor, device: torch.device) -> torch.Tensor:
        """计算TracIn评分"""
        classifier.eval()
        criterion = nn.CrossEntropyLoss(reduction='none')
        
        # 计算验证集的梯度
        val_gradients = []
        for i in range(0, len(val_data), self.batch_size):
            batch_val_data = val_data[i:i+self.batch_size].to(device)
            batch_val_labels = val_labels[i:i+self.batch_size].to(device)
            
            # 前向传播
            outputs = classifier(batch_val_data)
            losses = criterion(outputs, batch_val_labels)
            
            # 计算每个样本的梯度
            for j, loss in enumerate(losses):
                classifier.zero_grad()
                loss.backward(retain_graph=True)
                
                # 收集梯度
                grad_vec = []
                for param in classifier.parameters():
                    if param.grad is not None:
                        grad_vec.append(param.grad.view(-1))
                
                if grad_vec:
                    val_gradients.append(torch.cat(grad_vec))
        
        # 计算候选样本的影响力评分
        influence_scores = []
        for i in range(0, len(candidate_data), self.batch_size):
            batch_candidate_data = candidate_data[i:i+self.batch_size].to(device)
            batch_candidate_labels = candidate_labels[i:i+self.batch_size].to(device)
            
            # 前向传播
            outputs = classifier(batch_candidate_data)
            losses = criterion(outputs, batch_candidate_labels)
            
            # 计算每个候选样本的影响力
            for j, loss in enumerate(losses):
                classifier.zero_grad()
                loss.backward(retain_graph=True)
                
                # 收集梯度
                grad_vec = []
                for param in classifier.parameters():
                    if param.grad is not None:
                        grad_vec.append(param.grad.view(-1))
                
                if grad_vec:
                    candidate_grad = torch.cat(grad_vec)
                    
                    # 计算与验证集梯度的点积（TracIn）
                    influence = 0.0
                    for val_grad in val_gradients:
                        influence += torch.dot(candidate_grad, val_grad).item()
                    
                    influence_scores.append(-influence)  # TracIn取负号
                else:
                    influence_scores.append(0.0)
        
        return torch.tensor(influence_scores)


class OutlierDetector:
    """离群检测器"""
    
    def __init__(self, config: Dict):
        self.config = config['data_screening']['outlier_detection']
        self.method = self.config['method']
        self.k_neighbors = self.config['k_neighbors']
        self.contamination = self.config['contamination']
        
    def detect(self, classifier: nn.Module, data: torch.Tensor, 
               labels: torch.Tensor, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        检测并移除离群样本
        
        Args:
            classifier: 训练好的分类器
            data: 输入数据
            labels: 数据标签
            device: 计算设备
            
        Returns:
            filtered_data: 过滤后的数据
            filtered_labels: 过滤后的标签
        """
        logger.info(f"开始离群检测，方法: {self.method}")
        
        # 提取特征
        features = self._extract_features(classifier, data, device)
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 离群检测
        if self.method == "lof":
            detector = LocalOutlierFactor(
                n_neighbors=self.k_neighbors,
                contamination=self.contamination
            )
            outlier_labels = detector.fit_predict(features_scaled)
            inlier_mask = outlier_labels == 1
            
        elif self.method == "one_class_svm":
            detector = OneClassSVM(nu=self.contamination)
            outlier_labels = detector.fit_predict(features_scaled)
            inlier_mask = outlier_labels == 1
            
        elif self.method == "mahalanobis":
            # 使用马氏距离检测离群点
            cov = EmpiricalCovariance().fit(features_scaled)
            distances = cov.mahalanobis(features_scaled)
            threshold = np.quantile(distances, 1 - self.contamination)
            inlier_mask = distances <= threshold
            
        else:
            raise ValueError(f"不支持的离群检测方法: {self.method}")
        
        # 应用筛选
        filtered_data = data[inlier_mask]
        filtered_labels = labels[inlier_mask]
        
        logger.info(f"离群检测完成: {len(data)} -> {len(filtered_data)} "
                   f"(保留率: {len(filtered_data)/len(data):.2%})")
        
        return filtered_data, filtered_labels
    
    def _extract_features(self, classifier: nn.Module, data: torch.Tensor, 
                         device: torch.device) -> np.ndarray:
        """提取特征向量"""
        classifier.eval()
        features = []
        
        with torch.no_grad():
            batch_size = 64
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i+batch_size].to(device)
                
                # 使用分类器的features方法提取特征
                if hasattr(classifier, 'features'):
                    batch_features = classifier.features(batch_data)
                else:
                    # 如果没有features方法，使用倒数第二层
                    batch_features = classifier(batch_data)
                
                features.append(batch_features.cpu().numpy())
        
        return np.concatenate(features, axis=0)


class DiversitySelector:
    """多样性子集选择器"""

    def __init__(self, config: Dict):
        self.config = config['data_screening']['diversity_selection']
        self.method = self.config['method']
        self.target_ratio = self.config['target_ratio']
        self.n_clusters = self.config['n_clusters']

    def select(self, classifier: nn.Module, data: torch.Tensor,
               labels: torch.Tensor, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        基于多样性选择子集

        Args:
            classifier: 训练好的分类器
            data: 输入数据
            labels: 数据标签
            device: 计算设备

        Returns:
            selected_data: 选择的数据
            selected_labels: 选择的标签
        """
        logger.info(f"开始多样性选择，方法: {self.method}, 目标比例: {self.target_ratio}")

        # 计算目标样本数
        target_size = int(len(data) * self.target_ratio)
        if target_size >= len(data):
            logger.info("目标样本数大于等于输入样本数，返回原始数据")
            return data, labels

        if self.method == "kmeans":
            selected_indices = self._kmeans_selection(classifier, data, labels, target_size, device)
        elif self.method == "random":
            selected_indices = self._random_selection(data, target_size)
        else:
            raise ValueError(f"不支持的多样性选择方法: {self.method}")

        # 应用选择
        selected_data = data[selected_indices]
        selected_labels = labels[selected_indices]

        logger.info(f"多样性选择完成: {len(data)} -> {len(selected_data)} "
                   f"(选择率: {len(selected_data)/len(data):.2%})")

        return selected_data, selected_labels

    def _kmeans_selection(self, classifier: nn.Module, data: torch.Tensor,
                         labels: torch.Tensor, target_size: int, device: torch.device) -> List[int]:
        """K-means聚类选择"""
        # 提取特征
        features = self._extract_features(classifier, data, device)

        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)

        # 确定聚类数
        if self.n_clusters is None:
            n_clusters = min(target_size, len(data))
        else:
            n_clusters = min(self.n_clusters, target_size, len(data))

        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)

        # 选择每个聚类的中心点（medoid）
        selected_indices = []
        for cluster_id in range(n_clusters):
            cluster_mask = cluster_labels == cluster_id
            cluster_indices = np.where(cluster_mask)[0]

            if len(cluster_indices) > 0:
                # 找到距离聚类中心最近的点
                cluster_center = kmeans.cluster_centers_[cluster_id]
                cluster_features = features_scaled[cluster_mask]

                distances = np.linalg.norm(cluster_features - cluster_center, axis=1)
                medoid_idx = cluster_indices[np.argmin(distances)]
                selected_indices.append(medoid_idx)

        # 如果选择的样本数不足，随机补充
        if len(selected_indices) < target_size:
            remaining_indices = list(set(range(len(data))) - set(selected_indices))
            additional_needed = target_size - len(selected_indices)
            additional_indices = np.random.choice(
                remaining_indices,
                size=min(additional_needed, len(remaining_indices)),
                replace=False
            )
            selected_indices.extend(additional_indices.tolist())

        # 如果选择的样本数过多，随机减少
        elif len(selected_indices) > target_size:
            selected_indices = np.random.choice(
                selected_indices,
                size=target_size,
                replace=False
            ).tolist()

        return selected_indices

    def _random_selection(self, data: torch.Tensor, target_size: int) -> List[int]:
        """随机选择"""
        return np.random.choice(len(data), size=target_size, replace=False).tolist()

    def _extract_features(self, classifier: nn.Module, data: torch.Tensor,
                         device: torch.device) -> np.ndarray:
        """提取特征向量"""
        classifier.eval()
        features = []

        with torch.no_grad():
            batch_size = 64
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i+batch_size].to(device)

                # 使用分类器的features方法提取特征
                if hasattr(classifier, 'features'):
                    batch_features = classifier.features(batch_data)
                else:
                    # 如果没有features方法，使用倒数第二层
                    batch_features = classifier(batch_data)

                features.append(batch_features.cpu().numpy())

        return np.concatenate(features, axis=0)


class DataScreeningPipeline:
    """生成数据筛选流水线"""

    def __init__(self, config: Dict):
        self.config = config
        self.screening_config = config['data_screening']
        self.enabled = self.screening_config['enabled']
        self.screening_level = self.screening_config['screening_level']

        # 初始化各个筛选器
        if self.enabled:
            self.confidence_filter = ConfidenceFilter(config) if self.screening_config['confidence_filter']['enabled'] else None
            self.influence_filter = InfluenceFilter(config) if self.screening_config['influence_filter']['enabled'] else None
            self.outlier_detector = OutlierDetector(config) if self.screening_config['outlier_detection']['enabled'] else None
            self.diversity_selector = DiversitySelector(config) if self.screening_config['diversity_selection']['enabled'] else None

            logger.info(f"数据筛选流水线已初始化，筛选档位: {self.screening_level}")
            self._log_pipeline_config()

    def _log_pipeline_config(self):
        """记录流水线配置"""
        logger.info("筛选流水线配置:")
        logger.info(f"  置信度过滤: {'启用' if self.confidence_filter else '禁用'}")
        logger.info(f"  Influence过滤: {'启用' if self.influence_filter else '禁用'}")
        logger.info(f"  离群检测: {'启用' if self.outlier_detector else '禁用'}")
        logger.info(f"  多样性选择: {'启用' if self.diversity_selector else '禁用'}")

    def screen_generated_data(self, classifier: nn.Module, generated_data: torch.Tensor,
                             generated_labels: torch.Tensor, val_data: torch.Tensor,
                             val_labels: torch.Tensor, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        筛选生成的数据

        Args:
            classifier: 训练好的分类器
            generated_data: 生成的数据
            generated_labels: 生成数据的标签
            val_data: 验证数据
            val_labels: 验证标签
            device: 计算设备

        Returns:
            screened_data: 筛选后的数据
            screened_labels: 筛选后的标签
            screening_stats: 筛选统计信息
        """
        if not self.enabled:
            logger.info("数据筛选未启用，返回原始生成数据")
            return generated_data, generated_labels, {'screening_enabled': False}

        logger.info("=" * 50)
        logger.info("开始生成数据筛选流水线")
        logger.info("=" * 50)

        current_data = generated_data.clone()
        current_labels = generated_labels.clone()

        # 记录筛选统计
        screening_stats = {
            'screening_enabled': True,
            'screening_level': self.screening_level,
            'original_count': len(generated_data),
            'steps': []
        }

        # 步骤1: 置信度过滤
        if self.confidence_filter:
            logger.info("步骤1: 置信度过滤")
            before_count = len(current_data)
            current_data, current_labels = self.confidence_filter.filter(
                classifier, current_data, current_labels, device
            )
            after_count = len(current_data)

            screening_stats['steps'].append({
                'step': 'confidence_filter',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })

        # 步骤2: Influence评分过滤
        if self.influence_filter and len(current_data) > 0:
            logger.info("步骤2: Influence评分过滤")
            before_count = len(current_data)
            current_data, current_labels = self.influence_filter.filter(
                classifier, current_data, current_labels, val_data, val_labels, device
            )
            after_count = len(current_data)

            screening_stats['steps'].append({
                'step': 'influence_filter',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })

        # 步骤3: 离群检测
        if self.outlier_detector and len(current_data) > 0:
            logger.info("步骤3: 离群检测")
            before_count = len(current_data)
            current_data, current_labels = self.outlier_detector.detect(
                classifier, current_data, current_labels, device
            )
            after_count = len(current_data)

            screening_stats['steps'].append({
                'step': 'outlier_detection',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })

        # 步骤4: 多样性子集选择
        if self.diversity_selector and len(current_data) > 0:
            logger.info("步骤4: 多样性子集选择")
            before_count = len(current_data)
            current_data, current_labels = self.diversity_selector.select(
                classifier, current_data, current_labels, device
            )
            after_count = len(current_data)

            screening_stats['steps'].append({
                'step': 'diversity_selection',
                'before_count': before_count,
                'after_count': after_count,
                'retention_rate': after_count / before_count if before_count > 0 else 0
            })

        # 最终统计
        screening_stats['final_count'] = len(current_data)
        screening_stats['overall_retention_rate'] = (
            len(current_data) / len(generated_data) if len(generated_data) > 0 else 0
        )

        logger.info("=" * 50)
        logger.info("数据筛选流水线完成")
        logger.info(f"原始样本数: {screening_stats['original_count']}")
        logger.info(f"筛选后样本数: {screening_stats['final_count']}")
        logger.info(f"总体保留率: {screening_stats['overall_retention_rate']:.2%}")
        logger.info("=" * 50)

        return current_data, current_labels, screening_stats
