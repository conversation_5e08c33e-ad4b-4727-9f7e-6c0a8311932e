"""
一维振动信号故障诊断数据增强主程序
基于条件去噪扩散概率模型(CDDPM)和多尺度残差CNN
"""

import os
import sys
import argparse

# 设置matplotlib后端为非GUI后端，避免Windows上的Qt问题
import matplotlib
matplotlib.use('Agg')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from common.utils import (
    load_config, setup_logging, set_seed, get_device,
    create_directories, Timer, EarlyStopping, save_results, count_parameters,
    manage_checkpoints, fix_compiled_state_dict
)
from common.data_loader import VibrationDataLoader
from common.metrics import ClassificationMetrics, GenerationMetrics, GANEvaluator, extract_features
from common.visualization import Visualizer
from common.performance_manager import apply_performance_config, log_gpu_info
from models import CDDPM, MRCNN

logger = logging.getLogger(__name__)


def validate_healthy_sample_config(config: Dict) -> None:
    """
    验证健康样本配置的一致性

    Args:
        config: 配置字典

    Raises:
        ValueError: 配置不一致时抛出异常
    """
    # 获取配置
    data_config = config['dataset']['data_loading']
    aug_config = config['augmentation']

    healthy_config = data_config.get('healthy_samples', {})
    max_healthy_samples = healthy_config.get('max_healthy_samples', 0)
    generate_fault_only = aug_config.get('generate_fault_only', False)

    classifier_healthy_config = aug_config.get('classifier_healthy_samples', {})
    use_real_when_no_generated = classifier_healthy_config.get('use_real_when_no_generated', True)
    real_healthy_count = classifier_healthy_config.get('real_healthy_count', -1)

    logger.info("=" * 60)
    logger.info("健康样本配置验证")
    logger.info("=" * 60)
    logger.info(f"扩散训练健康样本数量: {max_healthy_samples}")
    logger.info(f"只生成故障样本: {generate_fault_only}")
    logger.info(f"分类器使用真实健康样本: {use_real_when_no_generated}")
    logger.info(f"真实健康样本数量: {real_healthy_count}")

    # 验证规则1: 如果扩散训练中不包含健康样本(=0)，则生成时只能生成故障样本
    if max_healthy_samples == 0 and not generate_fault_only:
        raise ValueError(
            "配置错误: 扩散训练中不包含健康样本(max_healthy_samples=0)时，"
            "生成阶段必须设置为只生成故障样本(generate_fault_only=true)"
        )

    # 验证规则2: 如果只生成故障样本，分类器必须使用真实健康样本
    if generate_fault_only and not use_real_when_no_generated:
        raise ValueError(
            "配置错误: 只生成故障样本时，分类器必须使用真实健康样本"
            "(use_real_when_no_generated=true)"
        )

    # 验证规则3: 如果使用真实健康样本，数量必须合理
    if use_real_when_no_generated and real_healthy_count == 0:
        logger.warning(
            "警告: 分类器设置为使用真实健康样本，但数量为0，这可能导致分类性能下降"
        )

    logger.info("✅ 健康样本配置验证通过")
    logger.info("=" * 60)


class DiffusionTrainer:
    """扩散模型训练器"""
    
    def __init__(self, config: Dict, device: torch.device):
        self.config = config
        self.device = device
        
        # 创建模型
        self.model = CDDPM(config).to(device)

        # 性能优化设置
        opt_config = config.get('system', {}).get('optimization', {})
        self.use_amp = opt_config.get('use_amp', False)
        self.compile_model = opt_config.get('compile_model', False)
        self.channels_last = opt_config.get('channels_last', False)

        # 应用优化
        if self.channels_last:
            self.model = self.model.to(memory_format=torch.channels_last)
            logger.info("启用channels_last内存格式")

        if self.compile_model and hasattr(torch, 'compile'):
            self.model = torch.compile(self.model)
            logger.info("启用PyTorch 2.0编译优化")

        if self.use_amp:
            self.scaler = torch.cuda.amp.GradScaler()
            logger.info("启用自动混合精度训练")

        # 优化器和调度器
        train_config = config['training']['diffusion']
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )
        
        self._setup_scheduler()
        
        # 早停
        early_stop_config = train_config['early_stopping']
        self.early_stopping = EarlyStopping(
            patience=early_stop_config['patience'],
            min_delta=early_stop_config['min_delta'],
            monitor=early_stop_config['monitor'],
            mode=early_stop_config.get('mode', 'min'),
            enabled=early_stop_config.get('enabled', True),
            restore_best_weights=early_stop_config.get('restore_best_weights', True),
            weighted_loss_config=early_stop_config.get('weighted_loss', {})
        )
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        
        logger.info(f"扩散模型参数数量: {count_parameters(self.model):,}")
    
    def _setup_scheduler(self):
        """设置学习率调度器"""
        scheduler_config = self.config['training']['diffusion']['scheduler']
        scheduler_type = scheduler_config['type']
        
        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=scheduler_config['T_max'],
                eta_min=scheduler_config['eta_min']
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config['step_size'],
                gamma=scheduler_config['gamma']
            )
        elif scheduler_type == 'exponential':
            self.scheduler = optim.lr_scheduler.ExponentialLR(
                self.optimizer,
                gamma=scheduler_config['gamma']
            )
        else:
            self.scheduler = None
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for _, (data, labels) in enumerate(train_loader):
            data = data.to(self.device)
            labels = labels.to(self.device)
            
            # 确保数据格式正确
            if data.dim() == 2:
                data = data.unsqueeze(1)  # 添加通道维度
            
            self.optimizer.zero_grad()

            # 前向传播（支持混合精度）
            if self.use_amp:
                with torch.cuda.amp.autocast():
                    losses = self.model(data, labels)
                    loss = losses['loss']

                # 反向传播（混合精度）
                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # 标准前向传播
                losses = self.model(data, labels)
                loss = losses['loss']

                # 标准反向传播
                loss.backward()
                self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 移除batch级别的日志输出，只保留epoch级别的信息
        
        return total_loss / num_batches
    
    def validate_epoch(self, val_loader: DataLoader) -> float:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for data, labels in val_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)
                
                if data.dim() == 2:
                    data = data.unsqueeze(1)
                
                losses = self.model(data, labels)
                loss = losses['loss']
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict:
        """完整训练过程"""
        logger.info("开始训练扩散模型...")
        
        train_config = self.config['training']['diffusion']
        epochs = train_config['epochs']
        save_every = self.config['system']['save']['save_every_n_epochs']
        
        timer = Timer()
        timer.start()
        
        best_val_loss = float('inf')
        best_train_loss = float('inf')

        # 获取最佳模型判断配置
        best_model_config = train_config.get('best_model_criteria', {})
        best_metric = best_model_config.get('metric', 'val_loss')
        best_mode = best_model_config.get('mode', 'min')

        # 初始化最佳分数
        if best_mode == 'min':
            best_score = float('inf')
        else:
            best_score = float('-inf')

        # 加权损失配置
        weighted_config = best_model_config.get('weighted_loss', {})
        train_weight = weighted_config.get('train_weight', 0.7)
        val_weight = weighted_config.get('val_weight', 0.3)

        logger.info(f"最佳模型判断标准: {best_metric} ({best_mode})")
        if best_metric == 'weighted_loss':
            logger.info(f"加权损失配置: 训练损失权重={train_weight}, 验证损失权重={val_weight}")

        for epoch in range(epochs):
            epoch_timer = Timer()
            epoch_timer.start()

            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss = self.validate_epoch(val_loader)

            # 更新学习率
            if self.scheduler is not None:
                self.scheduler.step()

            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)

            epoch_timer.stop()

            # 检查保存状态
            save_info = []

            # 计算当前评估指标
            if best_metric == 'train_loss':
                current_score = train_loss
            elif best_metric == 'val_loss':
                current_score = val_loss
            elif best_metric == 'weighted_loss':
                current_score = train_weight * train_loss + val_weight * val_loss
            else:
                current_score = val_loss  # 默认使用验证损失
                logger.warning(f"未知的最佳模型判断指标: {best_metric}, 使用默认的验证损失")

            # 判断是否为最佳模型
            is_best = False
            if best_mode == 'min':
                if current_score < best_score:
                    best_score = current_score
                    is_best = True
            else:  # max
                if current_score > best_score:
                    best_score = current_score
                    is_best = True

            # 保存最佳模型
            if is_best:
                self._save_checkpoint('best', epoch, current_score)
                save_info.append(f"Best✓({best_metric}={current_score:.6f})")

            # 更新最佳训练损失（用于早停）
            if train_loss < best_train_loss:
                best_train_loss = train_loss

            # 更新最佳验证损失（用于记录）
            if val_loss < best_val_loss:
                best_val_loss = val_loss

            # 定期保存过程权重（仅当不是只保存最佳模型时）
            save_config = self.config['system']['save']
            if (not save_config.get('save_best_only', True) and
                (epoch + 1) % save_every == 0):
                self._save_checkpoint('process', epoch, val_loss)
                save_info.append("Process✓")

            # 单行显示epoch信息（包含保存状态）
            lr = self.optimizer.param_groups[0]['lr']
            save_status = f" | Save: {', '.join(save_info)}" if save_info else ""

            # 构建损失显示字符串
            loss_info = f"Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f}"

            # 如果使用加权损失，也显示加权损失
            if best_metric == 'weighted_loss':
                weighted_loss = train_weight * train_loss + val_weight * val_loss
                loss_info += f" | Weighted Loss: {weighted_loss:.6f}"

            logger.info(f"Epoch {epoch+1:3d}/{epochs} | {loss_info} | LR: {lr:.2e} | Time: {epoch_timer.elapsed_str()}{save_status}")
            
            # 早停检查（支持多种监控指标）
            if self.early_stopping(train_loss=train_loss, val_loss=val_loss,
                                  model_state_dict=self.model.state_dict()):
                monitor_metric = self.early_stopping.monitor
                logger.info(f"早停触发，在第{epoch+1}轮停止训练（基于{monitor_metric}）")
                # 恢复最佳权重
                if self.early_stopping.restore_best_weights and self.early_stopping.get_best_weights():
                    self.model.load_state_dict(self.early_stopping.get_best_weights())
                    logger.info("已恢复最佳权重")
                break
        
        timer.stop()
        
        training_results = {
            'total_epochs': epoch + 1,
            'best_val_loss': best_val_loss,
            'training_time': timer.elapsed(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        
        logger.info(f"扩散模型训练完成，用时: {timer.elapsed_str()}")
        logger.info(f"最佳验证损失: {best_val_loss:.6f}")
        
        return training_results
    
    def _save_checkpoint(self, checkpoint_type: str, epoch: int, score: float):
        """保存检查点"""
        checkpoint_dir = os.path.join(
            self.config['system']['save']['checkpoints_dir'],
            'diffusion',
            checkpoint_type
        )
        os.makedirs(checkpoint_dir, exist_ok=True)

        if checkpoint_type == 'best':
            checkpoint_path = os.path.join(checkpoint_dir, 'best_model.pth')
        else:
            checkpoint_path = os.path.join(checkpoint_dir, f'epoch_{epoch+1}.pth')

        # 获取当前训练和验证损失用于保存
        current_train_loss = self.train_losses[-1] if self.train_losses else 0.0
        current_val_loss = self.val_losses[-1] if self.val_losses else 0.0

        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'val_loss': current_val_loss,  # 保持向后兼容
            'train_loss': current_train_loss,
            'best_score': score,  # 保存用于判断最佳模型的分数
            'config': self.config
        }, checkpoint_path)

        # 管理检查点数量（只对过程检查点进行管理，不删除最佳检查点）
        if checkpoint_type == 'process':
            max_keep = self.config['system']['save']['max_checkpoints_to_keep']
            manage_checkpoints(checkpoint_dir, max_keep)
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        state_dict = checkpoint['model_state_dict']

        # 处理torch.compile导致的参数名前缀问题
        state_dict = fix_compiled_state_dict(state_dict)

        self.model.load_state_dict(state_dict)
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        logger.info(f"检查点已加载: {checkpoint_path}")
        return checkpoint['epoch'], checkpoint['val_loss']


class ClassifierTrainer:
    """分类器训练器"""

    def __init__(self, config: Dict, device: torch.device):
        self.config = config
        self.device = device

        # 创建模型
        self.model = MRCNN(config).to(device)

        # 损失函数
        self.criterion = nn.CrossEntropyLoss()

        # 优化器和调度器
        train_config = config['training']['classifier']
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )

        self._setup_scheduler()

        # 早停
        early_stop_config = train_config['early_stopping']
        self.early_stopping = EarlyStopping(
            patience=early_stop_config['patience'],
            min_delta=early_stop_config['min_delta'],
            monitor=early_stop_config['monitor'],
            mode=early_stop_config.get('mode', 'min'),
            enabled=early_stop_config.get('enabled', True),
            restore_best_weights=early_stop_config.get('restore_best_weights', True),
            weighted_loss_config=early_stop_config.get('weighted_loss', {})
        )

        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_accs = []
        self.val_accs = []

        logger.info(f"分类器参数数量: {count_parameters(self.model):,}")

    def _setup_scheduler(self):
        """设置学习率调度器"""
        scheduler_config = self.config['training']['classifier']['scheduler']
        scheduler_type = scheduler_config['type']

        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=scheduler_config.get('T_max', 100),
                eta_min=scheduler_config.get('eta_min', 0.00001)
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config['step_size'],
                gamma=scheduler_config['gamma']
            )
        elif scheduler_type == 'exponential':
            self.scheduler = optim.lr_scheduler.ExponentialLR(
                self.optimizer,
                gamma=scheduler_config['gamma']
            )
        else:
            self.scheduler = None

    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for _, (data, labels) in enumerate(train_loader):
            data = data.to(self.device)
            labels = labels.to(self.device)

            self.optimizer.zero_grad()

            # 前向传播
            outputs = self.model(data)
            loss = self.criterion(outputs, labels)

            # 反向传播
            loss.backward()
            self.optimizer.step()

            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            # 移除batch级别的日志输出，只保留epoch级别的信息

        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, labels in val_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)

                outputs = self.model(data)
                loss = self.criterion(outputs, labels)

                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict:
        """完整训练过程"""
        logger.info("开始训练分类器...")

        train_config = self.config['training']['classifier']
        epochs = train_config['epochs']
        save_every = self.config['system']['save']['save_every_n_epochs']

        timer = Timer()
        timer.start()

        best_val_acc = 0.0
        best_train_loss = float('inf')

        for epoch in range(epochs):
            epoch_timer = Timer()
            epoch_timer.start()

            # 训练
            train_loss, train_acc = self.train_epoch(train_loader)

            # 验证
            val_loss, val_acc = self.validate_epoch(val_loader)

            # 更新学习率
            if self.scheduler is not None:
                self.scheduler.step()

            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_accs.append(train_acc)
            self.val_accs.append(val_acc)

            epoch_timer.stop()

            # 检查保存状态
            save_info = []

            # 保存最佳模型（基于验证准确率，但早停基于训练损失）
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                self._save_checkpoint('best', epoch, val_acc)
                save_info.append("Best✓")

            # 更新最佳训练损失（用于早停）
            if train_loss < best_train_loss:
                best_train_loss = train_loss

            # 定期保存过程权重（仅当不是只保存最佳模型时）
            save_config = self.config['system']['save']
            if (not save_config.get('save_best_only', True) and
                (epoch + 1) % save_every == 0):
                self._save_checkpoint('process', epoch, val_acc)
                save_info.append("Process✓")

            # 单行显示epoch信息（包含保存状态）
            lr = self.optimizer.param_groups[0]['lr']
            save_status = f" | Save: {', '.join(save_info)}" if save_info else ""
            logger.info(f"Epoch {epoch+1:3d}/{epochs} | Train Loss: {train_loss:.4f} Acc: {train_acc:.4f} | Val Loss: {val_loss:.4f} Acc: {val_acc:.4f} | LR: {lr:.2e} | Time: {epoch_timer.elapsed_str()}{save_status}")

            # 早停检查（支持多种监控指标）
            if self.early_stopping(train_loss=train_loss, val_loss=val_loss,
                                  model_state_dict=self.model.state_dict()):
                monitor_metric = self.early_stopping.monitor
                logger.info(f"早停触发，在第{epoch+1}轮停止训练（基于{monitor_metric}）")
                # 恢复最佳权重
                if self.early_stopping.restore_best_weights and self.early_stopping.get_best_weights():
                    self.model.load_state_dict(self.early_stopping.get_best_weights())
                    logger.info("已恢复最佳权重")
                break

        timer.stop()

        training_results = {
            'total_epochs': epoch + 1,
            'best_val_accuracy': best_val_acc,
            'training_time': timer.elapsed(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accs': self.train_accs,
            'val_accs': self.val_accs
        }

        logger.info(f"分类器训练完成，用时: {timer.elapsed_str()}")
        logger.info(f"最佳验证准确率: {best_val_acc:.4f}")

        return training_results

    def _save_checkpoint(self, checkpoint_type: str, epoch: int, val_acc: float):
        """保存检查点"""
        checkpoint_dir = os.path.join(
            self.config['system']['save']['checkpoints_dir'],
            'classifier',
            checkpoint_type
        )
        os.makedirs(checkpoint_dir, exist_ok=True)

        if checkpoint_type == 'best':
            checkpoint_path = os.path.join(checkpoint_dir, 'best_model.pth')
        else:
            checkpoint_path = os.path.join(checkpoint_dir, f'epoch_{epoch+1}.pth')

        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'val_accuracy': val_acc,
            'config': self.config
        }, checkpoint_path)

        # 管理检查点数量（只对过程检查点进行管理）
        if checkpoint_type == 'process':
            max_keep = self.config['system']['save']['max_checkpoints_to_keep']
            manage_checkpoints(checkpoint_dir, max_keep)

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        state_dict = checkpoint['model_state_dict']

        # 处理torch.compile导致的参数名前缀问题
        state_dict = fix_compiled_state_dict(state_dict)

        self.model.load_state_dict(state_dict)
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        logger.info(f"检查点已加载: {checkpoint_path}")
        return checkpoint['epoch'], checkpoint['val_accuracy']

    def evaluate(self, test_loader: DataLoader) -> Dict:
        """评估模型"""
        self.model.eval()
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for data, labels in test_loader:
                data = data.to(self.device)
                labels = labels.to(self.device)

                outputs = self.model(data)
                _, predicted = torch.max(outputs, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        # 计算指标
        class_names = self.config['dataset']['datasets'][self.config['dataset']['name']]['class_names']
        metrics = ClassificationMetrics.compute_metrics(
            np.array(all_labels),
            np.array(all_predictions),
            class_names
        )

        # 混淆矩阵
        cm = ClassificationMetrics.compute_confusion_matrix(
            np.array(all_labels),
            np.array(all_predictions)
        )

        return {
            'metrics': metrics,
            'confusion_matrix': cm,
            'predictions': all_predictions,
            'labels': all_labels
        }


class DataGenerator:
    """统一数据生成器，支持多种数据增强方法"""

    def __init__(self, config: Dict, device: torch.device):
        self.config = config
        self.device = device
        self.augmentation_method = None
        self.method_name = config['augmentation']['method']
        self.diffusion_model = None

    def initialize_method(self):
        """初始化数据增强方法"""
        from models import UnifiedAugmentationInterface

        self.augmentation_method = UnifiedAugmentationInterface(
            self.method_name, self.config, self.device
        )

        logger.info(f"数据增强方法已初始化: {self.method_name}")

    def train_method(self, train_loader, val_loader=None) -> Dict:
        """训练数据增强方法（仅适用于深度学习方法）"""
        if self.augmentation_method is None:
            self.initialize_method()

        return self.augmentation_method.train(train_loader, val_loader)

    def load_model(self, checkpoint_path: str):
        """加载训练好的模型"""
        if self.augmentation_method is None:
            self.initialize_method()

        self.augmentation_method.load_model(checkpoint_path)
        logger.info(f"模型已加载: {checkpoint_path}")

    def load_diffusion_model(self, checkpoint_path: str):
        """加载训练好的扩散模型"""
        from models import CDDPM
        from common.utils import fix_compiled_state_dict

        self.diffusion_model = CDDPM(self.config).to(self.device)

        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        state_dict = checkpoint['model_state_dict']

        # 处理torch.compile导致的参数名前缀问题
        state_dict = fix_compiled_state_dict(state_dict)

        self.diffusion_model.load_state_dict(state_dict)
        self.diffusion_model.eval()

        logger.info(f"扩散模型已加载: {checkpoint_path}")

    def generate_samples(self, num_samples_per_class: int, signal_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """使用扩散模型生成样本"""
        # 兼容处理num_samples_per_class可能为列表的情况
        if isinstance(num_samples_per_class, list):
            if len(num_samples_per_class) == 1:
                logger.warning(f"配置中的 num_samples_per_class 是一个列表，自动取其第一个元素。值: {num_samples_per_class}")
                num_samples_per_class = num_samples_per_class[0]
            else:
                # 处理多值列表情况 - 这是组合实验的情况
                # 在组合实验中，ExperimentManager已经处理了参数组合
                # 我们只需要获取当前实验的具体参数值
                if 'experiment' in self.config and 'current_experiment' in self.config['experiment']:
                    current_exp = self.config['experiment']['current_experiment']
                    if 'parameters' in current_exp:
                        # 尝试从当前实验参数中获取num_generated_per_class的值
                        for param_key, param_value in current_exp['parameters'].items():
                            if param_key == 'augmentation.num_generated_per_class':
                                num_samples_per_class = param_value
                                logger.info(f"从当前实验配置中获取生成样本数: {num_samples_per_class}")
                                break
                        else:
                            # 如果没有找到，使用第一个值
                            logger.warning(f"无法从当前实验参数中找到num_generated_per_class，使用列表的第一个值: {num_samples_per_class[0]}")
                            num_samples_per_class = num_samples_per_class[0]
                    else:
                        # 如果没有parameters字段，使用第一个值
                        logger.warning(f"当前实验参数未定义，使用列表的第一个值: {num_samples_per_class[0]}")
                        num_samples_per_class = num_samples_per_class[0]
                else:
                    # 如果不是组合实验，使用第一个值
                    logger.warning(f"非组合实验模式，但num_samples_per_class是多值列表，使用第一个值: {num_samples_per_class[0]}")
                    num_samples_per_class = num_samples_per_class[0]

        if self.diffusion_model is None:
            raise ValueError("扩散模型未加载，请先调用 load_diffusion_model")

        logger.info(f"开始使用扩散模型生成样本，每个类别 {num_samples_per_class} 个...")

        all_generated_data = []
        all_generated_labels = []

        # 获取类别数量
        dataset_name = self.config['dataset']['name']
        num_classes = self.config['dataset']['datasets'][dataset_name]['num_classes']

        # 检查生成样本配置
        generate_fault_only = self.config['augmentation'].get('generate_fault_only', False)
        healthy_label = self.config['dataset']['data_loading'].get('healthy_samples', {}).get('healthy_label', 0)

        if generate_fault_only:
            logger.info(f"只生成故障样本，跳过健康样本（标签={healthy_label}）")
        else:
            logger.info(f"生成故障样本+健康样本，健康样本标签={healthy_label}，每类生成{num_samples_per_class}个")

        with torch.no_grad():
            for class_idx in range(num_classes):
                # 如果只生成故障样本，则跳过健康类别
                if generate_fault_only and class_idx == healthy_label:
                    logger.info(f"跳过类别 {class_idx} (健康样本)")
                    continue
                    
                logger.info(f"生成类别 {class_idx} 的样本...")

                # 生成样本
                shape = (num_samples_per_class, 1, signal_length)

                # 创建类别标签
                class_labels = torch.full(size=(num_samples_per_class,),
                                        fill_value=class_idx,
                                        device=self.device,
                                        dtype=torch.long)

                # 使用扩散模型生成样本
                generated_samples = self.diffusion_model.p_sample_loop(shape, class_labels)

                # 转换为numpy
                generated_samples = generated_samples.squeeze(1).cpu().numpy()

                all_generated_data.append(generated_samples)
                all_generated_labels.extend([class_idx] * num_samples_per_class)

        # 合并所有生成的数据
        generated_data = np.concatenate(all_generated_data, axis=0)
        generated_labels = np.array(all_generated_labels)

        logger.info(f"样本生成完成，总共生成 {len(generated_data)} 个样本")
        return generated_data, generated_labels

    def generate_samples_legacy(self, train_data: np.ndarray, train_labels: np.ndarray,
                        num_samples_per_class: int, signal_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """使用传统方法生成样本（保持向后兼容）"""
        if self.augmentation_method is None:
            self.initialize_method()

        logger.info(f"开始使用 {self.method_name} 生成样本，每个类别 {num_samples_per_class} 个...")

        # 检查是否只生成故障样本
        generate_fault_only = self.config['augmentation'].get('generate_fault_only', False)

        if generate_fault_only:
            healthy_label = self.config['dataset']['data_loading'].get('healthy_samples', {}).get('healthy_label', 0)
            generated_data, generated_labels = self.augmentation_method.generate_fault_only_samples(
                train_data, train_labels, num_samples_per_class, signal_length, healthy_label
            )
            logger.info(f"只生成故障样本，跳过健康样本（标签={healthy_label}）")
        else:
            generated_data, generated_labels = self.augmentation_method.generate_samples(
                train_data, train_labels, num_samples_per_class, signal_length
            )

        logger.info(f"样本生成完成，总共生成 {len(generated_data)} 个样本")
        return generated_data, generated_labels

    def save_model(self, save_path: str):
        """保存模型"""
        if self.augmentation_method is None:
            logger.warning("数据增强方法未初始化，无法保存模型")
            return

        self.augmentation_method.save_model(save_path)

    def save_generated_samples(self, generated_data: np.ndarray, generated_labels: np.ndarray):
        """保存生成的样本"""
        dataset_name = self.config['dataset']['name']
        method_name = self.config['augmentation']['method']

        save_dir = self.config['system']['save']['generated_samples_dir'].format(dataset_name=dataset_name)
        save_dir = os.path.join(save_dir, method_name)
        os.makedirs(save_dir, exist_ok=True)

        # 保存数据
        data_path = os.path.join(save_dir, f'generated_data.npy')
        label_path = os.path.join(save_dir, f'generated_labels.npy')

        np.save(data_path, generated_data)
        np.save(label_path, generated_labels)

        logger.info(f"生成样本已保存:")
        logger.info(f"  数据: {data_path}")
        logger.info(f"  标签: {label_path}")

        return data_path, label_path


def print_experiment_config(config: Dict):
    """打印实验配置信息"""
    logger.info("=" * 80)
    logger.info("当前实验配置")
    logger.info("=" * 80)

    # 数据集配置
    dataset_config = config['dataset']
    logger.info(f"数据集: {dataset_config['name']}")

    # 数据加载配置
    data_loading = dataset_config['data_loading']
    fault_samples = data_loading.get('fault_samples', {}).get('max_fault_samples_per_class', 'N/A')
    healthy_samples = data_loading.get('healthy_samples', {}).get('max_healthy_samples', 'N/A')
    logger.info(f"故障样本每类: {fault_samples}")
    logger.info(f"健康样本总数: {healthy_samples}")
    logger.info(f"信号长度: {data_loading.get('signal_length', 'N/A')}")
    logger.info(f"归一化方法: {data_loading.get('normalization_method', 'N/A')}")

    # 数据增强配置
    aug_config = config['augmentation']
    logger.info(f"增强方法: {aug_config['method']}")
    logger.info(f"每类生成样本数: {aug_config['num_generated_per_class']}")
    logger.info(f"只生成故障样本: {aug_config.get('generate_fault_only', False)}")

    # 训练配置
    training_config = config['training']
    logger.info(f"扩散模型训练轮数: {training_config['diffusion']['epochs']}")
    logger.info(f"分类器训练轮数: {training_config['classifier']['epochs']}")
    logger.info(f"扩散模型学习率: {training_config['diffusion']['learning_rate']}")
    logger.info(f"分类器学习率: {training_config['classifier']['learning_rate']}")

    # 系统配置
    system_config = config['system']
    logger.info(f"设备: {system_config['device']}")
    logger.info(f"性能模式: {system_config.get('performance_mode', 'auto')}")
    logger.info(f"随机种子: {system_config.get('seed', 42)}")

    logger.info("=" * 80)


def adjust_healthy_samples_for_balance(augmented_data: np.ndarray, augmented_labels: np.ndarray,
                                      original_train_data: np.ndarray, original_train_labels: np.ndarray,
                                      config: Dict) -> Tuple[np.ndarray, np.ndarray]:
    """
    根据新的健康样本处理逻辑调整健康样本数量

    Args:
        augmented_data: 增强后的训练数据（原始+生成）
        augmented_labels: 增强后的训练标签
        original_train_data: 完整的原始训练数据（用于获取更多健康样本）
        original_train_labels: 完整的原始训练标签
        config: 配置字典

    Returns:
        调整后的训练数据和标签
    """
    # 获取健康样本配置
    healthy_config = config['dataset']['data_loading'].get('healthy_samples', {})
    healthy_label = healthy_config.get('healthy_label', 0)

    # 获取分类器健康样本配置
    aug_config = config['augmentation']
    generate_fault_only = aug_config.get('generate_fault_only', False)
    classifier_healthy_config = aug_config.get('classifier_healthy_samples', {})
    use_real_when_no_generated = classifier_healthy_config.get('use_real_when_no_generated', True)
    real_healthy_count = classifier_healthy_config.get('real_healthy_count', -1)

    logger.info("健康样本处理逻辑:")
    logger.info(f"  只生成故障样本: {generate_fault_only}")
    logger.info(f"  使用真实健康样本: {use_real_when_no_generated}")
    logger.info(f"  真实健康样本数量配置: {real_healthy_count}")

    # 检查是否有生成的健康样本
    has_generated_healthy = not generate_fault_only and (healthy_label in augmented_labels)

    if has_generated_healthy:
        logger.info("  检测到生成的健康样本，使用生成的健康样本")
        return augmented_data, augmented_labels

    # 如果没有生成健康样本，需要使用真实健康样本
    if not use_real_when_no_generated:
        logger.info("  配置为不使用真实健康样本，移除所有健康样本")
        non_healthy_mask = (augmented_labels != healthy_label)
        return augmented_data[non_healthy_mask], augmented_labels[non_healthy_mask]

    # 使用真实健康样本，需要确定数量
    logger.info("  使用真实健康样本进行分类器训练")

    # 分析当前增强数据中的类别分布
    unique_labels, counts = np.unique(augmented_labels, return_counts=True)
    label_counts = dict(zip(unique_labels, counts))

    # 计算故障样本的平均数量（排除健康样本）
    fault_counts = [count for label, count in label_counts.items() if label != healthy_label]
    if not fault_counts:
        logger.warning("没有找到故障样本，跳过健康样本平衡调整")
        return augmented_data, augmented_labels

    # 计算每类故障样本的平均数量
    avg_fault_samples = int(np.mean(fault_counts))

    # 确定目标健康样本数量
    if real_healthy_count == -1:
        # -1表示与故障样本数量保持一致
        target_healthy_samples = avg_fault_samples
        logger.info(f"  真实健康样本数量设置为-1，自动匹配故障样本平均数量: {target_healthy_samples}")
    elif real_healthy_count == 0:
        # 0表示不使用健康样本
        target_healthy_samples = 0
        logger.info("  真实健康样本数量设置为0，不使用健康样本")
    else:
        # 大于0表示使用指定数量
        target_healthy_samples = real_healthy_count
        logger.info(f"  真实健康样本数量设置为指定值: {target_healthy_samples}")

    logger.info(f"  故障类别数量分布: {[count for label, count in label_counts.items() if label != healthy_label]}")
    logger.info(f"  平均故障样本数: {avg_fault_samples}")
    logger.info(f"  目标健康样本数: {target_healthy_samples}")

    # 获取当前健康样本数量
    current_healthy_samples = label_counts.get(healthy_label, 0)
    logger.info(f"  当前健康样本数: {current_healthy_samples}")

    if target_healthy_samples == 0:
        # 移除所有健康样本
        logger.info("  移除所有健康样本")
        non_healthy_mask = (augmented_labels != healthy_label)
        return augmented_data[non_healthy_mask], augmented_labels[non_healthy_mask]

    if current_healthy_samples == target_healthy_samples:
        logger.info("  健康样本数量已平衡，无需调整")
        return augmented_data, augmented_labels

    # 从原始数据中获取健康样本
    healthy_mask = (original_train_labels.flatten() == healthy_label)
    available_healthy_data = original_train_data[healthy_mask]
    available_healthy_labels = original_train_labels[healthy_mask]

    if len(available_healthy_data) < target_healthy_samples:
        logger.warning(f"  可用健康样本不足: {len(available_healthy_data)} < {target_healthy_samples}")
        target_healthy_samples = len(available_healthy_data)

    # 移除当前的健康样本
    non_healthy_mask = (augmented_labels != healthy_label)
    filtered_data = augmented_data[non_healthy_mask]
    filtered_labels = augmented_labels[non_healthy_mask]

    # 随机选择目标数量的健康样本
    if target_healthy_samples > 0:
        np.random.seed(config['system']['seed'])
        selected_indices = np.random.choice(
            len(available_healthy_data),
            size=target_healthy_samples,
            replace=False
        )
        selected_healthy_data = available_healthy_data[selected_indices]
        selected_healthy_labels = available_healthy_labels[selected_indices]

        # 合并数据
        balanced_data = np.concatenate([filtered_data, selected_healthy_data], axis=0)
        balanced_labels = np.concatenate([filtered_labels, selected_healthy_labels], axis=0)
    else:
        balanced_data = filtered_data
        balanced_labels = filtered_labels

    # 验证结果
    final_unique, final_counts = np.unique(balanced_labels, return_counts=True)
    final_distribution = dict(zip(final_unique, final_counts))

    logger.info(f"  调整后数据分布: {final_distribution}")
    logger.info(f"  健康样本调整: {current_healthy_samples} → {final_distribution.get(healthy_label, 0)}")

    return balanced_data, balanced_labels


def run_experiment(config: Dict):
    """运行完整实验"""
    # 实验开始前打印参数组合信息
    logger.info("🚀 实验开始")
    print_experiment_config(config)

    # 验证健康样本配置
    validate_healthy_sample_config(config)

    # 设置随机种子
    set_seed(config['system']['seed'])

    # 获取设备
    device = get_device(config['system']['device'])
    logger.info(f"使用设备: {device}")

    # 创建目录
    create_directories(config)

    # 创建可视化器
    visualizer = Visualizer(config['system']['save']['results_dir'])

    # 加载数据
    logger.info("加载数据...")
    data_loader = VibrationDataLoader(config)
    train_loader, test_loader, data_info = data_loader.load_data()

    # 分割训练集为训练和验证
    train_val_split = config['dataset']['data_loading']['train_val_split']
    train_dataset = train_loader.dataset
    train_size = int(train_val_split * len(train_dataset))
    val_size = len(train_dataset) - train_size

    train_subset, val_subset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(config['system']['seed'])
    )

    train_loader_split = DataLoader(
        train_subset,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    # 训练数据增强模型
    logger.info("=" * 50)
    logger.info("开始训练数据增强模型")
    logger.info("=" * 50)

    generator = DataGenerator(config, device)

    # 获取训练数据用于传统方法
    train_data_for_traditional = []
    train_labels_for_traditional = []

    for data, labels in train_loader_split:
        train_data_for_traditional.append(data.numpy())
        train_labels_for_traditional.append(labels.numpy())

    train_data_for_traditional = np.concatenate(train_data_for_traditional, axis=0)
    train_labels_for_traditional = np.concatenate(train_labels_for_traditional, axis=0)

    # 训练数据增强方法（如果是深度学习方法）
    augmentation_results = generator.train_method(train_loader_split, val_loader)

    # 可视化训练曲线（如果有的话）
    if 'train_losses' in augmentation_results and augmentation_results['train_losses']:
        visualizer.plot_training_curves(
            augmentation_results.get('train_losses', []),
            augmentation_results.get('val_losses', []),
            title=f"{config['augmentation']['method']} 训练曲线",
            save_name=f"{config['augmentation']['method'].lower()}_training_curves.png"
        )

    # 生成样本
    logger.info("=" * 50)
    logger.info("生成增强样本")
    logger.info("=" * 50)

    # 如果是深度学习方法，加载最佳模型
    method_name = config['augmentation']['method']
    if generator.augmentation_method.is_deep_learning:
        # 构建模型保存路径
        model_save_dir = os.path.join(
            config['system']['save']['checkpoints_dir'],
            'augmentation', method_name.lower()
        )
        os.makedirs(model_save_dir, exist_ok=True)

        # 保存当前模型
        model_save_path = os.path.join(model_save_dir, 'best_model.pth')
        generator.save_model(model_save_path)

    # 生成样本
    num_generated_per_class = config['augmentation']['num_generated_per_class']
    signal_length = config['dataset']['data_loading']['signal_length']

    # 加载训练好的扩散模型
    if hasattr(generator, 'load_diffusion_model'):
        # 对于DataGenerator，需要加载扩散模型
        best_diffusion_path = os.path.join(
            config['system']['save']['checkpoints_dir'],
            'diffusion', 'best', 'best_model.pth'
        )
        if os.path.exists(best_diffusion_path):
            generator.load_diffusion_model(best_diffusion_path)
        else:
            # 如果没有找到最佳模型，尝试从增强方法保存的路径加载
            method_name = config['augmentation']['method']
            model_save_path = os.path.join(
                config['system']['save']['checkpoints_dir'],
                'augmentation', method_name.lower(), 'best_model.pth'
            )
            if os.path.exists(model_save_path):
                generator.load_diffusion_model(model_save_path)
            else:
                raise FileNotFoundError(f"找不到训练好的扩散模型: {best_diffusion_path} 或 {model_save_path}")

    generated_data, generated_labels = generator.generate_samples(
        num_generated_per_class, signal_length
    )

    # 保存生成样本
    if config['augmentation']['save_generated']:
        generator.save_generated_samples(generated_data, generated_labels)

    # 可视化生成样本
    class_names = data_info['class_names']
    visualizer.plot_signal_samples(
        generated_data, generated_labels, class_names,
        num_samples=5, title="生成的信号样本",
        save_name="generated_samples.png"
    )

    # 首先训练基线分类器（仅使用原始数据）
    logger.info("=" * 50)
    logger.info("训练基线分类器（仅原始数据）")
    logger.info("=" * 50)

    # 为基线分类器创建专用的数据加载器（使用分类器的batch_size）
    baseline_train_loader = DataLoader(
        train_subset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    baseline_val_loader = DataLoader(
        val_subset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    baseline_classifier = ClassifierTrainer(config, device)
    baseline_results = baseline_classifier.train(baseline_train_loader, baseline_val_loader)

    # 评估基线分类器
    baseline_eval = baseline_classifier.evaluate(test_loader)
    baseline_metrics = baseline_eval['metrics']

    logger.info("基线分类器性能:")
    logger.info(f"  准确率: {baseline_metrics['accuracy']:.4f}")
    logger.info(f"  精确率: {baseline_metrics['precision']:.4f}")
    logger.info(f"  召回率: {baseline_metrics['recall']:.4f}")
    logger.info(f"  F1分数: {baseline_metrics['f1_score']:.4f}")

    # 检查基线分类器的训练情况
    logger.info("基线分类器训练情况:")
    logger.info(f"  最终训练损失: {baseline_results['train_losses'][-1]:.4f}")
    logger.info(f"  最终验证损失: {baseline_results['val_losses'][-1]:.4f}")
    logger.info(f"  最终训练准确率: {baseline_results['train_accs'][-1]:.4f}")
    logger.info(f"  最终验证准确率: {baseline_results['val_accs'][-1]:.4f}")
    logger.info(f"  实际训练轮数: {baseline_results['total_epochs']}")

    # 如果性能很差，给出警告
    if baseline_metrics['accuracy'] < 0.5:
        logger.warning("⚠️  基线分类器性能异常低，可能的原因:")
        logger.warning("   1. 数据集太小或质量差")
        logger.warning("   2. 模型配置不当")
        logger.warning("   3. 训练时间不足")
        logger.warning("   4. 学习率设置问题")

    # 训练分类器（使用原始数据 + 生成数据）
    logger.info("=" * 50)
    logger.info("训练增强分类器（原始数据 + 生成数据）")
    logger.info("=" * 50)

    # 合并原始训练数据和生成数据
    # 注意：这里使用train_subset（训练部分），而不是完整的train_loader
    original_train_data = []
    original_train_labels = []

    for data, labels in baseline_train_loader:
        original_train_data.append(data.numpy())
        original_train_labels.append(labels.numpy())

    original_train_data = np.concatenate(original_train_data, axis=0)
    original_train_labels = np.concatenate(original_train_labels, axis=0)

    logger.info("数据来源确认:")
    logger.info(f"  基线训练数据来源: train_subset ({len(train_subset)} 样本)")
    logger.info(f"  测试数据来源: test_loader.dataset ({len(test_loader.dataset)} 样本)")
    logger.info(f"  验证数据来源: val_subset ({len(val_subset)} 样本)")

    # 检查并处理数据维度
    logger.info(f"数据维度检查:")
    logger.info(f"  原始数据形状: {original_train_data.shape}")
    logger.info(f"  生成数据形状: {generated_data.shape}")

    # 如果生成的数据为空，直接使用原始数据
    if len(generated_data) == 0:
        logger.warning("生成数据为空，使用原始数据进行训练")
        augmented_train_data = original_train_data
        augmented_train_labels = original_train_labels
    else:
        # 确保数据维度一致
        if generated_data.ndim == 1 and len(generated_data) > 0:
            # 如果生成数据是1维，需要重新整形
            logger.warning("生成数据维度异常，尝试修复...")
            generated_data = generated_data.reshape(-1, original_train_data.shape[1])
            logger.info(f"  修复后生成数据形状: {generated_data.shape}")

        # 合并数据
        augmented_train_data = np.concatenate([original_train_data, generated_data], axis=0)
        augmented_train_labels = np.concatenate([original_train_labels, generated_labels], axis=0)

    logger.info(f"原始训练数据: {len(original_train_data)} 样本")
    logger.info(f"生成数据: {len(generated_data)} 样本")
    logger.info(f"增强后训练数据: {len(augmented_train_data)} 样本")

    # 分析数据分布
    unique_original, counts_original = np.unique(original_train_labels, return_counts=True)
    unique_generated, counts_generated = np.unique(generated_labels, return_counts=True)
    unique_augmented, counts_augmented = np.unique(augmented_train_labels, return_counts=True)

    logger.info("数据分布分析:")
    logger.info(f"  原始数据类别分布: {dict(zip(unique_original, counts_original))}")
    logger.info(f"  生成数据类别分布: {dict(zip(unique_generated, counts_generated))}")
    logger.info(f"  增强数据类别分布: {dict(zip(unique_augmented, counts_augmented))}")

    # 获取完整的原始训练数据（用于健康样本平衡调整）
    full_train_data = []
    full_train_labels = []
    for data, labels in train_loader:
        full_train_data.append(data.numpy())
        full_train_labels.append(labels.numpy())
    full_train_data = np.concatenate(full_train_data, axis=0)
    full_train_labels = np.concatenate(full_train_labels, axis=0)

    # 动态调整健康样本数量以保持平衡
    augmented_train_data, augmented_train_labels = adjust_healthy_samples_for_balance(
        augmented_train_data, augmented_train_labels, full_train_data, full_train_labels, config
    )

    # 检查数据质量
    logger.info("数据质量检查:")
    logger.info(f"  原始数据形状: {original_train_data.shape}")
    logger.info(f"  生成数据形状: {generated_data.shape}")
    logger.info(f"  原始数据范围: [{original_train_data.min():.4f}, {original_train_data.max():.4f}]")
    if len(generated_data) > 0:
        logger.info(f"  生成数据范围: [{generated_data.min():.4f}, {generated_data.max():.4f}]")
    else:
        logger.info(f"  生成数据范围: [无数据]")

    # 创建增强数据集
    augmented_dataset = torch.utils.data.TensorDataset(
        torch.FloatTensor(augmented_train_data),
        torch.LongTensor(augmented_train_labels)
    )

    # 分割增强数据为训练和验证
    aug_train_size = int(train_val_split * len(augmented_dataset))
    aug_val_size = len(augmented_dataset) - aug_train_size

    aug_train_subset, aug_val_subset = torch.utils.data.random_split(
        augmented_dataset, [aug_train_size, aug_val_size],
        generator=torch.Generator().manual_seed(config['system']['seed'])
    )

    # 创建数据加载器
    aug_train_loader = DataLoader(
        aug_train_subset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    aug_val_loader = DataLoader(
        aug_val_subset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    # 训练分类器
    classifier_trainer = ClassifierTrainer(config, device)
    classifier_results = classifier_trainer.train(aug_train_loader, aug_val_loader)

    # 可视化分类器训练曲线
    visualizer.plot_training_curves(
        classifier_results['train_losses'],
        classifier_results['val_losses'],
        classifier_results['train_accs'],
        classifier_results['val_accs'],
        title="分类器训练曲线（增强数据）",
        save_name="classifier_training_curves.png"
    )

    # 评估分类器性能
    logger.info("=" * 50)
    logger.info("评估分类器性能")
    logger.info("=" * 50)

    # 在测试集上评估
    test_loader_eval = DataLoader(
        test_loader.dataset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers'],
        pin_memory=config['system']['pin_memory']
    )

    evaluation_results = classifier_trainer.evaluate(test_loader_eval)

    # 打印分类指标
    metrics = evaluation_results['metrics']
    logger.info("分类性能指标:")
    logger.info(f"  准确率 (Accuracy): {metrics['accuracy']:.4f}")
    logger.info(f"  精确率 (Precision): {metrics['precision']:.4f}")
    logger.info(f"  召回率 (Recall): {metrics['recall']:.4f}")
    logger.info(f"  F1分数 (F1-Score): {metrics['f1_score']:.4f}")

    # 可视化混淆矩阵
    visualizer.plot_confusion_matrix(
        evaluation_results['confusion_matrix'],
        class_names,
        title="混淆矩阵（增强数据训练）",
        save_name="confusion_matrix_augmented.png"
    )

    # 计算GAN-train和GAN-test指标（如果配置启用）
    gan_results = None
    if config['evaluation']['metrics']['generation']:
        logger.info("=" * 50)
        logger.info("计算GAN-train和GAN-test指标")
        logger.info("=" * 50)

        try:
            from models import MRCNN
            gan_evaluator = GANEvaluator(MRCNN, config)

            # 转换数据为tensor格式
            gen_data_tensor = torch.FloatTensor(generated_data)
            gen_labels_tensor = torch.LongTensor(generated_labels)
            train_data_tensor = torch.FloatTensor(original_train_data)
            train_labels_tensor = torch.LongTensor(original_train_labels)

            # 获取测试数据
            test_data = []
            test_labels = []
            for data, labels in test_loader_eval:
                test_data.append(data)
                test_labels.append(labels)
            test_data_tensor = torch.cat(test_data, dim=0)
            test_labels_tensor = torch.cat(test_labels, dim=0)

            # 计算GAN指标
            gan_train_score = gan_evaluator.calculate_gan_train(
                gen_data_tensor, gen_labels_tensor,
                test_data_tensor, test_labels_tensor
            )

            gan_test_score = gan_evaluator.calculate_gan_test(
                train_data_tensor, train_labels_tensor,
                gen_data_tensor, gen_labels_tensor
            )

            gan_results = {
                'gan_train': gan_train_score,
                'gan_test': gan_test_score
            }

            logger.info("GAN评估指标:")
            logger.info(f"  GAN-train: {gan_train_score:.4f}")
            logger.info(f"  GAN-test: {gan_test_score:.4f}")

        except Exception as e:
            logger.warning(f"GAN评估失败: {e}")
            gan_results = {'error': str(e)}

    # 性能对比总结
    logger.info("=" * 50)
    logger.info("性能对比总结")
    logger.info("=" * 50)

    augmented_metrics = evaluation_results['metrics']
    improvement = {
        'accuracy': augmented_metrics['accuracy'] - baseline_metrics['accuracy'],
        'precision': augmented_metrics['precision'] - baseline_metrics['precision'],
        'recall': augmented_metrics['recall'] - baseline_metrics['recall'],
        'f1_score': augmented_metrics['f1_score'] - baseline_metrics['f1_score']
    }

    logger.info("性能对比 (增强 vs 基线):")
    logger.info(f"  准确率: {baseline_metrics['accuracy']:.4f} → {augmented_metrics['accuracy']:.4f} (Δ{improvement['accuracy']:+.4f})")
    logger.info(f"  精确率: {baseline_metrics['precision']:.4f} → {augmented_metrics['precision']:.4f} (Δ{improvement['precision']:+.4f})")
    logger.info(f"  召回率: {baseline_metrics['recall']:.4f} → {augmented_metrics['recall']:.4f} (Δ{improvement['recall']:+.4f})")
    logger.info(f"  F1分数: {baseline_metrics['f1_score']:.4f} → {augmented_metrics['f1_score']:.4f} (Δ{improvement['f1_score']:+.4f})")

    # 实验结束后打印参数组合信息
    logger.info("🏁 实验结束")
    print_experiment_config(config)

    return {
        'augmentation_results': augmentation_results,
        'baseline_results': baseline_results,
        'baseline_evaluation': baseline_eval,
        'classifier_results': classifier_results,
        'evaluation_results': evaluation_results,
        'gan_results': gan_results,
        'performance_comparison': {
            'baseline_metrics': baseline_metrics,
            'augmented_metrics': augmented_metrics,
            'improvement': improvement
        },
        'generated_data': generated_data,
        'generated_labels': generated_labels,
        'data_info': data_info,
        'augmented_data_info': {
            'original_samples': len(original_train_data),
            'generated_samples': len(generated_data),
            'total_samples': len(augmented_train_data)
        }
    }


def run_comparison_experiments(base_config_path: str):
    """运行对比实验（智能重用扩散模型）"""
    from common.experiment_manager import ExperimentManager
    from common.results_manager import ResultsManager

    # 加载基础配置
    base_config = load_config(base_config_path)
    base_config['_config_path'] = base_config_path

    # 应用性能配置
    base_config = apply_performance_config(base_config)

    # 创建实验管理器
    experiment_manager = ExperimentManager(base_config)

    # 检测对比参数
    comparison_params = experiment_manager.detect_comparison_parameters()

    if not comparison_params:
        # 没有多参数，运行单一实验
        return run_single_experiment(base_config)

    # 生成所有实验配置
    experiment_configs = experiment_manager.generate_experiment_configs()

    # 获取数据集名称和时间戳
    dataset_name = experiment_configs[0]['dataset']['name']
    timestamp = experiment_manager.timestamp

    # 初始化结果管理器
    results_manager = ResultsManager(base_config, dataset_name, timestamp)

    # 设置日志
    experiment_name = base_config['experiment']['name']
    experiment_name_with_time = f"{experiment_name}_comparison_{timestamp}"

    setup_logging(base_config['system']['save']['logs_dir'], experiment_name_with_time)

    # 检查是否可以重用扩散模型
    can_reuse_diffusion = experiment_manager.should_reuse_diffusion_model(comparison_params)

    logger.info("=" * 80)
    logger.info(f"检测到对比实验配置")
    logger.info(f"实验名称: {experiment_name}")
    logger.info(f"数据集: {dataset_name}")
    logger.info(f"总实验数: {len(experiment_configs)}")
    logger.info(f"对比参数: {list(comparison_params.keys())}")
    logger.info(f"🔥 扩散模型重用: {'是' if can_reuse_diffusion else '否'}")
    logger.info("=" * 80)

    # 创建缓存目录（保留不删除）
    cache_dir = os.path.join("cache", timestamp)
    os.makedirs(cache_dir, exist_ok=True)
    logger.info(f"缓存配置文件保存在: {cache_dir}")

    # 复制原始配置文件
    results_manager.copy_config_to_results(base_config_path)

    # 运行所有实验
    total_timer = Timer()
    total_timer.start()

    if can_reuse_diffusion:
        # 智能重用扩散模型的实验流程
        return run_experiments_with_diffusion_reuse(
            experiment_configs, experiment_manager, results_manager, cache_dir, total_timer
        )
    else:
        # 传统的独立实验流程
        return run_experiments_independently(
            experiment_configs, experiment_manager, results_manager, cache_dir, total_timer
        )


def run_experiments_with_diffusion_reuse(experiment_configs, experiment_manager, results_manager, cache_dir, total_timer):
    """运行实验并重用扩散模型"""
    logger.info("🔥 启用扩散模型重用模式")

    # 按训练数据分组实验
    experiment_groups = experiment_manager.group_experiments_by_training_data()

    diffusion_model_cache = {}  # 缓存训练好的扩散模型

    for group_key, group_configs in experiment_groups.items():
        logger.info("=" * 70)
        logger.info(f"处理实验组: {group_key}")
        logger.info(f"该组包含 {len(group_configs)} 个实验")
        logger.info("=" * 70)

        # 使用组内第一个配置训练扩散模型
        base_config = group_configs[0]

        # 训练扩散模型（只训练一次）
        logger.info("🚀 训练扩散模型（该组唯一一次）...")
        diffusion_model_path = train_diffusion_model_once(base_config)
        diffusion_model_cache[group_key] = diffusion_model_path

        # 对该组的所有实验重用扩散模型
        for i, config in enumerate(group_configs):
            try:
                exp_info = config['experiment']['current_experiment']
                exp_index = exp_info['index']

                logger.info("=" * 60)
                logger.info(f"开始实验 {exp_index}/{len(experiment_configs)} (组内 {i+1}/{len(group_configs)})")
                logger.info(f"实验参数: {exp_info['parameters']}")
                logger.info(f"🔄 重用扩散模型: {diffusion_model_path}")
                logger.info("=" * 60)

                # 保存缓存配置文件
                cache_config_path = os.path.join(cache_dir, f"experiment_{exp_index:03d}_config.yaml")
                experiment_manager._save_yaml_with_comments(config, cache_config_path)
                logger.info(f"缓存配置已保存: {cache_config_path}")

                # 运行实验（重用扩散模型）
                results = run_experiment_with_pretrained_diffusion(config, diffusion_model_path)

                # 保存单次实验结果
                results_manager.save_individual_experiment_results(exp_index, config, results)

                # 添加到汇总结果
                experiment_manager.add_experiment_result(config, results)

                logger.info(f"实验 {exp_index} 完成")

            except Exception as e:
                logger.error(f"实验 {exp_index} 失败: {str(e)}", exc_info=True)
                continue

    total_timer.stop()

    # 保存对比实验汇总
    experiment_manager.save_comparison_results(results_manager.comparison_results_dir)
    results_manager.save_comparison_summary(experiment_manager.results_summary)

    logger.info("=" * 80)
    logger.info(f"🎉 智能重用对比实验全部完成，总用时: {total_timer.elapsed_str()}")
    logger.info(f"💾 扩散模型重用次数: {sum(len(configs) for configs in experiment_groups.values()) - len(experiment_groups)}")
    logger.info(f"⏱️ 预计节省时间: 约 {(len(experiment_groups) - 1) * 0.8:.1f} 倍扩散模型训练时间")
    logger.info(f"结果保存在: {results_manager.experiment_results_dir}")
    logger.info(f"缓存配置保存在: {cache_dir}")
    logger.info("=" * 80)

    return {
        'comparison_results': experiment_manager.results_summary,
        'results_directory': results_manager.experiment_results_dir,
        'cache_directory': cache_dir,
        'total_experiments': len(experiment_configs),
        'total_time': total_timer.elapsed(),
        'diffusion_reuse_enabled': True,
        'diffusion_models_trained': len(experiment_groups),
        'diffusion_models_reused': sum(len(configs) for configs in experiment_groups.values()) - len(experiment_groups)
    }


def run_experiments_independently(experiment_configs, experiment_manager, results_manager, cache_dir, total_timer):
    """运行独立实验（传统模式）"""
    logger.info("🔧 使用传统独立实验模式")

    for _, config in enumerate(experiment_configs):
        try:
            exp_info = config['experiment']['current_experiment']
            exp_index = exp_info['index']

            logger.info("=" * 60)
            logger.info(f"开始实验 {exp_index}/{len(experiment_configs)}")
            logger.info(f"实验参数: {exp_info['parameters']}")
            logger.info("=" * 60)

            # 保存缓存配置文件
            cache_config_path = os.path.join(cache_dir, f"experiment_{exp_index:03d}_config.yaml")
            experiment_manager._save_yaml_with_comments(config, cache_config_path)
            logger.info(f"缓存配置已保存: {cache_config_path}")

            # 运行单个实验
            results = run_experiment(config)

            # 保存单次实验结果
            results_manager.save_individual_experiment_results(exp_index, config, results)

            # 添加到汇总结果
            experiment_manager.add_experiment_result(config, results)

            logger.info(f"实验 {exp_index} 完成")

        except Exception as e:
            logger.error(f"实验 {exp_index} 失败: {str(e)}", exc_info=True)
            continue

    total_timer.stop()

    # 保存对比实验汇总
    experiment_manager.save_comparison_results(results_manager.comparison_results_dir)
    results_manager.save_comparison_summary(experiment_manager.results_summary)

    logger.info("=" * 80)
    logger.info(f"对比实验全部完成，总用时: {total_timer.elapsed_str()}")
    logger.info(f"结果保存在: {results_manager.experiment_results_dir}")
    logger.info(f"缓存配置保存在: {cache_dir}")
    logger.info("=" * 80)

    return {
        'comparison_results': experiment_manager.results_summary,
        'results_directory': results_manager.experiment_results_dir,
        'cache_directory': cache_dir,
        'total_experiments': len(experiment_configs),
        'total_time': total_timer.elapsed(),
        'diffusion_reuse_enabled': False
    }


def run_single_experiment(config: Dict):
    """运行单一实验"""
    # 设置日志
    experiment_name = config['experiment']['name']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name_with_time = f"{experiment_name}_{timestamp}"

    setup_logging(config['system']['save']['logs_dir'], experiment_name_with_time)

    logger.info("=" * 80)
    logger.info(f"开始单一实验: {experiment_name}")
    logger.info(f"数据集: {config['dataset']['name']}")
    logger.info("=" * 80)

    # 运行实验
    results = run_experiment(config)

    # 保存实验结果
    results_path = os.path.join(
        config['system']['save']['results_dir'],
        f"{experiment_name_with_time}_results.json"
    )
    save_results(results, results_path)

    logger.info(f"实验完成，结果已保存: {results_path}")
    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='一维振动信号故障诊断数据增强')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='配置文件路径')
    parser.add_argument('--mode', type=str, default='full',
                       choices=['full', 'train_diffusion', 'train_classifier', 'generate', 'evaluate'],
                       help='运行模式')
    parser.add_argument('--checkpoint', type=str, default=None,
                       help='检查点路径')

    args = parser.parse_args()

    try:
        if args.mode == 'full':
            # 自动检测是否为对比实验
            results = run_comparison_experiments(args.config)
            logger.info("实验完成")



        elif args.mode == 'train_diffusion':
            # 只训练扩散模型
            logger.info("训练扩散模型模式")
            # 实现训练扩散模型的逻辑

        elif args.mode == 'train_classifier':
            # 只训练分类器
            logger.info("训练分类器模式")
            # 实现训练分类器的逻辑

        elif args.mode == 'generate':
            # 只生成样本
            logger.info("生成样本模式")
            # 实现生成样本的逻辑

        elif args.mode == 'evaluate':
            # 只评估模型
            logger.info("评估模式")
            # 实现评估的逻辑

    except Exception as e:
        logger.error(f"实验失败: {str(e)}", exc_info=True)
        raise

    logger.info("程序结束")


def train_diffusion_model_once(config: Dict) -> str:
    """训练扩散模型一次并返回模型路径"""
    # 设置随机种子
    set_seed(config['system']['seed'])

    # 获取设备
    device = get_device(config['system']['device'])

    # 加载数据
    data_loader = VibrationDataLoader(config)
    train_loader, _, _ = data_loader.load_data()

    # 分割训练集
    train_val_split = config['dataset']['data_loading']['train_val_split']
    train_dataset = train_loader.dataset
    train_size = int(train_val_split * len(train_dataset))
    val_size = len(train_dataset) - train_size

    train_dataset_split, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size]
    )

    train_loader_split = torch.utils.data.DataLoader(
        train_dataset_split,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers']
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers']
    )

    # 训练扩散模型
    diffusion_trainer = DiffusionTrainer(config, device)
    diffusion_results = diffusion_trainer.train(train_loader_split, val_loader)

    # 返回最佳模型路径
    best_diffusion_path = os.path.join(
        config['system']['save']['checkpoints_dir'],
        'diffusion', 'best', 'best_model.pth'
    )

    logger.info(f"✅ 扩散模型训练完成，保存至: {best_diffusion_path}")
    return best_diffusion_path


def run_experiment_with_pretrained_diffusion(config: Dict, diffusion_model_path: str):
    """使用预训练扩散模型运行实验"""
    # 设置随机种子
    set_seed(config['system']['seed'])

    # 获取设备
    device = get_device(config['system']['device'])

    # 加载数据
    data_loader = VibrationDataLoader(config)
    train_loader, test_loader, data_info = data_loader.load_data()

    # 分割训练集
    train_val_split = config['dataset']['data_loading']['train_val_split']
    train_dataset = train_loader.dataset
    train_size = int(train_val_split * len(train_dataset))
    val_size = len(train_dataset) - train_size

    train_dataset_split, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size]
    )

    train_loader_split = torch.utils.data.DataLoader(
        train_dataset_split,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers']
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=config['training']['diffusion']['batch_size'],
        shuffle=False,
        num_workers=config['system']['num_workers']
    )

    # 训练基线分类器
    logger.info("训练基线分类器...")
    baseline_trainer = ClassifierTrainer(config, device)
    baseline_results = baseline_trainer.train(train_loader_split, val_loader)

    # 生成样本（使用预训练扩散模型）
    logger.info(f"🔄 使用预训练扩散模型生成样本: {diffusion_model_path}")
    generator = DataGenerator(config, device)
    generator.load_diffusion_model(diffusion_model_path)

    # 检查是否启用数据筛选
    screening_enabled = config.get('data_screening', {}).get('enabled', False)

    if screening_enabled:
        # 使用筛选功能
        return run_experiment_with_screening_logic(
            config, device, generator, baseline_trainer,
            train_loader_split, val_loader, test_loader, data_info
        )
    else:
        # 传统生成方式
        return run_experiment_with_traditional_generation(
            config, device, generator, baseline_trainer,
            train_loader_split, val_loader, test_loader, data_info
        )


def run_experiment_with_screening_logic(config, device, generator, baseline_trainer,
                                       train_loader_split, val_loader, test_loader, data_info):
    """使用筛选功能的实验逻辑"""
    # 计算生成数量
    from common.adaptive_screening import AdaptiveDataGenerator
    adaptive_generator = AdaptiveDataGenerator(config)
    target_per_class = config['augmentation']['num_generated_per_class']
    generation_per_class = adaptive_generator.calculate_generation_count(target_per_class)
    signal_length = config['dataset']['data_loading']['signal_length']

    generated_data, generated_labels = generator.generate_samples(
        generation_per_class, signal_length
    )

    # 数据筛选
    logger.info("应用数据筛选...")
    from common.adaptive_screening import AdaptiveScreeningPipeline
    adaptive_screening_pipeline = AdaptiveScreeningPipeline(config)

    # 获取验证数据
    val_data_list = []
    val_labels_list = []
    for data, labels in val_loader:
        val_data_list.append(data)
        val_labels_list.append(labels)
    val_data_tensor = torch.cat(val_data_list, dim=0)
    val_labels_tensor = torch.cat(val_labels_list, dim=0)

    # 转换数据格式
    generated_data_tensor = torch.FloatTensor(generated_data)
    generated_labels_tensor = torch.LongTensor(generated_labels)

    # 应用筛选
    screened_data_tensor, screened_labels_tensor, screening_stats = adaptive_screening_pipeline.screen_to_target(
        baseline_trainer.model, generated_data_tensor, generated_labels_tensor,
        val_data_tensor, val_labels_tensor, target_per_class, device
    )

    # 转换回numpy
    screened_data = screened_data_tensor.numpy()
    screened_labels = screened_labels_tensor.numpy()

    # 合并数据并训练增强分类器
    logger.info("训练增强分类器...")
    original_train_data = []
    original_train_labels = []
    for data, labels in train_loader_split:
        original_train_data.append(data.numpy())
        original_train_labels.append(labels.numpy())

    original_train_data = np.concatenate(original_train_data, axis=0)
    original_train_labels = np.concatenate(original_train_labels, axis=0)

    # 合并数据
    augmented_train_data = np.concatenate([original_train_data, screened_data], axis=0)
    augmented_train_labels = np.concatenate([original_train_labels, screened_labels], axis=0)

    # 创建增强数据加载器
    augmented_dataset = torch.utils.data.TensorDataset(
        torch.FloatTensor(augmented_train_data),
        torch.LongTensor(augmented_train_labels)
    )
    augmented_loader = torch.utils.data.DataLoader(
        augmented_dataset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers']
    )

    # 训练增强分类器
    augmented_trainer = ClassifierTrainer(config, device)
    augmented_results = augmented_trainer.train(augmented_loader, val_loader)

    # 评估
    logger.info("评估模型性能...")
    from common.metrics import ClassificationMetrics
    metrics_calculator = ClassificationMetrics()

    # 基线评估
    baseline_metrics = metrics_calculator.evaluate_model(
        baseline_trainer.model, test_loader, device
    )

    # 增强评估
    augmented_metrics = metrics_calculator.evaluate_model(
        augmented_trainer.model, test_loader, device
    )

    # 计算改进
    improvement = {
        'accuracy': augmented_metrics['accuracy'] - baseline_metrics['accuracy'],
        'precision': augmented_metrics['precision'] - baseline_metrics['precision'],
        'recall': augmented_metrics['recall'] - baseline_metrics['recall'],
        'f1_score': augmented_metrics['f1_score'] - baseline_metrics['f1_score']
    }

    return {
        'baseline_results': None,  # baseline_trainer已经训练过，不需要重复训练结果
        'augmented_results': augmented_results,
        'baseline_metrics': baseline_metrics,
        'augmented_metrics': augmented_metrics,
        'improvement': improvement,
        'screening_stats': screening_stats,
        'data_info': {
            'original_samples': len(original_train_data),
            'generated_samples': len(generated_data),
            'screened_samples': len(screened_data),
            'total_samples': len(augmented_train_data)
        },
        'diffusion_reused': True
    }


def run_experiment_with_traditional_generation(config, device, generator, baseline_trainer,
                                              train_loader_split, val_loader, test_loader, data_info):
    """使用传统生成方式的实验逻辑"""
    # 获取原始训练数据
    original_train_data = []
    original_train_labels = []
    for data, labels in train_loader_split:
        original_train_data.append(data.numpy())
        original_train_labels.append(labels.numpy())

    original_train_data = np.concatenate(original_train_data, axis=0)
    original_train_labels = np.concatenate(original_train_labels, axis=0)

    # 生成样本
    num_generated_per_class = config['augmentation']['num_generated_per_class']
    signal_length = config['dataset']['data_loading']['signal_length']

    generated_data, generated_labels = generator.generate_samples(
        num_generated_per_class, signal_length
    )

    # 合并数据
    augmented_train_data = np.concatenate([original_train_data, generated_data], axis=0)
    augmented_train_labels = np.concatenate([original_train_labels, generated_labels], axis=0)

    # 创建增强数据加载器
    augmented_dataset = torch.utils.data.TensorDataset(
        torch.FloatTensor(augmented_train_data),
        torch.LongTensor(augmented_train_labels)
    )
    augmented_loader = torch.utils.data.DataLoader(
        augmented_dataset,
        batch_size=config['training']['classifier']['batch_size'],
        shuffle=True,
        num_workers=config['system']['num_workers']
    )

    # 训练增强分类器
    augmented_trainer = ClassifierTrainer(config, device)
    augmented_results = augmented_trainer.train(augmented_loader, val_loader)

    # 评估
    logger.info("评估模型性能...")
    from common.metrics import ClassificationMetrics
    metrics_calculator = ClassificationMetrics()

    # 基线评估
    baseline_metrics = metrics_calculator.evaluate_model(
        baseline_trainer.model, test_loader, device
    )

    # 增强评估
    augmented_metrics = metrics_calculator.evaluate_model(
        augmented_trainer.model, test_loader, device
    )

    # 计算改进
    improvement = {
        'accuracy': augmented_metrics['accuracy'] - baseline_metrics['accuracy'],
        'precision': augmented_metrics['precision'] - baseline_metrics['precision'],
        'recall': augmented_metrics['recall'] - baseline_metrics['recall'],
        'f1_score': augmented_metrics['f1_score'] - baseline_metrics['f1_score']
    }

    return {
        'baseline_results': None,  # baseline_trainer已经训练过，不需要重复训练结果
        'augmented_results': augmented_results,
        'baseline_metrics': baseline_metrics,
        'augmented_metrics': augmented_metrics,
        'improvement': improvement,
        'data_info': {
            'original_samples': len(original_train_data),
            'generated_samples': len(generated_data),
            'total_samples': len(augmented_train_data)
        },
        'diffusion_reused': True
    }





if __name__ == "__main__":
    main()
