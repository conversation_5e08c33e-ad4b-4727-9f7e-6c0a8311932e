2025-06-25 21:58:13,537 - common.utils - INFO - 日志系统初始化完成，日志文件: logs\cddpm_fault_diagnosis_comparison_20250625_215813_20250625_215813.log
2025-06-25 21:58:13,538 - common.experiment_manager - INFO - 检测到只有生成数量参数变化，将重用扩散模型
2025-06-25 21:58:13,538 - __main__ - INFO - ================================================================================
2025-06-25 21:58:13,538 - __main__ - INFO - 检测到对比实验配置
2025-06-25 21:58:13,539 - __main__ - INFO - 实验名称: cddpm_fault_diagnosis
2025-06-25 21:58:13,539 - __main__ - INFO - 数据集: KAT
2025-06-25 21:58:13,539 - __main__ - INFO - 总实验数: 2
2025-06-25 21:58:13,540 - __main__ - INFO - 对比参数: ['augmentation.num_generated_per_class']
2025-06-25 21:58:13,541 - __main__ - INFO - 🔥 扩散模型重用: 是
2025-06-25 21:58:13,541 - __main__ - INFO - ================================================================================
2025-06-25 21:58:13,547 - __main__ - INFO - 缓存配置文件保存在: cache\20250625_215813
2025-06-25 21:58:13,575 - common.results_manager - INFO - 原始配置文件已复制到结果目录: results\KAT\20250625_215813\configs\original_config.yaml
2025-06-25 21:58:13,575 - __main__ - INFO - 🔥 启用扩散模型重用模式
2025-06-25 21:58:13,576 - common.experiment_manager - INFO - 实验分组完成，共 1 个训练数据组
2025-06-25 21:58:13,577 - common.experiment_manager - INFO -   组 1: 2 个实验 - dataset=KAT_fault=4_healthy=0_length=1024
2025-06-25 21:58:13,577 - __main__ - INFO - ======================================================================
2025-06-25 21:58:13,578 - __main__ - INFO - 处理实验组: dataset=KAT_fault=4_healthy=0_length=1024
2025-06-25 21:58:13,578 - __main__ - INFO - 该组包含 2 个实验
2025-06-25 21:58:13,579 - __main__ - INFO - ======================================================================
2025-06-25 21:58:13,579 - __main__ - INFO - 🚀 训练扩散模型（该组唯一一次）...
2025-06-25 21:58:13,580 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 21:58:13,581 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 21:58:13,627 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:58:13,671 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:58:13,671 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:58:13,672 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:58:13,673 - common.data_loader - INFO - 样本配置: 故障样本每类最多4个, 健康样本最多0个
2025-06-25 21:58:13,673 - common.data_loader - INFO - 健康样本使用状态: 禁用
2025-06-25 21:58:13,674 - common.data_loader - INFO - 类别 0 (健康样本): 跳过，健康样本功能已禁用
2025-06-25 21:58:13,674 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,675 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,675 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,676 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,676 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,677 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 4 个
2025-06-25 21:58:13,677 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:13,682 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 21:58:13,699 - common.data_loader - INFO - 数据加载完成:
2025-06-25 21:58:13,700 - common.data_loader - INFO -   训练样本: 28
2025-06-25 21:58:13,700 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 21:58:13,701 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 21:58:13,701 - common.data_loader - INFO -   类别数: 8
2025-06-25 21:58:13,703 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 21:58:13,704 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 21:58:13,704 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 21:58:13,705 - models.cddpm - INFO -   类别数量: 8
2025-06-25 21:58:13,965 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 21:58:13,965 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 21:58:13,966 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 21:58:13,966 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 21:58:14,121 - __main__ - INFO - 扩散模型参数数量: 52,721,281
2025-06-25 21:58:14,122 - __main__ - INFO - 开始训练扩散模型...
2025-06-25 21:58:14,122 - __main__ - INFO - 最佳模型判断标准: weighted_loss (min)
2025-06-25 21:58:14,123 - __main__ - INFO - 加权损失配置: 训练损失权重=0.7, 验证损失权重=0.3
2025-06-25 21:58:26,984 - __main__ - INFO - Epoch   1/2 | Train Loss: 0.842392 | Val Loss: 0.875688 | Weighted Loss: 0.852381 | LR: 1.00e-04 | Time: 00:06 | Save: Best✓(weighted_loss=0.852381)
2025-06-25 21:58:27,481 - __main__ - INFO - Epoch   2/2 | Train Loss: 0.860277 | Val Loss: 0.844428 | Weighted Loss: 0.855522 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:27,483 - __main__ - INFO - 扩散模型训练完成，用时: 00:13
2025-06-25 21:58:27,483 - __main__ - INFO - 最佳验证损失: 0.844428
2025-06-25 21:58:27,484 - __main__ - INFO - ✅ 扩散模型训练完成，保存至: checkpoints\diffusion\best\best_model.pth
2025-06-25 21:58:27,485 - __main__ - INFO - ============================================================
2025-06-25 21:58:27,485 - __main__ - INFO - 开始实验 1/2 (组内 1/2)
2025-06-25 21:58:27,486 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 2}
2025-06-25 21:58:27,486 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\diffusion\best\best_model.pth
2025-06-25 21:58:27,487 - __main__ - INFO - ============================================================
2025-06-25 21:58:27,511 - __main__ - INFO - 缓存配置已保存: cache\20250625_215813\experiment_001_config.yaml
2025-06-25 21:58:27,512 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 21:58:27,512 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 21:58:27,553 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:58:27,592 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 21:58:27,592 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:58:27,593 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 21:58:27,593 - common.data_loader - INFO - 样本配置: 故障样本每类最多4个, 健康样本最多0个
2025-06-25 21:58:27,594 - common.data_loader - INFO - 健康样本使用状态: 禁用
2025-06-25 21:58:27,594 - common.data_loader - INFO - 类别 0 (健康样本): 跳过，健康样本功能已禁用
2025-06-25 21:58:27,595 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,595 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,595 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,596 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,596 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,597 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 4 个
2025-06-25 21:58:27,597 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 21:58:27,602 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 21:58:27,604 - common.data_loader - INFO - 数据加载完成:
2025-06-25 21:58:27,605 - common.data_loader - INFO -   训练样本: 28
2025-06-25 21:58:27,605 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 21:58:27,606 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 21:58:27,606 - common.data_loader - INFO -   类别数: 8
2025-06-25 21:58:27,607 - __main__ - INFO - 训练基线分类器...
2025-06-25 21:58:27,646 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-25 21:58:27,647 - __main__ - INFO - 开始训练分类器...
2025-06-25 21:58:27,879 - __main__ - INFO - Epoch   1/20 | Train Loss: 2.0854 Acc: 0.0526 | Val Loss: 2.0799 Acc: 0.0000 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,149 - __main__ - INFO - Epoch   2/20 | Train Loss: 2.0783 Acc: 0.1579 | Val Loss: 2.0802 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-25 21:58:28,215 - __main__ - INFO - Epoch   3/20 | Train Loss: 2.0652 Acc: 0.2105 | Val Loss: 2.0803 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,267 - __main__ - INFO - Epoch   4/20 | Train Loss: 2.0598 Acc: 0.2105 | Val Loss: 2.0799 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,317 - __main__ - INFO - Epoch   5/20 | Train Loss: 2.0519 Acc: 0.2632 | Val Loss: 2.0794 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,368 - __main__ - INFO - Epoch   6/20 | Train Loss: 2.0321 Acc: 0.3684 | Val Loss: 2.0787 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,419 - __main__ - INFO - Epoch   7/20 | Train Loss: 2.0262 Acc: 0.4737 | Val Loss: 2.0777 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,470 - __main__ - INFO - Epoch   8/20 | Train Loss: 2.0075 Acc: 0.4211 | Val Loss: 2.0765 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,521 - __main__ - INFO - Epoch   9/20 | Train Loss: 2.0064 Acc: 0.3684 | Val Loss: 2.0752 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,587 - __main__ - INFO - Epoch  10/20 | Train Loss: 2.0015 Acc: 0.2632 | Val Loss: 2.0738 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,641 - __main__ - INFO - Epoch  11/20 | Train Loss: 1.9879 Acc: 0.3158 | Val Loss: 2.0723 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,692 - __main__ - INFO - Epoch  12/20 | Train Loss: 1.9860 Acc: 0.3684 | Val Loss: 2.0706 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,759 - __main__ - INFO - Epoch  13/20 | Train Loss: 1.9662 Acc: 0.2632 | Val Loss: 2.0689 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,810 - __main__ - INFO - Epoch  14/20 | Train Loss: 1.9593 Acc: 0.3684 | Val Loss: 2.0671 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,867 - __main__ - INFO - Epoch  15/20 | Train Loss: 1.9472 Acc: 0.3158 | Val Loss: 2.0653 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,920 - __main__ - INFO - Epoch  16/20 | Train Loss: 1.9344 Acc: 0.2632 | Val Loss: 2.0635 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:28,987 - __main__ - INFO - Epoch  17/20 | Train Loss: 1.9287 Acc: 0.4211 | Val Loss: 2.0618 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:29,038 - __main__ - INFO - Epoch  18/20 | Train Loss: 1.9043 Acc: 0.3158 | Val Loss: 2.0601 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:29,097 - __main__ - INFO - Epoch  19/20 | Train Loss: 1.8876 Acc: 0.4211 | Val Loss: 2.0587 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:29,155 - __main__ - INFO - Epoch  20/20 | Train Loss: 1.8822 Acc: 0.3158 | Val Loss: 2.0574 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 21:58:29,156 - __main__ - INFO - 分类器训练完成，用时: 00:01
2025-06-25 21:58:29,157 - __main__ - INFO - 最佳验证准确率: 0.1111
2025-06-25 21:58:29,157 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\diffusion\best\best_model.pth
2025-06-25 21:58:29,158 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 21:58:29,158 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 21:58:29,159 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 21:58:29,159 - models.cddpm - INFO -   类别数量: 8
2025-06-25 21:58:29,324 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 21:58:29,325 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 21:58:29,325 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 21:58:29,326 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 21:58:31,634 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-25 21:58:31,636 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 2 个...
2025-06-25 21:58:31,636 - __main__ - INFO - 设置为只生成故障样本，健康样本（标签=0）将从原始数据加载
2025-06-25 21:58:31,675 - __main__ - INFO - 从原始数据加载了 2 个健康样本
2025-06-25 21:58:31,676 - __main__ - INFO - 跳过生成类别 0 (健康样本)，将使用原始数据
2025-06-25 21:58:31,676 - __main__ - INFO - 生成类别 1 的样本...
2025-06-25 21:58:48,593 - __main__ - INFO - 生成类别 2 的样本...
2025-06-25 21:59:04,251 - __main__ - INFO - 生成类别 3 的样本...
2025-06-25 21:59:20,404 - __main__ - INFO - 生成类别 4 的样本...
2025-06-25 21:59:35,932 - __main__ - INFO - 生成类别 5 的样本...
2025-06-25 21:59:51,729 - __main__ - INFO - 生成类别 6 的样本...
2025-06-25 22:00:07,781 - __main__ - INFO - 生成类别 7 的样本...
2025-06-25 22:00:23,655 - __main__ - INFO - 已将 2 个原始健康样本添加到生成数据中
2025-06-25 22:00:23,656 - __main__ - INFO - 样本生成完成，总共生成/使用 16 个样本
2025-06-25 22:00:23,691 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-25 22:00:23,692 - __main__ - INFO - 开始训练分类器...
2025-06-25 22:00:24,071 - __main__ - INFO - Epoch   1/20 | Train Loss: 2.0792 Acc: 0.1714 | Val Loss: 2.0784 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-25 22:00:24,204 - __main__ - INFO - Epoch   2/20 | Train Loss: 2.0632 Acc: 0.1429 | Val Loss: 2.0779 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,356 - __main__ - INFO - Epoch   3/20 | Train Loss: 2.0640 Acc: 0.1714 | Val Loss: 2.0772 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,474 - __main__ - INFO - Epoch   4/20 | Train Loss: 2.0600 Acc: 0.1429 | Val Loss: 2.0773 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,581 - __main__ - INFO - Epoch   5/20 | Train Loss: 2.0503 Acc: 0.1714 | Val Loss: 2.0772 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,734 - __main__ - INFO - Epoch   6/20 | Train Loss: 2.0422 Acc: 0.1714 | Val Loss: 2.0767 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,862 - __main__ - INFO - Epoch   7/20 | Train Loss: 2.0482 Acc: 0.1429 | Val Loss: 2.0763 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:24,989 - __main__ - INFO - Epoch   8/20 | Train Loss: 2.0387 Acc: 0.2000 | Val Loss: 2.0753 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,106 - __main__ - INFO - Epoch   9/20 | Train Loss: 2.0098 Acc: 0.2000 | Val Loss: 2.0736 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,221 - __main__ - INFO - Epoch  10/20 | Train Loss: 2.0127 Acc: 0.2000 | Val Loss: 2.0726 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,338 - __main__ - INFO - Epoch  11/20 | Train Loss: 2.1096 Acc: 0.2286 | Val Loss: 2.0723 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,490 - __main__ - INFO - Epoch  12/20 | Train Loss: 2.1304 Acc: 0.1714 | Val Loss: 2.0731 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,625 - __main__ - INFO - Epoch  13/20 | Train Loss: 2.0142 Acc: 0.2000 | Val Loss: 2.0742 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,763 - __main__ - INFO - Epoch  14/20 | Train Loss: 1.9934 Acc: 0.2000 | Val Loss: 2.0752 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:25,898 - __main__ - INFO - Epoch  15/20 | Train Loss: 2.0159 Acc: 0.2000 | Val Loss: 2.0762 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,037 - __main__ - INFO - Epoch  16/20 | Train Loss: 2.0265 Acc: 0.2000 | Val Loss: 2.0773 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,153 - __main__ - INFO - Epoch  17/20 | Train Loss: 1.9782 Acc: 0.2286 | Val Loss: 2.0788 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,279 - __main__ - INFO - Epoch  18/20 | Train Loss: 1.9933 Acc: 0.2000 | Val Loss: 2.0801 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,380 - __main__ - INFO - Epoch  19/20 | Train Loss: 1.9229 Acc: 0.2000 | Val Loss: 2.0821 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,497 - __main__ - INFO - Epoch  20/20 | Train Loss: 2.0846 Acc: 0.2286 | Val Loss: 2.0838 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:26,499 - __main__ - INFO - 分类器训练完成，用时: 00:02
2025-06-25 22:00:26,499 - __main__ - INFO - 最佳验证准确率: 0.1111
2025-06-25 22:00:26,500 - __main__ - INFO - 评估模型性能...
2025-06-25 22:00:26,500 - __main__ - ERROR - 实验 1 失败: 'ClassificationMetrics' object has no attribute 'evaluate_model'
Traceback (most recent call last):
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1627, in run_experiments_with_diffusion_reuse
    results = run_experiment_with_pretrained_diffusion(config, diffusion_model_path)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1906, in run_experiment_with_pretrained_diffusion
    return run_experiment_with_traditional_generation(
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 2072, in run_experiment_with_traditional_generation
    baseline_metrics = metrics_calculator.evaluate_model(
AttributeError: 'ClassificationMetrics' object has no attribute 'evaluate_model'
2025-06-25 22:00:26,504 - __main__ - INFO - ============================================================
2025-06-25 22:00:26,504 - __main__ - INFO - 开始实验 2/2 (组内 2/2)
2025-06-25 22:00:26,505 - __main__ - INFO - 实验参数: {'augmentation.num_generated_per_class': 3}
2025-06-25 22:00:26,505 - __main__ - INFO - 🔄 重用扩散模型: checkpoints\diffusion\best\best_model.pth
2025-06-25 22:00:26,506 - __main__ - INFO - ============================================================
2025-06-25 22:00:26,531 - __main__ - INFO - 缓存配置已保存: cache\20250625_215813\experiment_002_config.yaml
2025-06-25 22:00:26,531 - common.data_loader - INFO - 使用顺序采样数据: dataset/KAT/used_npy_sequential
2025-06-25 22:00:26,532 - common.data_loader - INFO - 样本选择方式: sequential
2025-06-25 22:00:26,573 - common.data_loader - INFO - 加载 train 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 22:00:26,616 - common.data_loader - INFO - 加载 test 数据 (sequential): (1001, 1024), 标签: (1001, 1)
2025-06-25 22:00:26,617 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 22:00:26,617 - common.data_loader - INFO - 顺序数据无需截取，当前长度: 1024
2025-06-25 22:00:26,618 - common.data_loader - INFO - 样本配置: 故障样本每类最多4个, 健康样本最多0个
2025-06-25 22:00:26,618 - common.data_loader - INFO - 健康样本使用状态: 禁用
2025-06-25 22:00:26,619 - common.data_loader - INFO - 类别 0 (健康样本): 跳过，健康样本功能已禁用
2025-06-25 22:00:26,619 - common.data_loader - INFO - 类别 1 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,620 - common.data_loader - INFO - 类别 2 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,620 - common.data_loader - INFO - 类别 3 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,621 - common.data_loader - INFO - 类别 4 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,621 - common.data_loader - INFO - 类别 5 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,622 - common.data_loader - INFO - 类别 6 (故障样本): 从 126 个样本中顺序选择前 4 个
2025-06-25 22:00:26,622 - common.data_loader - INFO - 类别 7 (故障样本): 从 125 个样本中顺序选择前 4 个
2025-06-25 22:00:26,627 - common.data_loader - INFO - 数据归一化完成，方法: minmax
2025-06-25 22:00:26,629 - common.data_loader - INFO - 数据加载完成:
2025-06-25 22:00:26,629 - common.data_loader - INFO -   训练样本: 28
2025-06-25 22:00:26,630 - common.data_loader - INFO -   测试样本: 1001
2025-06-25 22:00:26,630 - common.data_loader - INFO -   信号长度: 1024
2025-06-25 22:00:26,631 - common.data_loader - INFO -   类别数: 8
2025-06-25 22:00:26,632 - __main__ - INFO - 训练基线分类器...
2025-06-25 22:00:26,667 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-25 22:00:26,668 - __main__ - INFO - 开始训练分类器...
2025-06-25 22:00:26,731 - __main__ - INFO - Epoch   1/20 | Train Loss: 2.0854 Acc: 0.0526 | Val Loss: 2.0799 Acc: 0.0000 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,023 - __main__ - INFO - Epoch   2/20 | Train Loss: 2.0783 Acc: 0.1579 | Val Loss: 2.0802 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-25 22:00:27,077 - __main__ - INFO - Epoch   3/20 | Train Loss: 2.0652 Acc: 0.2105 | Val Loss: 2.0803 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,144 - __main__ - INFO - Epoch   4/20 | Train Loss: 2.0598 Acc: 0.2105 | Val Loss: 2.0799 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,199 - __main__ - INFO - Epoch   5/20 | Train Loss: 2.0519 Acc: 0.2632 | Val Loss: 2.0794 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,253 - __main__ - INFO - Epoch   6/20 | Train Loss: 2.0321 Acc: 0.3684 | Val Loss: 2.0787 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,321 - __main__ - INFO - Epoch   7/20 | Train Loss: 2.0262 Acc: 0.4737 | Val Loss: 2.0777 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,373 - __main__ - INFO - Epoch   8/20 | Train Loss: 2.0075 Acc: 0.4211 | Val Loss: 2.0765 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,427 - __main__ - INFO - Epoch   9/20 | Train Loss: 2.0064 Acc: 0.3684 | Val Loss: 2.0752 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,493 - __main__ - INFO - Epoch  10/20 | Train Loss: 2.0015 Acc: 0.2632 | Val Loss: 2.0738 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,545 - __main__ - INFO - Epoch  11/20 | Train Loss: 1.9879 Acc: 0.3158 | Val Loss: 2.0723 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,600 - __main__ - INFO - Epoch  12/20 | Train Loss: 1.9860 Acc: 0.3684 | Val Loss: 2.0706 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,653 - __main__ - INFO - Epoch  13/20 | Train Loss: 1.9662 Acc: 0.2632 | Val Loss: 2.0689 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,705 - __main__ - INFO - Epoch  14/20 | Train Loss: 1.9593 Acc: 0.3684 | Val Loss: 2.0671 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,759 - __main__ - INFO - Epoch  15/20 | Train Loss: 1.9472 Acc: 0.3158 | Val Loss: 2.0653 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,811 - __main__ - INFO - Epoch  16/20 | Train Loss: 1.9344 Acc: 0.2632 | Val Loss: 2.0635 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,863 - __main__ - INFO - Epoch  17/20 | Train Loss: 1.9287 Acc: 0.4211 | Val Loss: 2.0618 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,930 - __main__ - INFO - Epoch  18/20 | Train Loss: 1.9043 Acc: 0.3158 | Val Loss: 2.0601 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:27,981 - __main__ - INFO - Epoch  19/20 | Train Loss: 1.8876 Acc: 0.4211 | Val Loss: 2.0587 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:28,034 - __main__ - INFO - Epoch  20/20 | Train Loss: 1.8822 Acc: 0.3158 | Val Loss: 2.0574 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:00:28,036 - __main__ - INFO - 分类器训练完成，用时: 00:01
2025-06-25 22:00:28,036 - __main__ - INFO - 最佳验证准确率: 0.1111
2025-06-25 22:00:28,037 - __main__ - INFO - 🔄 使用预训练扩散模型生成样本: checkpoints\diffusion\best\best_model.pth
2025-06-25 22:00:28,037 - models.cddpm - INFO - Classifier-Free Guidance配置:
2025-06-25 22:00:28,038 - models.cddpm - INFO -   无条件训练概率: 0.1
2025-06-25 22:00:28,038 - models.cddpm - INFO -   引导强度: 1.0
2025-06-25 22:00:28,038 - models.cddpm - INFO -   类别数量: 8
2025-06-25 22:00:28,201 - models.cddpm - INFO - CDDPM初始化完成:
2025-06-25 22:00:28,202 - models.cddpm - INFO -   时间步数: 1000
2025-06-25 22:00:28,203 - models.cddpm - INFO -   噪声调度: linear
2025-06-25 22:00:28,203 - models.cddpm - INFO -   beta范围: [0.000100, 0.020000]
2025-06-25 22:00:30,795 - __main__ - INFO - 扩散模型已加载: checkpoints\diffusion\best\best_model.pth
2025-06-25 22:00:30,798 - __main__ - INFO - 开始使用扩散模型生成样本，每个类别 3 个...
2025-06-25 22:00:30,799 - __main__ - INFO - 设置为只生成故障样本，健康样本（标签=0）将从原始数据加载
2025-06-25 22:00:30,844 - __main__ - INFO - 从原始数据加载了 3 个健康样本
2025-06-25 22:00:30,845 - __main__ - INFO - 跳过生成类别 0 (健康样本)，将使用原始数据
2025-06-25 22:00:30,845 - __main__ - INFO - 生成类别 1 的样本...
2025-06-25 22:00:50,688 - __main__ - INFO - 生成类别 2 的样本...
2025-06-25 22:01:10,562 - __main__ - INFO - 生成类别 3 的样本...
2025-06-25 22:01:30,539 - __main__ - INFO - 生成类别 4 的样本...
2025-06-25 22:01:49,359 - __main__ - INFO - 生成类别 5 的样本...
2025-06-25 22:02:08,226 - __main__ - INFO - 生成类别 6 的样本...
2025-06-25 22:02:27,620 - __main__ - INFO - 生成类别 7 的样本...
2025-06-25 22:02:48,109 - __main__ - INFO - 已将 3 个原始健康样本添加到生成数据中
2025-06-25 22:02:48,110 - __main__ - INFO - 样本生成完成，总共生成/使用 24 个样本
2025-06-25 22:02:48,138 - __main__ - INFO - 分类器参数数量: 2,399,496
2025-06-25 22:02:48,138 - __main__ - INFO - 开始训练分类器...
2025-06-25 22:02:48,493 - __main__ - INFO - Epoch   1/20 | Train Loss: 2.0759 Acc: 0.1628 | Val Loss: 2.0783 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00 | Save: Best✓
2025-06-25 22:02:48,609 - __main__ - INFO - Epoch   2/20 | Train Loss: 2.0613 Acc: 0.1860 | Val Loss: 2.0777 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:48,729 - __main__ - INFO - Epoch   3/20 | Train Loss: 2.0650 Acc: 0.2558 | Val Loss: 2.0779 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:48,854 - __main__ - INFO - Epoch   4/20 | Train Loss: 2.0476 Acc: 0.1395 | Val Loss: 2.0787 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:48,955 - __main__ - INFO - Epoch   5/20 | Train Loss: 2.0229 Acc: 0.3256 | Val Loss: 2.0791 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,071 - __main__ - INFO - Epoch   6/20 | Train Loss: 2.0414 Acc: 0.1860 | Val Loss: 2.0796 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,176 - __main__ - INFO - Epoch   7/20 | Train Loss: 2.0203 Acc: 0.2093 | Val Loss: 2.0799 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,277 - __main__ - INFO - Epoch   8/20 | Train Loss: 2.0082 Acc: 0.2558 | Val Loss: 2.0804 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,424 - __main__ - INFO - Epoch   9/20 | Train Loss: 2.0288 Acc: 0.1860 | Val Loss: 2.0810 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,528 - __main__ - INFO - Epoch  10/20 | Train Loss: 1.9652 Acc: 0.2558 | Val Loss: 2.0817 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,630 - __main__ - INFO - Epoch  11/20 | Train Loss: 1.9559 Acc: 0.2326 | Val Loss: 2.0826 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,739 - __main__ - INFO - Epoch  12/20 | Train Loss: 1.9484 Acc: 0.2326 | Val Loss: 2.0833 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,844 - __main__ - INFO - Epoch  13/20 | Train Loss: 1.9417 Acc: 0.2326 | Val Loss: 2.0838 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:49,989 - __main__ - INFO - Epoch  14/20 | Train Loss: 1.9835 Acc: 0.3256 | Val Loss: 2.0841 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,105 - __main__ - INFO - Epoch  15/20 | Train Loss: 1.8900 Acc: 0.2791 | Val Loss: 2.0847 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,234 - __main__ - INFO - Epoch  16/20 | Train Loss: 1.8834 Acc: 0.2558 | Val Loss: 2.0851 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,345 - __main__ - INFO - Epoch  17/20 | Train Loss: 1.8727 Acc: 0.1860 | Val Loss: 2.0847 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,451 - __main__ - INFO - Epoch  18/20 | Train Loss: 1.8354 Acc: 0.2791 | Val Loss: 2.0848 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,556 - __main__ - INFO - Epoch  19/20 | Train Loss: 1.8262 Acc: 0.2558 | Val Loss: 2.0852 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,704 - __main__ - INFO - Epoch  20/20 | Train Loss: 1.8137 Acc: 0.1628 | Val Loss: 2.0858 Acc: 0.1111 | LR: 1.00e-04 | Time: 00:00
2025-06-25 22:02:50,705 - __main__ - INFO - 分类器训练完成，用时: 00:02
2025-06-25 22:02:50,705 - __main__ - INFO - 最佳验证准确率: 0.1111
2025-06-25 22:02:50,706 - __main__ - INFO - 评估模型性能...
2025-06-25 22:02:50,706 - __main__ - ERROR - 实验 2 失败: 'ClassificationMetrics' object has no attribute 'evaluate_model'
Traceback (most recent call last):
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1627, in run_experiments_with_diffusion_reuse
    results = run_experiment_with_pretrained_diffusion(config, diffusion_model_path)
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 1906, in run_experiment_with_pretrained_diffusion
    return run_experiment_with_traditional_generation(
  File "\\wsl.localhost\Ubuntu-24.04\opt\tasks\250610_code\main.py", line 2072, in run_experiment_with_traditional_generation
    baseline_metrics = metrics_calculator.evaluate_model(
AttributeError: 'ClassificationMetrics' object has no attribute 'evaluate_model'
2025-06-25 22:02:50,710 - common.results_manager - WARNING - 没有结果数据可保存
2025-06-25 22:02:50,710 - __main__ - INFO - ================================================================================
2025-06-25 22:02:50,711 - __main__ - INFO - 🎉 智能重用对比实验全部完成，总用时: 04:37
2025-06-25 22:02:50,711 - __main__ - INFO - 💾 扩散模型重用次数: 1
2025-06-25 22:02:50,712 - __main__ - INFO - ⏱️ 预计节省时间: 约 0.0 倍扩散模型训练时间
2025-06-25 22:02:50,712 - __main__ - INFO - 结果保存在: results\KAT\20250625_215813
2025-06-25 22:02:50,713 - __main__ - INFO - 缓存配置保存在: cache\20250625_215813
2025-06-25 22:02:50,713 - __main__ - INFO - ================================================================================
2025-06-25 22:02:50,714 - __main__ - INFO - 实验完成
2025-06-25 22:02:50,714 - __main__ - INFO - 程序结束
