"""
<PERSON>, <PERSON>, <PERSON>, et al. Multiscale kernel based residual convolutional neural network for motor fault diagnosis under nonstationary conditions[J]. IEEE Transactions on Industrial Informatics, 2019, 16(6): 3797-3806.

@article{liu2019multiscale,
  title={Multiscale kernel based residual convolutional neural network for motor fault diagnosis under nonstationary conditions},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, S Joe},
  journal={IEEE Transactions on Industrial Informatics},
  volume={16},
  number={6},
  pages={3797--3806},
  year={2019},
  publisher={IEEE}
}
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class ResidualConvBlock(nn.Module):
    """残差卷积块，包含两个卷积层和残差连接"""
    def __init__(self, in_channels, out_channels, kernel_size, stride=1):
        super(ResidualConvBlock, self).__init__()
        
        # 计算padding保持输出大小
        padding = kernel_size // 2
        
        # 主线路
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, 
                               stride=stride, padding=padding)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.relu1 = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, 
                               stride=stride, padding=padding)
        self.bn2 = nn.BatchNorm1d(out_channels)
        
        # 残差连接（如果输入输出通道数不同，则使用1x1卷积匹配）
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv1d(in_channels, out_channels, kernel_size=1, 
                          stride=stride, padding=0),
                nn.BatchNorm1d(out_channels)
            )
        
        self.relu2 = nn.ReLU(inplace=True)
    
    def forward(self, x):
        residual = self.shortcut(x)
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu1(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        out += residual
        out = self.relu2(out)
        
        return out

class MRCNN(nn.Module):
    """
    多尺度核残差卷积神经网络 (MR-CNN)
    
    一种基于多尺度卷积核和残差结构的神经网络，用于非平稳条件下的电机故障诊断。
    
    特点:
    1. 采用多个并行的残差分支，使用不同尺寸的卷积核
    2. 利用残差连接改善梯度流动和模型训练
    3. 针对非平稳条件下的故障诊断具有较强的鲁棒性
    4. 完全自适应输入长度，无需指定固定的输入长度
    """
    def __init__(self, config=None, num_classes=10, input_channels=1):
        super(MRCNN, self).__init__()
        
        # 从配置中获取参数
        if config is not None:
            try:
                # 获取数据集名称和类别数
                dataset_name = config['dataset']['name']
                num_classes = config['dataset']['datasets'][dataset_name]['num_classes']
                
                # 获取模型特定参数（如果有）
                if 'MRCNN' in config['models']:
                    model_config = config['models']['MRCNN']
                    kernel_sizes = model_config.get('kernel_sizes', [3, 5, 7])
                    base_channels = model_config.get('base_channels', 64)
                    num_layers = model_config.get('num_layers', 3)
                    use_batch_norm = model_config.get('use_batch_norm', True)
                elif 'classifier' in config['models']:
                    # 从分类器配置中获取参数
                    classifier_config = config['models']['classifier']
                    kernel_sizes = classifier_config.get('kernel_sizes', [3, 5, 7])
                    base_channels = classifier_config.get('base_channels', 64)
                    num_layers = 3
                    use_batch_norm = True
                else:
                    kernel_sizes = [3, 5, 7]  # 默认卷积核大小
                    base_channels = 64  # 默认基础通道数
                    num_layers = 3
                    use_batch_norm = True
            except KeyError as e:
                print(f"配置字典缺少键: {str(e)}")
                print("使用默认参数初始化MRCNN")
                kernel_sizes = [3, 5, 7]
                base_channels = 64
        else:
            kernel_sizes = [3, 5, 7, 9]
            base_channels = 128

        self.kernel_sizes = kernel_sizes
        self.base_channels = base_channels
        self.num_branches = len(kernel_sizes)

        print(f"MRCNN配置: 类别数={num_classes}, 输入通道={input_channels}")
        print(f"卷积核大小: {kernel_sizes}, 基础通道数: {base_channels}")
        print(f"分支数量: {self.num_branches}")

        # 定义公共的初始卷积层
        self.init_conv = nn.Sequential(
            nn.Conv1d(input_channels, base_channels, kernel_size=7, stride=1, padding=3),
            nn.BatchNorm1d(base_channels),
            nn.ReLU(inplace=True),
            nn.MaxPool1d(kernel_size=2, stride=2)
        )

        # 动态创建多个并行的残差卷积分支
        self.branches = nn.ModuleList()
        for i, kernel_size in enumerate(kernel_sizes):
            branch = nn.Sequential(
                ResidualConvBlock(base_channels, base_channels, kernel_size=kernel_size),
                ResidualConvBlock(base_channels, base_channels*2, kernel_size=kernel_size),
                ResidualConvBlock(base_channels*2, base_channels*4, kernel_size=kernel_size),
                nn.AdaptiveAvgPool1d(1)
            )
            self.branches.append(branch)

        # 分类器 - 特征维度为 base_channels*4*num_branches
        feature_dim = base_channels * 4 * self.num_branches
        hidden_dim = feature_dim // 2

        # 获取dropout率
        dropout_rate = 0.2  # 默认值
        if 'classifier' in config['models']:
            dropout_rate = config['models']['classifier'].get('dropout', 0.2)
        elif 'MRCNN' in config['models']:
            dropout_rate = config['models']['MRCNN'].get('dropout', 0.2)

        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, num_classes)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 确保输入格式正确，自动适应任何长度的输入
        if x.dim() != 3:
            x = x.unsqueeze(1)  # [batch_size, 1, length]

        # 公共特征提取
        x = self.init_conv(x)

        # 多尺度残差分支 - 动态处理所有分支
        branch_outputs = []
        for branch in self.branches:
            branch_out = branch(x).squeeze(-1)
            branch_outputs.append(branch_out)

        # 合并特征
        out = torch.cat(branch_outputs, dim=1)

        # 分类
        out = self.classifier(out)

        return out

    def features(self, x):
        """
        提取特征向量（不包括分类器部分）

        Args:
            x: 输入信号，形状为 [batch_size, 1, signal_length]

        Returns:
            features: 特征向量，形状为 [batch_size, feature_dim]
        """
        # 确保输入格式正确，自动适应任何长度的输入
        if x.dim() != 3:
            x = x.unsqueeze(1)  # [batch_size, 1, length]

        # 公共特征提取
        x = self.init_conv(x)

        # 多尺度残差分支 - 动态处理所有分支
        branch_outputs = []
        for branch in self.branches:
            branch_out = branch(x).squeeze(-1)
            branch_outputs.append(branch_out)

        # 合并特征
        out = torch.cat(branch_outputs, dim=1)

        return out